{"name": "@vant/popperjs", "version": "1.3.0", "description": "Pre-compiled popperjs core", "main": "dist/index.cjs.js", "module": "dist/index.esm.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.esm.mjs", "require": "./dist/index.cjs.js"}}, "sideEffects": false, "files": ["dist"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "scripts": {"clean": "rimraf ./dist", "dev": "node ./build.js -w", "build:types": "tsc -p ./tsconfig.json --emitDeclarationOnly", "build:bundle": "node ./build.js", "build": "pnpm clean && pnpm build:bundle && pnpm build:types", "release": "pnpm build && release-it", "prepare": "pnpm build"}, "repository": {"type": "git", "url": "https://github.com/vant-ui/vant.git", "directory": "packages/vant-popperjs"}, "bugs": "https://github.com/vant-ui/vant/issues", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"@popperjs/core": "^2.11.6", "esbuild": "^0.14.54", "release-it": "^15.4.1", "typescript": "^4.8.2"}, "release-it": {"git": {"tag": false, "commitMessage": "release: @vant/popperjs ${version}"}}}