import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { login, getUserInfo, logout, register, updateUserInfo, changePassword } from '@/api/user'
import type { LoginParams, RegisterParams, UserInfo, TokenResponse } from '@/api/types'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '')
  const userInfo = ref<UserInfo | null>(null)
  const loading = ref<boolean>(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)

  // 设置token
  const setToken = (newToken: string) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }

  // 清除token
  const clearToken = () => {
    token.value = ''
    localStorage.removeItem('token')
  }

  // 设置用户信息
  const setUserInfo = (info: UserInfo) => {
    userInfo.value = info
  }

  // 用户登录
  const userLogin = async (loginForm: LoginParams) => {
    try {
      loading.value = true
      // 使用FormData格式发送登录请求，符合OAuth2标准
      const formData = new FormData()
      formData.append('username', loginForm.username)
      formData.append('password', loginForm.password)
      
      console.log('准备调用登录API...');
      try {
        // 将 AxiosResponse 转换为 unknown 再转为 TokenResponse
        const response = await login(formData)
        const res = response.data as unknown as TokenResponse
        console.log('登录API响应:', res);
        
        // 适配OAuth2响应格式
        if (res && res.access_token) {
          console.log('成功获取access_token，准备设置token...');
          setToken(res.access_token)
          console.log('Token已设置，准备获取用户信息...');
          
          const userInfoSuccess = await fetchUserInfo() // 获取用户信息
          console.log('获取用户信息操作完成，结果:', userInfoSuccess);

          if (userInfoSuccess) {
            ElMessage.success('登录成功')
            console.log('登录流程成功，返回true');
            return true
          } else {
            ElMessage.error('登录成功，但获取用户信息失败');
            console.log('获取用户信息失败，返回false');
            return false;
          }
        }
        console.log('登录API响应无效，未包含access_token');
        return false
      } catch (error: any) {
        console.error('登录API调用失败:', error)
        // 错误处理已在axios拦截器中统一处理，这里不需要重复显示
        return false
      }
    } finally {
      loading.value = false
    }
  }

  // 用户注册
  const userRegister = async (registerForm: RegisterParams) => {
    try {
      loading.value = true;
      console.log('发送注册请求，数据:', registerForm);
      
      try {
        const res = await register(registerForm);
        console.log('注册响应:', res);
        
        // 处理不同的成功响应格式
        // 1. 标准REST API响应 (返回用户对象)
        if (res && res.id) {
          ElMessage.success('注册成功！现在可以登录了');
          return true;
        }
        // 2. 自定义API响应 (code=0)
        else if (res && res.code === 0) {
          ElMessage.success(res.message || '注册成功！现在可以登录了');
          return true;
        }
        // 3. 其他成功响应
        else if (res) {
          ElMessage.success('注册成功！现在可以登录了');
          return true;
        }

        ElMessage.error('注册失败: 服务器返回了意外的响应');
        return false;
      } catch (error: any) {
        console.error('注册请求失败:', error);
        // 错误处理已在axios拦截器中统一处理，这里不需要重复显示
        return false;
      }
    } finally {
      loading.value = false;
    }
  };

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      loading.value = true
      const userInfoRes = await getUserInfo() as unknown as UserInfo;
      
      // 后端直接返回用户对象，我们检查关键字段（如id）是否存在
      if (userInfoRes && userInfoRes.id) {
        setUserInfo(userInfoRes);
        console.log('用户信息获取并设置成功:', userInfoRes);
        return true;
      }
      
      console.error('获取用户信息失败: 无效的用户数据格式', userInfoRes);
      // 如果格式不对，也清除token，强制重新登录
      clearToken();
      return false;
    } catch (error) {
      console.error('获取用户信息接口调用失败:', error)
      // 如果接口调用失败，也清除token，强制重新登录
      clearToken();
      return false
    } finally {
      loading.value = false
    }
  }

  // 更新用户信息
  const updateUser = async (data: Partial<UserInfo>) => {
    try {
      loading.value = true
      const res = await updateUserInfo(data) as UserInfo
      setUserInfo(res)
      ElMessage.success('个人信息已更新')
      return true
    } catch (error) {
      console.error('更新用户信息失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 修改密码
  const changeUserPassword = async (data: { currentPassword: string; newPassword: string }) => {
    try {
      loading.value = true
      await changePassword(data)
      ElMessage.success('密码已成功修改')
      return true
    } catch (error) {
      console.error('修改密码失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 用户登出
  const userLogout = async () => {
    try {
      loading.value = true
      await logout()
      clearToken()
      userInfo.value = null
      ElMessage.success('已安全退出')
      return true
    } catch (error) {
      console.error('登出失败:', error)
      // 即使API调用失败，也清除本地状态
      clearToken()
      userInfo.value = null
      return true
    } finally {
      loading.value = false
    }
  }

  // 自动加载用户信息（如果有token）
  const loadUserInfo = async () => {
    if (token.value && !userInfo.value) {
      await fetchUserInfo()
    }
  }

  return {
    token,
    userInfo,
    loading,
    isLoggedIn,
    setToken,
    clearToken,
    setUserInfo,
    userLogin,
    userRegister,
    fetchUserInfo,
    updateUser,
    changeUserPassword,
    userLogout,
    loadUserInfo
  }
}) 