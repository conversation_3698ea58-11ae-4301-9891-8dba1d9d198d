// 通用工具函数

// 格式化金额
function formatAmount(amount) {
  return parseFloat(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 格式化交易金额（带符号）
function formatTransactionAmount(amount, type) {
  const formattedAmount = formatAmount(Math.abs(amount))
  return type === 'income' ? `+¥${formattedAmount}` : `-¥${formattedAmount}`
}

// 获取交易图标
function getTransactionIcon(categoryName, type) {
  const iconMap = {
    '餐饮美食': '🍽️',
    '交通出行': '🚗',
    '购物消费': '🛍️',
    '生活服务': '🏠',
    '医疗健康': '🏥',
    '教育培训': '📚',
    '娱乐休闲': '🎮',
    '工资收入': '💰',
    '投资收益': '📈',
    '其他收入': '💵',
    '转账': '🔄'
  }
  
  return iconMap[categoryName] || (type === 'income' ? '💰' : '💸')
}

// 获取账户图标
function getAccountIcon(type) {
  const iconMap = {
    'cash': '💵',
    'bank': '🏦',
    'credit': '💳',
    'investment': '📈',
    'other': '💰'
  }
  return iconMap[type] || '💰'
}

// 格式化日期时间
function formatDateTime(dateString) {
  const date = new Date(dateString)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  const transactionDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
  
  const timeStr = date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
  
  if (transactionDate.getTime() === today.getTime()) {
    return `今天 ${timeStr}`
  } else if (transactionDate.getTime() === yesterday.getTime()) {
    return `昨天 ${timeStr}`
  } else {
    return date.toLocaleDateString('zh-CN', { 
      month: '2-digit', 
      day: '2-digit' 
    }) + ` ${timeStr}`
  }
}

// 格式化日期（仅日期）
function formatDate(dateString) {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 格式化时间（仅时间）
function formatTime(dateString) {
  const date = new Date(dateString)
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

// 验证邮箱格式
function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// 验证手机号格式
function validatePhone(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

// 防抖函数
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 节流函数
function throttle(func, limit) {
  let inThrottle
  return function() {
    const args = arguments
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 深拷贝
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item))
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

module.exports = {
  formatAmount,
  formatTransactionAmount,
  getTransactionIcon,
  getAccountIcon,
  formatDateTime,
  formatDate,
  formatTime,
  validateEmail,
  validatePhone,
  debounce,
  throttle,
  deepClone
}
