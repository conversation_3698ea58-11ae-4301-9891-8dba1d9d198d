<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElButton } from 'element-plus'
import { updateAccount } from '@/api/account'
import type { Account } from '@/api/types'
import { useResponsive } from '@/plugins/useResponsive'

// 使用响应式工具检测设备类型
const { isMobile } = useResponsive()

// 组件属性
interface Props {
  account: Account
  visible: boolean
}

const props = defineProps<Props>()

// 事件
const emit = defineEmits(['close', 'success'])

// 响应式状态
const submitting = ref(false)

// 表单数据
const form = reactive({
  id: 0,
  account_name: '',
  account_type: '',
})

// 表单校验规则
const rules = {
  account_name: [
    { required: true, message: '请输入账户名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  account_type: [
    { required: true, message: '请选择账户类型', trigger: 'change' }
  ]
}

// 表单引用
const formRef = ref()

// 账户类型选项 - 仅使用后端支持的枚举值
const accountTypes = [
  { value: 'bank', label: '银行账户', icon: '🏦' },
  { value: 'fund', label: '基金账户', icon: '📊' },
  { value: 'stock', label: '股票账户', icon: '📈' },
  { value: 'debt', label: '负债账户', icon: '💳' }
]

// 监听属性变化，填充表单
watch(() => props.account, (newAccount) => {
  if (newAccount) {
    form.id = newAccount.id
    form.account_name = newAccount.account_name
    form.account_type = newAccount.account_type
  }
}, { immediate: true })

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        submitting.value = true
        
        const updateData = {
          id: form.id,
          account_name: form.account_name,
          account_type: form.account_type
        }
        
        await updateAccount(updateData)
        
        ElMessage.success('账户更新成功')
        emit('success')
        emit('close')
      } catch (error: any) {
        console.error('更新账户失败', error)
        if (error?.response?.status === 500) {
          ElMessage.error('更新账户失败：账户类型不匹配。请选择有效的账户类型。')
        } else {
          ElMessage.error('更新账户失败：' + (error?.message || '未知错误'))
        }
      } finally {
        submitting.value = false
      }
    }
  })
}

// 取消编辑
const handleCancel = () => {
  emit('close')
}

</script>

<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-position="top"
    class="edit-form"
  >
    <el-form-item label="账户名称" prop="account_name">
      <el-input 
        v-model="form.account_name"
        placeholder="请输入账户名称"
        size="large"
      />
    </el-form-item>
    
    <el-form-item label="账户类型" prop="account_type">
      <el-select 
        v-model="form.account_type"
        placeholder="请选择账户类型"
        style="width: 100%"
        size="large"
      >
        <el-option
          v-for="item in accountTypes"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <div class="type-option">
            <span class="icon">{{ item.icon }}</span>
            <span>{{ item.label }}</span>
          </div>
        </el-option>
      </el-select>
    </el-form-item>
    
    <div class="form-actions">
      <el-button @click="handleCancel" size="large">取消</el-button>
      <el-button
        type="primary"
        @click="submitForm"
        :loading="submitting"
        size="large"
      >
        保存修改
      </el-button>
    </div>
  </el-form>
</template>

<style lang="scss" scoped>
.edit-form {
  width: 100%;
}

.type-option {
  display: flex;
  align-items: center;
  
  .icon {
    margin-right: 8px;
    font-size: 1.2em;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-md);
  gap: var(--spacing-sm);
  
  @media (max-width: 768px) {
    flex-direction: column;
    
    .el-button {
      width: 100%;
      margin-left: 0 !important;
      margin-bottom: 10px;
      height: 44px;
    }
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
  
  @media (max-width: 768px) {
    padding-bottom: 5px;
  }
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-input__wrapper) {
  @media (max-width: 768px) {
    padding: 8px 12px;
  }
}

:deep(.el-input__inner) {
  @media (max-width: 768px) {
    height: 44px;
    font-size: 16px; /* 防止iOS缩放 */
  }
}
</style> 