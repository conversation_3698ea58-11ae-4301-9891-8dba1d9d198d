// API工具类
const app = getApp()

class ApiService {
  constructor() {
    // 开发环境API地址
    this.baseURL = 'http://127.0.0.1:8000/api/v1'

    console.log('🌐 API服务地址:', this.baseURL)
  }



  // 通用请求方法
  request(options) {
    return new Promise((resolve, reject) => {
      const token = wx.getStorageSync('token')
      const fullUrl = this.baseURL + options.url

      console.log('🚀 发起API请求:', {
        url: fullUrl,
        method: options.method || 'GET',
        data: options.data,
        hasToken: !!token
      })

      wx.request({
        url: fullUrl,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          ...options.header
        },
        success: (res) => {
          console.log('✅ API请求成功:', {
            url: options.url,
            method: options.method,
            statusCode: res.statusCode,
            data: res.data,
            header: res.header
          })

          if (res.statusCode >= 200 && res.statusCode < 300) {
            // 2xx状态码都视为成功
            console.log('✅ API请求成功:', {
              url: options.url,
              statusCode: res.statusCode,
              dataType: typeof res.data
            })
            resolve(res.data)
          } else if (res.statusCode === 401) {
            // Token过期，跳转到登录页
            wx.removeStorageSync('token')
            wx.removeStorageSync('userInfo')

            wx.showToast({
              title: '登录已过期',
              icon: 'none'
            })

            setTimeout(() => {
              wx.reLaunch({
                url: '/pages/login/login'
              })
            }, 1500)

            reject(new Error('登录已过期'))
          } else {
            const errorMsg = res.data?.message || res.data?.detail || `请求失败 (${res.statusCode})`
            console.error('❌ API请求错误:', {
              url: options.url,
              statusCode: res.statusCode,
              error: errorMsg,
              data: res.data
            })
            reject(new Error(errorMsg))
          }
        },
        fail: (error) => {
          console.error('💥 API请求失败:', {
            url: options.url,
            error: error,
            baseURL: this.baseURL
          })
          reject(new Error('网络连接失败，请检查网络设置和后端服务'))
        }
      })
    })
  }

  // GET请求
  get(url, data = {}) {
    return this.request({
      url,
      method: 'GET',
      data
    })
  }

  // POST请求
  post(url, data = {}) {
    return this.request({
      url,
      method: 'POST',
      data
    })
  }

  // PUT请求
  put(url, data = {}) {
    return this.request({
      url,
      method: 'PUT',
      data
    })
  }

  // DELETE请求
  delete(url, data = {}) {
    return this.request({
      url,
      method: 'DELETE',
      data
    })
  }

  // 用户相关API
  auth = {
    // 登录 - 使用OAuth2PasswordRequestForm格式
    login: (username, password) => {
      return this.request({
        url: '/users/login/access-token',
        method: 'POST',
        data: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
        header: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })
    },

    // 注册
    register: (userData) => {
      return this.post('/users/register', userData)
    },

    // 获取用户信息
    getUserInfo: () => {
      return this.get('/users/me')
    },

    // 发送注册验证码
    sendRegisterCode: (email) => {
      return this.post('/users/send-register-verification', { email })
    },

    // 退出登录
    logout: () => {
      return this.post('/users/logout')
    },

    // 更新用户设置
    updateSettings: (settingsData) => {
      return this.put('/users/settings', settingsData)
    },

    // 获取用户设置
    getSettings: () => {
      return this.get('/users/settings')
    }
  }

  // 用户数据管理API
  user = {
    // 备份用户数据
    backupData: () => {
      return this.post('/users/backup')
    },

    // 获取用户备份列表
    getBackups: () => {
      return this.get('/users/backups')
    },

    // 恢复最新备份
    restoreLatestBackup: () => {
      return this.post('/users/restore-latest')
    },

    // 导出用户数据 (JSON格式，适用于小程序)
    exportData: () => {
      return this.get('/users/export-json')
    },

    // 清空用户数据
    clearAllData: () => {
      return this.delete('/users/clear-data')
    },

    // 删除用户账号
    deleteAccount: () => {
      return this.delete('/users/account')
    }
  }

  // 账户相关API
  accounts = {
    // 获取账户列表
    getList: () => {
      return this.get('/accounts')
    },

    // 创建账户
    create: (accountData) => {
      return this.post('/accounts', accountData)
    },

    // 更新账户
    update: (id, accountData) => {
      return this.put(`/accounts/${id}`, accountData)
    },

    // 删除账户
    delete: (id) => {
      return this.delete(`/accounts/${id}`)
    },

    // 获取账户详情
    getDetail: (id) => {
      return this.get(`/accounts/${id}`)
    }
  }

  // 交易相关API
  transactions = {
    // 获取交易列表
    getList: (params = {}) => {
      return this.get('/transactions', params)
    },

    // 创建交易
    create: (transactionData) => {
      return this.post('/transactions', transactionData)
    },

    // 更新交易
    update: (id, transactionData) => {
      return this.put(`/transactions/${id}`, transactionData)
    },

    // 删除交易
    delete: (id) => {
      return this.delete(`/transactions/${id}`)
    },

    // 获取交易详情
    getDetail: (id) => {
      return this.get(`/transactions/${id}`)
    },

    // 获取最近交易
    getRecent: (limit = 10) => {
      return this.get('/transactions/recent', { limit })
    }
  }

  // 分类相关API
  categories = {
    // 获取分类列表
    getList: () => {
      return this.get('/categories')
    },

    // 按类型获取分类
    getCategories: (type) => {
      return this.get('/categories', { type })
    },

    // 创建分类
    create: (categoryData) => {
      return this.post('/categories', categoryData)
    },

    // 更新分类
    update: (id, categoryData) => {
      return this.put(`/categories/${id}`, categoryData)
    },

    // 删除分类
    delete: (id) => {
      return this.delete(`/categories/${id}`)
    }
  }

  // 统计相关API
  analytics = {
    // 获取仪表盘数据
    getDashboard: () => {
      return this.get('/analytics/dashboard')
    },

    // 获取月度统计
    getMonthlyStats: (year, month) => {
      return this.get('/analytics/monthly', { year, month })
    },

    // 获取月度汇总数据
    getMonthlySummary: (year, month) => {
      return this.get('/analytics/monthly-summary', { year, month })
    },

    // 获取年度统计
    getYearlyStats: (year) => {
      return this.get('/analytics/yearly', { year })
    },

    // 获取分类统计
    getCategoryStats: (params = {}) => {
      return this.get('/analytics/categories', params)
    },

    // 获取账户余额数据
    getAccountBalances: () => {
      return this.get('/analytics/account-balances')
    },

    // 获取净资产趋势
    getNetWorthTrend: (params = {}) => {
      return this.get('/analytics/net-worth-trend', params)
    }
  }

  // 预算相关API
  budgets = {
    // 获取月度预算
    getMonthlyBudgets: (year, month) => {
      return this.get('/budgets/', { year, month })
    },

    // 创建预算
    createBudget: (budgetData) => {
      return this.post('/budgets/', budgetData)
    },

    // 更新预算
    updateBudget: (budgetId, budgetData) => {
      return this.put(`/budgets/${budgetId}`, budgetData)
    },

    // 删除预算
    deleteBudget: (budgetId) => {
      return this.delete(`/budgets/${budgetId}`)
    }
  }
}

// 创建API实例
const api = new ApiService()

// 导出API实例
module.exports = api
