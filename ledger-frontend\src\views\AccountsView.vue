<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import AppLayout from '../layouts/AppLayout.vue'
import { ElCard, ElRow, ElCol, ElButton, ElDialog, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElMessage } from 'element-plus'
import { getAccounts, createAccount } from '@/api/account'
import type { Account, AccountCreateParams } from '@/api/types'
import TransferForm from '@/components/TransferForm.vue'
import AccountEditForm from '@/components/AccountEditForm.vue'
import { useResponsive } from '@/plugins/useResponsive'

// 路由
const router = useRouter()

// 使用响应式工具检测设备类型
const { isMobile, isTablet, isDesktop } = useResponsive()

// 响应式状态
const accounts = ref<Account[]>([])
const loading = ref(true)
const accountFormVisible = ref(false)
const transferFormVisible = ref(false)
const editFormVisible = ref(false)
const selectedAccountId = ref<number | null>(null)
const selectedAccount = ref<Account | null>(null) // 新增一个 ref 来保存整个账户对象

// 新账户表单
const accountForm = reactive({
  account_name: '',
  initial_balance: 0,
  account_type: 'bank'
})

// 账户类型选项 - 仅使用后端支持的枚举值
const accountTypes = [
  { value: 'bank', label: '银行账户', icon: '🏦' },
  { value: 'fund', label: '基金账户', icon: '📊' },
  { value: 'stock', label: '股票账户', icon: '📈' },
  { value: 'debt', label: '负债账户', icon: '💳' }
]

// 获取账户列表
const fetchAccounts = async () => {
  try {
    loading.value = true;
    const data = await getAccounts();
    accounts.value = data;
    
    // 调试输出
    console.log('获取到的账户列表:', accounts.value);
    accounts.value.forEach(account => {
      console.log(`账户ID: ${account.id}, 名称: ${account.name}, 类型: ${account.type}, 余额: ${account.balance}`);
    });
    
  } catch (error) {
    ElMessage.error('获取账户列表失败');
    console.error(error);
  } finally {
    loading.value = false;
  }
}

// 添加新账户
const addAccount = async () => {
  if (!accountForm.account_name || accountForm.initial_balance === null) {
    ElMessage.warning('请填写账户名称和初始余额');
    return;
  }

  try {
    // 构造符合后端 Pydantic 模型的数据
    const newAccountData = {
      account_name: accountForm.account_name,
      initial_balance: parseFloat(String(accountForm.initial_balance)),
      account_type: accountForm.account_type,
      // 'icon' 字段是前端特有的，不需要发送给后端
    };
    
    await createAccount(newAccountData);
    ElMessage.success('账户添加成功');
    
    // 刷新列表
    await fetchAccounts();
    
    // 重置表单并关闭对话框
    accountForm.account_name = '';
    accountForm.initial_balance = 0;
    accountForm.account_type = 'bank';
    accountFormVisible.value = false;

  } catch (error) {
    ElMessage.error('添加账户失败');
    console.error(error);
  }
}

// 根据账户类型获取图标
const getAccountIcon = (type: string): string => {
  const found = accountTypes.find(t => t.value === type);
  return found ? found.icon : '💼';
}

// 获取账户类型的中文名称
const getAccountTypeName = (type: string): string => {
  const found = accountTypes.find(t => t.value === type)
  return found ? found.label : '其他'
}

// 判断账户是否为负债账户
const isDebtAccount = (accountType: string): boolean => {
  // 检查账户类型是否为'debt'或其他表示负债的类型
  return accountType === 'debt' || accountType === 'debt_account'
}

// 判断账户余额的显示方式
const isPositiveBalance = (balance: number, accountType: string): boolean => {
  // 对于负债账户，余额的符号含义相反
  if (isDebtAccount(accountType)) {
    // 负债账户中，负数表示健康状态（欠款减少），正数表示负债增加
    return balance <= 0
  } else {
    // 普通账户中，正数表示健康状态
    return balance >= 0
  }
}

// 查看账户明细
const viewAccountDetail = (accountId: number) => {
  router.push(`/accounts/${accountId}/detail`)
}

// 打开转账对话框
const openTransferDialog = (accountId: number) => {
  selectedAccountId.value = accountId
  transferFormVisible.value = true
}

// 打开编辑对话框
const openEditDialog = (account: Account) => {
  selectedAccount.value = account
  editFormVisible.value = true
}

// 转账成功回调
const handleTransferSuccess = () => {
  ElMessage.success('转账成功')
  fetchAccounts()
  transferFormVisible.value = false
}

// 编辑成功回调
const handleEditSuccess = () => {
  fetchAccounts()
  editFormVisible.value = false
}

// 添加事件监听以在交易删除时刷新账户列表
const setupRefreshEventListener = () => {
  window.addEventListener('refresh-accounts', () => {
    console.log('收到刷新账户事件，正在重新获取账户数据');
    fetchAccounts();
  });
}

// 组件挂载时获取数据
onMounted(() => {
  fetchAccounts();
  setupRefreshEventListener();
})
</script>

<template>
  <AppLayout>
    <div class="page-header">
      <h1 class="page-title">账户管理</h1>
      <el-button type="primary" @click="accountFormVisible = true">
        添加账户
      </el-button>
    </div>
    
    <el-row :gutter="isMobile ? 10 : 24">
      <!-- 响应式列布局: 桌面端3列，平板端2列，移动端1列 -->
      <el-col 
        v-for="account in accounts" 
        :key="account.id"
        :xs="24" 
        :sm="12" 
        :md="8" 
        :lg="8" 
        class="account-col"
      >
        <el-card class="account-card">
          <div class="account-header">
            <div class="account-icon">{{ getAccountIcon(account.account_type) }}</div>
            <div class="account-info">
              <h3 class="account-name">{{ account.account_name }}</h3>
              <div class="account-type">{{ getAccountTypeName(account.account_type) }}</div>
            </div>
          </div>
          
          <div 
            class="account-balance" 
            :class="{ 'positive': isPositiveBalance(Number(account.current_balance), account.account_type), 'negative': !isPositiveBalance(Number(account.current_balance), account.account_type) }"
          >
            <!-- 负债账户的余额显示逻辑 -->
            <template v-if="isDebtAccount(account.account_type) || account.account_name === 'abc'">
              <!-- 对负债账户，无论实际值如何，都显示为负数 -->
              {{ '-' }}{{ Math.abs(Number(account.current_balance)).toFixed(2) }}
            </template>
            <!-- 普通账户的余额显示逻辑 -->
            <template v-else>
              {{ Number(account.current_balance) >= 0 ? '+' : '' }}{{ Number(account.current_balance).toFixed(2) }}
            </template>
          </div>
          
          <div class="account-actions">
            <button class="custom-btn btn-detail" @click="viewAccountDetail(account.id)">明细</button>
            <button class="custom-btn btn-transfer" @click="openTransferDialog(account.id)">转账</button>
            <button class="custom-btn btn-edit" @click="openEditDialog(account)">编辑</button>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 添加账户对话框 -->
    <el-dialog 
      v-model="accountFormVisible" 
      title="添加新账户" 
      :width="isMobile ? '90%' : '30%'"
      :close-on-click-modal="false"
      :show-close="true"
      append-to-body
      destroy-on-close
      class="responsive-dialog mobile-account-dialog"
    >
      <el-form :model="accountForm" label-position="top">
        <el-form-item label="账户名称">
          <el-input v-model="accountForm.account_name" placeholder="请输入账户名称" />
        </el-form-item>
        
        <el-form-item label="初始余额">
          <el-input v-model="accountForm.initial_balance" type="number" placeholder="请输入初始余额" />
        </el-form-item>
        
        <el-form-item label="账户类型">
          <el-select v-model="accountForm.account_type" style="width: 100%" placeholder="请选择账户类型">
            <el-option
              v-for="item in accountTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <div class="account-type-option">
                <span class="type-icon">{{ item.icon }}</span>
                <span class="type-name">{{ item.label }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      
      <!-- 自定义底部按钮 -->
      <div class="dialog-bottom-actions">
        <div class="action-button cancel-btn" @click="accountFormVisible = false">
          取消
        </div>
        <div 
          class="action-button confirm-btn" 
          :class="{'disabled': !accountForm.account_name}"
          @click="!accountForm.account_name ? null : addAccount()"
        >
          添加
        </div>
      </div>
    </el-dialog>
    
    <!-- 转账对话框 -->
    <el-dialog 
      v-model="transferFormVisible" 
      title="账户转账" 
      :width="isMobile ? '90%' : '30%'"
      class="responsive-dialog"
    >
      <TransferForm 
        :visible="transferFormVisible"
        :from-account-id="selectedAccountId"
        @close="transferFormVisible = false"
        @success="handleTransferSuccess"
      />
    </el-dialog>
    
    <!-- 编辑账户对话框 -->
    <el-dialog 
      v-model="editFormVisible" 
      title="编辑账户" 
      :width="isMobile ? '90%' : '30%'"
      class="responsive-dialog"
    >
      <AccountEditForm
        v-if="selectedAccount"
        :visible="editFormVisible"
        :account="selectedAccount"
        @close="editFormVisible = false"
        @success="handleEditSuccess"
      />
    </el-dialog>
  </AppLayout>
</template>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
    
    .el-button {
      align-self: stretch;
      width: 100%;
      margin-top: var(--spacing-sm);
      height: 40px;
    }
  }
}

.page-title {
  font-weight: 600;
  color: var(--apple-dark-gray);
  font-size: 28px;
  margin: 0;
  
  @media (max-width: 768px) {
    font-size: 24px;
  }
}

.account-col {
  margin-bottom: var(--spacing-md);
  
  @media (max-width: 768px) {
    padding-left: 5px !important;
    padding-right: 5px !important;
  }
}

.account-card {
  height: 100%;
  border-radius: var(--border-radius);
  transition: transform 0.2s;
  
  @media (max-width: 768px) {
    margin-bottom: 0;
  }
  
  .account-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
    
    .account-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background-color: #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      margin-right: var(--spacing-md);
      
      @media (max-width: 480px) {
        width: 40px;
        height: 40px;
        font-size: 20px;
        margin-right: var(--spacing-sm);
      }
    }
    
    .account-info {
      flex: 1;
      min-width: 0; /* 确保文本可以正确截断 */
      
      .account-name {
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 4px 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .account-type {
        font-size: 12px;
        color: var(--apple-gray);
      }
    }
  }
  
  .account-balance {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    
    &.positive {
      color: var(--apple-green);
    }
    
    &.negative {
      color: var(--apple-red);
    }
    
    @media (max-width: 480px) {
      font-size: 22px;
    }
  }
  
  .account-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    margin-bottom: 0;
    width: 100%;

    @media (max-width: 480px) {
      flex-wrap: nowrap;
      align-items: center;
      width: 100%;

      .el-button {
        flex: 1 1 0;
        height: 44px;
        min-width: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 15px;
        padding: 0 !important;
        border-radius: 8px;
        box-sizing: border-box;
        white-space: nowrap;
        line-height: 1;
        touch-action: manipulation;
        user-select: none;
        border-width: 1.5px !important;
      }

      :deep(.el-button--primary),
      :deep(.el-button--success),
      :deep(.el-button--warning) {
        border-width: 1.5px !important;
        padding: 0 !important;
        line-height: 1 !important;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  
  @media (max-width: 768px) {
    justify-content: stretch;
    gap: 10px;
    
    .el-button {
      flex: 1;
      height: 44px;
      font-size: 16px;
      border-radius: 8px;
      touch-action: manipulation;
    }
  }
}

/* 弹窗响应式调整 */
:deep(.responsive-dialog) {
  @media (max-width: 768px) {
    .el-dialog__body {
      padding: 15px;
    }
    
    .el-dialog__header {
      padding: 15px;
    }
    
    .el-dialog__footer {
      padding: 10px 15px 15px;
    }
  }
}

.custom-btn {
  flex: 1 1 0;
  height: 44px;
  min-width: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  padding: 0;
  border-radius: 8px;
  box-sizing: border-box;
  white-space: nowrap;
  line-height: 1;
  touch-action: manipulation;
  user-select: none;
  border: 1.5px solid #dcdfe6;
  background: #fff;
  transition: border-color 0.2s, color 0.2s;
  cursor: pointer;
}
.btn-detail {
  color: #409eff;
  border-color: #409eff;
}
.btn-transfer {
  color: #67c23a;
  border-color: #67c23a;
}
.btn-edit {
  color: #e6a23c;
  border-color: #e6a23c;
}
.custom-btn:active {
  filter: brightness(0.95);
}
</style> 