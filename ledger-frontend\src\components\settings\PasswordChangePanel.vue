<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElForm, ElFormItem, ElInput, ElButton, ElMessage } from 'element-plus'
import { changePassword as apiChangePassword } from '../../api/user'

// 组件状态
const isLoading = ref(false)
const formRef = ref()

// 表单数据
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const rules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少为6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 修改密码
const changePassword = async () => {
  if (!formRef.value) return
  
  try {
    // 表单验证
    await formRef.value.validate()
    
    isLoading.value = true
    
    // 调用API修改密码
    await apiChangePassword({
      currentPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword
    })
    
    ElMessage({
      type: 'success',
      message: '密码已成功修改'
    })
    
    // 重置表单
    resetForm()
  } catch (error) {
    console.error('修改密码失败', error)
    ElMessage({
      type: 'error',
      message: '修改密码失败，请确认当前密码是否正确'
    })
  } finally {
    isLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    passwordForm.currentPassword = ''
    passwordForm.newPassword = ''
    passwordForm.confirmPassword = ''
    formRef.value.resetFields()
  }
}
</script>

<template>
  <div class="password-change-panel">
    <el-form 
      ref="formRef"
      :model="passwordForm" 
      :rules="rules"
      label-position="top"
    >
      <el-form-item label="当前密码" prop="currentPassword">
        <el-input 
          v-model="passwordForm.currentPassword" 
          type="password" 
          show-password
          placeholder="请输入当前密码"
        />
      </el-form-item>
      
      <el-form-item label="新密码" prop="newPassword">
        <el-input 
          v-model="passwordForm.newPassword" 
          type="password" 
          show-password
          placeholder="请输入新密码"
        />
      </el-form-item>
      
      <el-form-item label="确认新密码" prop="confirmPassword">
        <el-input 
          v-model="passwordForm.confirmPassword" 
          type="password" 
          show-password
          placeholder="请再次输入新密码"
        />
      </el-form-item>
      
      <el-form-item>
        <div class="form-actions">
          <el-button type="primary" @click="changePassword" :loading="isLoading">
            修改密码
          </el-button>
          <el-button @click="resetForm">
            重置
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.password-change-panel {
  width: 100%;
}

.form-actions {
  display: flex;
  gap: 10px;

  @media (max-width: 768px) {
    gap: 12px;

    .el-button {
      flex: 1;
      height: 44px;
      font-size: 16px;
      border-radius: 8px;
      margin: 0;
    }
  }
}
</style> 