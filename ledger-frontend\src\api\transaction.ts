import { get, post, put, del } from './config'
import type { Transaction, TransactionCreateParams, TransactionUpdateParams } from './types'

// 获取交易记录列表（带筛选条件）
export function getTransactions(params?: any): Promise<Transaction[]> {
  return get('/transactions', params)
}

// 获取交易记录详情
export function getTransactionById(id: number): Promise<Transaction> {
  return get(`/transactions/${id}`)
}

// 创建新交易记录
export function createTransaction(data: TransactionCreateParams): Promise<Transaction> {
  return post('/transactions', data)
}

// 更新交易记录
export function updateTransaction(data: TransactionUpdateParams): Promise<Transaction> {
  return put(`/transactions/${data.id}`, data)
}

// 删除交易记录
export function deleteTransaction(id: number): Promise<void> {
  return del(`/transactions/${id}`)
}

// 获取特定账户的交易记录
export function getAccountTransactions(accountId: number, params?: any): Promise<Transaction[]> {
  return get(`/transactions/by-account/${accountId}`, params)
} 