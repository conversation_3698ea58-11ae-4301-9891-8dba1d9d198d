from sqlalchemy import Column, Inte<PERSON>, String, Boolean, DateTime, DECIMAL, Enum, ForeignKey
from sqlalchemy.orm import relationship
from app.db.base_class import Base
import datetime

class Account(Base):
    __tablename__ = "accounts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="RESTRICT"), nullable=False)
    account_name = Column(String(100), nullable=False)
    # 将 Enum 类型改为 String 类型，以增加灵活性
    account_type = Column(String(50), nullable=False)
    institution = Column(String(100))
    account_number = Column(String(50))
    currency = Column(String(10), default='CNY')
    initial_balance = Column(DECIMAL(15, 2), default=0)
    current_balance = Column(DECIMAL(15, 2), default=0)
    interest_rate = Column(DECIMAL(8, 4))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    deleted = Column(Boolean, default=False)

    user = relationship("User") 