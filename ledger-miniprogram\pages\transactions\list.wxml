<!-- 交易记录页面 -->
<view class="transactions-page">
  <!-- 筛选栏 -->
  <view class="filter-bar">
    <picker mode="date" value="{{startDate}}" bindchange="onStartDateChange">
      <view class="date-picker">
        <text class="date-text">{{startDate || '开始日期'}}</text>
      </view>
    </picker>

    <text class="date-separator">至</text>

    <picker mode="date" value="{{endDate}}" bindchange="onEndDateChange">
      <view class="date-picker">
        <text class="date-text">{{endDate || '结束日期'}}</text>
      </view>
    </picker>

    <view class="filter-btn" bindtap="showFilterModal">
      <text class="filter-icon">🔍</text>
    </view>

    <view class="eye-toggle-btn" bindtap="toggleAmountVisibility">
      <text class="eye-icon">{{hideAmounts ? '👁️' : '🙈'}}</text>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-summary">
    <view class="stat-item">
      <text class="stat-label">收入</text>
      <text class="stat-value income">{{hideAmounts ? '****' : '+¥' + totalIncome}}</text>
    </view>
    <view class="stat-item">
      <text class="stat-label">支出</text>
      <text class="stat-value expense">{{hideAmounts ? '****' : '-¥' + totalExpense}}</text>
    </view>
    <view class="stat-item">
      <text class="stat-label">结余</text>
      <text class="stat-value {{netAmount >= 0 ? 'income' : 'expense'}}">{{hideAmounts ? '****' : netAmountText}}</text>
    </view>
  </view>

  <!-- 交易列表 -->
  <view class="transactions-list">
    <view wx:if="{{loading}}" class="loading">
      <text>加载中...</text>
    </view>

    <view wx:elif="{{transactions.length === 0}}" class="empty-state">
      <text class="empty-icon">📝</text>
      <text class="empty-text">暂无交易记录</text>
      <button class="btn-primary" bindtap="addTransaction">添加第一笔记录</button>
    </view>

    <view wx:else>
      <view class="transaction-group" wx:for="{{groupedTransactions}}" wx:key="date">
        <view class="group-header">
          <text class="group-date">{{item.date}}</text>
          <text class="group-amount {{item.netAmount >= 0 ? 'income' : 'expense'}}">
            {{hideAmounts ? '****' : item.netAmountText}}
          </text>
        </view>

        <view class="group-content">
          <view class="transaction-item" wx:for="{{item.transactions}}" wx:key="id" wx:for-item="transaction">
            <view class="transaction-icon">
              <text class="icon-text">{{transaction.icon}}</text>
            </view>
            <view class="transaction-info">
              <text class="transaction-desc">{{transaction.description}}</text>
              <text class="transaction-category">{{transaction.category}} · {{transaction.account}}</text>
            </view>
            <view class="transaction-right">
              <text class="transaction-amount {{transaction.type}}">{{hideAmounts ? '****' : transaction.amountText}}</text>
              <text class="transaction-time">{{transaction.time}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 添加按钮 -->
  <view class="fab" bindtap="addTransaction">
    <text class="fab-icon">+</text>
  </view>
</view>
