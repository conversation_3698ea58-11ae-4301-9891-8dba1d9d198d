// pages/settings/index.js
const api = require('../../utils/api.js')

Page({
  data: {
    userInfo: {},
    cacheSize: '计算中...',
    loading: false
  },

  onLoad() {
    console.log('设置页面加载')
    this.loadUserInfo()
    this.updateCacheSize()
    // 调试：检查存储中的数据
    this.debugStorageInfo()
  },

  onShow() {
    // 每次显示页面时重新加载用户信息，确保数据同步
    this.loadUserInfo()
    this.updateCacheSize()
  },

  onReady() {
    // 页面渲染完成后，检查用户信息状态
    setTimeout(() => {
      console.log('=== onReady 检查用户信息状态 ===')
      console.log('当前页面用户信息:', this.data.userInfo)
      console.log('用户名是否为空:', !this.data.userInfo.username || this.data.userInfo.username === '')

      if (!this.data.userInfo.username || this.data.userInfo.username === '' || this.data.userInfo.username === '未登录') {
        console.log('用户信息异常，尝试重新加载')
        this.loadUserInfoFromServer()
      }
      console.log('=== onReady 检查完成 ===')
    }, 500) // 延迟500ms确保页面数据已设置
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      console.log('设置页面：开始加载用户信息...')

      // 首先尝试从全局数据获取
      const app = getApp()
      if (app.globalData.userInfo) {
        console.log('设置页面：从全局数据获取用户信息', app.globalData.userInfo)
        this.setData({
          userInfo: app.globalData.userInfo
        })
        return
      }

      // 然后从本地存储获取用户信息
      const localUserInfo = wx.getStorageSync('userInfo')
      console.log('设置页面：从存储获取的用户信息:', localUserInfo)

      if (localUserInfo) {
        try {
          const parsedUserInfo = typeof localUserInfo === 'string' ? JSON.parse(localUserInfo) : localUserInfo
          console.log('设置页面：解析后的用户信息:', parsedUserInfo)

          this.setData({
            userInfo: parsedUserInfo
          })

          // 同时更新全局数据
          app.globalData.userInfo = parsedUserInfo

          console.log('设置页面：用户信息已设置到页面数据')
        } catch (parseError) {
          console.error('设置页面：解析用户信息失败:', parseError)
          // 如果解析失败，尝试从服务器获取
          await this.loadUserInfoFromServer()
        }
      } else {
        // 如果没有本地用户信息，从服务器获取
        console.warn('设置页面：未找到本地用户信息，尝试从服务器获取')
        await this.loadUserInfoFromServer()
      }
    } catch (error) {
      console.error('设置页面：加载用户信息失败:', error)
      // 设置默认状态
      this.setData({
        userInfo: {
          username: '未登录',
          email: ''
        }
      })
    }
  },

  // 从服务器加载用户信息
  async loadUserInfoFromServer() {
    try {
      console.log('尝试从服务器获取用户信息...')
      const token = wx.getStorageSync('token')

      if (!token) {
        console.log('没有token，设置未登录状态')
        this.setData({
          userInfo: {
            username: '未登录',
            email: ''
          }
        })
        return
      }

      const response = await api.auth.getUserInfo()
      console.log('从服务器获取的用户信息:', response)

      if (response && response.username) {
        // 更新本地存储、全局数据和页面数据
        const app = getApp()
        app.updateGlobalUserInfo(response)
        this.setData({
          userInfo: response
        })
        console.log('设置页面：用户信息已从服务器更新')
      } else {
        console.warn('服务器返回的用户信息格式不正确')
        this.setData({
          userInfo: {
            username: '数据异常',
            email: ''
          }
        })
      }
    } catch (error) {
      console.error('从服务器获取用户信息失败:', error)

      // 如果是401错误，可能需要重新登录
      if (error.message && error.message.includes('401')) {
        console.log('Token可能已过期，需要重新登录')
        this.setData({
          userInfo: {
            username: '登录已过期',
            email: ''
          }
        })
      } else {
        // 其他错误，显示网络错误状态
        this.setData({
          userInfo: {
            username: '网络错误',
            email: ''
          }
        })
      }
    }
  },

  // 编辑个人信息
  editProfile() {
    wx.navigateTo({
      url: '/pages/settings/profile'
    })
  },

  // 个人信息
  goToProfile() {
    wx.navigateTo({
      url: '/pages/settings/profile'
    })
  },

  // 偏好设置
  goToPreferences() {
    wx.navigateTo({
      url: '/pages/settings/preferences'
    })
  },

  // 清除缓存
  clearCache() {
    // 计算缓存大小
    this.calculateCacheSize().then(cacheSize => {
      wx.showModal({
        title: '清除缓存',
        content: `当前缓存大小约 ${cacheSize}，确定要清除应用缓存吗？\n\n注意：这将清除所有本地数据，但不会影响您的登录状态。`,
        success: (res) => {
          if (res.confirm) {
            this.performClearCache()
          }
        }
      })
    })
  },

  // 计算缓存大小
  async calculateCacheSize() {
    try {
      const storageInfo = wx.getStorageInfoSync()
      const sizeKB = storageInfo.currentSize

      if (sizeKB < 1024) {
        return `${sizeKB}KB`
      } else {
        return `${(sizeKB / 1024).toFixed(1)}MB`
      }
    } catch (error) {
      console.error('计算缓存大小失败:', error)
      return '未知'
    }
  },

  // 执行清除缓存
  performClearCache() {
    wx.showLoading({
      title: '清除中...'
    })

    try {
      // 保留重要数据
      const importantData = {
        token: wx.getStorageSync('token'),
        userInfo: wx.getStorageSync('userInfo'),
        preferences: wx.getStorageSync('preferences')
      }

      // 清除所有缓存
      wx.clearStorageSync()

      // 恢复重要数据
      Object.keys(importantData).forEach(key => {
        if (importantData[key]) {
          wx.setStorageSync(key, importantData[key])
        }
      })

      wx.hideLoading()
      wx.showToast({
        title: '缓存已清除',
        icon: 'success'
      })

      // 重新计算缓存大小（延迟执行以确保清除完成）
      setTimeout(() => {
        this.updateCacheSize()
      }, 1000)

    } catch (error) {
      wx.hideLoading()
      console.error('清除缓存失败:', error)
      wx.showToast({
        title: '清除失败',
        icon: 'none'
      })
    }
  },

  // 更新缓存大小显示
  async updateCacheSize() {
    const cacheSize = await this.calculateCacheSize()
    this.setData({
      cacheSize: cacheSize
    })
    console.log('当前缓存大小:', cacheSize)
  },

  // 使用帮助
  showHelp() {
    wx.showModal({
      title: '使用帮助',
      content: '📊 记账：点击"记录"标签页添加收支记录\n\n💳 账户：管理您的银行卡、现金等账户\n\n📈 分析：查看收支统计和趋势分析\n\n⚙️ 设置：个人信息和应用偏好设置\n\n如需更多帮助，请联系客服。',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 关于应用
  goToAbout() {
    wx.navigateTo({
      url: '/pages/settings/about'
    })
  },

  // 调试页面
  goToDebug() {
    wx.navigateTo({
      url: '/pages/settings/debug'
    })
  },

  // 退出登录
  handleLogout() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除登录信息和全局数据
          const app = getApp()
          app.clearGlobalUserInfo()
          wx.removeStorageSync('token')

          // 跳转到登录页
          wx.reLaunch({
            url: '/pages/login/login'
          })

          wx.showToast({
            title: '已退出登录',
            icon: 'none'
          })
        }
      }
    })
  },

  // 调试：检查存储信息
  debugStorageInfo() {
    try {
      const token = wx.getStorageSync('token')
      const userInfo = wx.getStorageSync('userInfo')
      const app = getApp()

      console.log('=== 存储调试信息 ===')
      console.log('Token存在:', !!token)
      console.log('Token长度:', token ? token.length : 0)
      console.log('UserInfo存在:', !!userInfo)
      console.log('UserInfo类型:', typeof userInfo)
      console.log('UserInfo内容:', userInfo)
      console.log('全局UserInfo:', app.globalData.userInfo)

      if (userInfo) {
        try {
          const parsed = typeof userInfo === 'string' ? JSON.parse(userInfo) : userInfo
          console.log('解析后的UserInfo:', parsed)
          console.log('用户名:', parsed.username)
          console.log('邮箱:', parsed.email)
          console.log('ID:', parsed.id)
          console.log('创建时间:', parsed.created_at)
        } catch (parseError) {
          console.error('解析UserInfo失败:', parseError)
          console.log('原始UserInfo:', userInfo)
        }
      }

      console.log('当前页面数据:', this.data.userInfo)
      console.log('=== 调试信息结束 ===')

      // 显示调试信息给用户
      const debugInfo = `
Token: ${!!token}
本地存储: ${userInfo ? '有数据' : '无数据'}
全局数据: ${app.globalData.userInfo ? app.globalData.userInfo.username : '无数据'}
页面数据: ${this.data.userInfo.username || '无数据'}
      `

      wx.showModal({
        title: '调试信息',
        content: debugInfo,
        showCancel: false
      })
    } catch (error) {
      console.error('调试存储信息失败:', error)
    }
  },

  // 手动刷新用户信息（调试用）
  async refreshUserInfo() {
    console.log('手动刷新用户信息')
    wx.showLoading({ title: '刷新中...' })

    try {
      await this.loadUserInfo()
      wx.hideLoading()
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      })
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '刷新失败',
        icon: 'none'
      })
    }
  }
})
