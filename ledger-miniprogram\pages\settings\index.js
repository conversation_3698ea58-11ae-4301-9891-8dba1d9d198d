// pages/settings/index.js
const api = require('../../utils/api.js')

Page({
  data: {
    userInfo: {},
    cacheSize: '计算中...',
    loading: false
  },

  onLoad() {
    console.log('设置页面加载')
    // 设置一些测试数据
    this.setData({
      userInfo: {
        username: '测试用户',
        email: '<EMAIL>'
      },
      cacheSize: '2.5MB'
    })
    // 调试：检查存储中的数据
    this.debugStorageInfo()
  },

  onShow() {
    // 暂时注释掉可能有问题的方法
    // this.loadUserInfo()
    // this.updateCacheSize()
  },

  onReady() {
    // 页面渲染完成后，检查用户信息状态
    setTimeout(() => {
      console.log('检查用户信息状态:', this.data.userInfo)
      if (!this.data.userInfo.username || this.data.userInfo.username === '') {
        console.log('用户信息为空，尝试从服务器获取')
        this.loadUserInfoFromServer()
      }
    }, 500) // 延迟500ms确保页面数据已设置
  },

  // 加载用户信息
  loadUserInfo() {
    try {
      console.log('开始加载用户信息...')
      const userInfo = wx.getStorageSync('userInfo')
      console.log('从存储获取的用户信息:', userInfo)

      if (userInfo) {
        const parsedUserInfo = JSON.parse(userInfo)
        console.log('解析后的用户信息:', parsedUserInfo)

        this.setData({
          userInfo: parsedUserInfo
        })

        console.log('用户信息已设置到页面数据')
      } else {
        // 如果没有用户信息，设置默认状态
        console.warn('未找到用户信息，设置默认状态')
        this.setData({
          userInfo: {
            username: '',
            email: ''
          }
        })
      }
    } catch (error) {
      console.error('加载用户信息失败:', error)
      // 设置默认状态
      this.setData({
        userInfo: {
          username: '',
          email: ''
        }
      })
      wx.showToast({
        title: '加载用户信息失败',
        icon: 'none'
      })
    }
  },

  // 从服务器加载用户信息
  async loadUserInfoFromServer() {
    try {
      console.log('尝试从服务器获取用户信息...')
      const token = wx.getStorageSync('token')

      if (!token) {
        console.log('没有token，无法从服务器获取用户信息')
        return
      }

      const response = await api.auth.getUserInfo()
      console.log('从服务器获取的用户信息:', response)

      // 更新本地存储和页面数据
      wx.setStorageSync('userInfo', JSON.stringify(response))
      this.setData({
        userInfo: response
      })

      console.log('用户信息已从服务器更新')
    } catch (error) {
      console.error('从服务器获取用户信息失败:', error)
      // 如果是401错误，可能需要重新登录
      if (error.message && error.message.includes('401')) {
        console.log('Token可能已过期，需要重新登录')
      }
    }
  },

  // 编辑个人信息
  editProfile() {
    wx.navigateTo({
      url: '/pages/settings/profile'
    })
  },

  // 个人信息
  goToProfile() {
    wx.navigateTo({
      url: '/pages/settings/profile'
    })
  },

  // 偏好设置
  goToPreferences() {
    wx.navigateTo({
      url: '/pages/settings/preferences'
    })
  },

  // 清除缓存
  clearCache() {
    // 计算缓存大小
    this.calculateCacheSize().then(cacheSize => {
      wx.showModal({
        title: '清除缓存',
        content: `当前缓存大小约 ${cacheSize}，确定要清除应用缓存吗？\n\n注意：这将清除所有本地数据，但不会影响您的登录状态。`,
        success: (res) => {
          if (res.confirm) {
            this.performClearCache()
          }
        }
      })
    })
  },

  // 计算缓存大小
  async calculateCacheSize() {
    try {
      const storageInfo = wx.getStorageInfoSync()
      const sizeKB = storageInfo.currentSize

      if (sizeKB < 1024) {
        return `${sizeKB}KB`
      } else {
        return `${(sizeKB / 1024).toFixed(1)}MB`
      }
    } catch (error) {
      console.error('计算缓存大小失败:', error)
      return '未知'
    }
  },

  // 执行清除缓存
  performClearCache() {
    wx.showLoading({
      title: '清除中...'
    })

    try {
      // 保留重要数据
      const importantData = {
        token: wx.getStorageSync('token'),
        userInfo: wx.getStorageSync('userInfo'),
        preferences: wx.getStorageSync('preferences')
      }

      // 清除所有缓存
      wx.clearStorageSync()

      // 恢复重要数据
      Object.keys(importantData).forEach(key => {
        if (importantData[key]) {
          wx.setStorageSync(key, importantData[key])
        }
      })

      wx.hideLoading()
      wx.showToast({
        title: '缓存已清除',
        icon: 'success'
      })

      // 重新计算缓存大小（延迟执行以确保清除完成）
      setTimeout(() => {
        this.updateCacheSize()
      }, 1000)

    } catch (error) {
      wx.hideLoading()
      console.error('清除缓存失败:', error)
      wx.showToast({
        title: '清除失败',
        icon: 'none'
      })
    }
  },

  // 更新缓存大小显示
  async updateCacheSize() {
    const cacheSize = await this.calculateCacheSize()
    this.setData({
      cacheSize: cacheSize
    })
    console.log('当前缓存大小:', cacheSize)
  },

  // 使用帮助
  showHelp() {
    wx.showModal({
      title: '使用帮助',
      content: '📊 记账：点击"记录"标签页添加收支记录\n\n💳 账户：管理您的银行卡、现金等账户\n\n📈 分析：查看收支统计和趋势分析\n\n⚙️ 设置：个人信息和应用偏好设置\n\n如需更多帮助，请联系客服。',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 关于应用
  goToAbout() {
    wx.navigateTo({
      url: '/pages/settings/about'
    })
  },

  // 调试页面
  goToDebug() {
    wx.navigateTo({
      url: '/pages/settings/debug'
    })
  },

  // 退出登录
  handleLogout() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除登录信息
          wx.removeStorageSync('token')
          wx.removeStorageSync('userInfo')
          
          // 跳转到登录页
          wx.reLaunch({
            url: '/pages/login/login'
          })
          
          wx.showToast({
            title: '已退出登录',
            icon: 'none'
          })
        }
      }
    })
  },

  // 调试：检查存储信息
  debugStorageInfo() {
    try {
      const token = wx.getStorageSync('token')
      const userInfo = wx.getStorageSync('userInfo')

      console.log('=== 存储调试信息 ===')
      console.log('Token存在:', !!token)
      console.log('Token长度:', token ? token.length : 0)
      console.log('UserInfo存在:', !!userInfo)
      console.log('UserInfo内容:', userInfo)

      if (userInfo) {
        try {
          const parsed = JSON.parse(userInfo)
          console.log('解析后的UserInfo:', parsed)
          console.log('用户名:', parsed.username)
          console.log('邮箱:', parsed.email)
        } catch (parseError) {
          console.error('解析UserInfo失败:', parseError)
        }
      }

      console.log('当前页面数据:', this.data.userInfo)
      console.log('=== 调试信息结束 ===')
    } catch (error) {
      console.error('调试存储信息失败:', error)
    }
  }
})
