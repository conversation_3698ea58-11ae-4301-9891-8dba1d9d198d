<!-- 关于应用页面 -->
<view class="about-page">
  <!-- 应用信息 -->
  <view class="app-info-section">
    <view class="app-icon">
      <text class="icon-text">💰</text>
    </view>
    <view class="app-name">个人记账</view>
    <view class="app-version">版本 {{appInfo.version}}</view>
    <view class="app-description">简单易用的个人财务管理工具</view>
  </view>

  <!-- 应用详情 -->
  <view class="details-section">
    <view class="section-header">应用信息</view>
    <view class="section-content">
      <view class="detail-item">
        <text class="detail-label">版本号</text>
        <text class="detail-value">{{appInfo.version}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">构建版本</text>
        <text class="detail-value">{{appInfo.buildNumber}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">发布日期</text>
        <text class="detail-value">{{appInfo.releaseDate}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">开发者</text>
        <text class="detail-value">{{appInfo.developer}}</text>
      </view>
    </view>
  </view>

  <!-- 功能特性 -->
  <view class="features-section">
    <view class="section-header">主要功能</view>
    <view class="section-content">
      <view class="feature-item" wx:for="{{features}}" wx:key="index">
        <text class="feature-icon">{{item.icon}}</text>
        <view class="feature-info">
          <text class="feature-title">{{item.title}}</text>
          <text class="feature-desc">{{item.description}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 联系我们 -->
  <view class="contact-section">
    <view class="section-header">联系我们</view>
    <view class="section-content">
      <view class="contact-item" bindtap="copyEmail">
        <text class="contact-icon">📧</text>
        <view class="contact-info">
          <text class="contact-title">邮箱反馈</text>
          <text class="contact-value">{{contactInfo.email}}</text>
        </view>
        <text class="contact-action">复制</text>
      </view>
      
      <view class="contact-item" bindtap="openWebsite">
        <text class="contact-icon">🌐</text>
        <view class="contact-info">
          <text class="contact-title">官方网站</text>
          <text class="contact-value">{{contactInfo.website}}</text>
        </view>
        <text class="contact-action">></text>
      </view>
      
      <view class="contact-item" bindtap="joinGroup">
        <text class="contact-icon">👥</text>
        <view class="contact-info">
          <text class="contact-title">用户群</text>
          <text class="contact-value">加入微信群获取支持</text>
        </view>
        <text class="contact-action">></text>
      </view>
    </view>
  </view>

  <!-- 法律信息 -->
  <view class="legal-section">
    <view class="section-header">法律信息</view>
    <view class="section-content">
      <view class="legal-item" bindtap="showPrivacyPolicy">
        <text class="legal-title">隐私政策</text>
        <text class="legal-arrow">></text>
      </view>
      
      <view class="legal-item" bindtap="showTermsOfService">
        <text class="legal-title">服务条款</text>
        <text class="legal-arrow">></text>
      </view>
      
      <view class="legal-item" bindtap="showLicense">
        <text class="legal-title">开源许可</text>
        <text class="legal-arrow">></text>
      </view>
    </view>
  </view>

  <!-- 其他操作 -->
  <view class="actions-section">
    <button class="action-btn" bindtap="checkUpdate">
      检查更新
    </button>
    
    <button class="action-btn" bindtap="rateApp">
      给应用评分
    </button>
    
    <button class="action-btn" bindtap="shareApp">
      分享应用
    </button>
  </view>

  <!-- 版权信息 -->
  <view class="copyright-section">
    <text class="copyright-text">© 2024 个人记账. All rights reserved.</text>
    <text class="copyright-text">Made with ❤️ in China</text>
  </view>
</view>
