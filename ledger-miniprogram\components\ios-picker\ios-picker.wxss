/* components/ios-picker/ios-picker.wxss */

/* iOS风格选择器样式 */
.ios-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 3000;
  animation: fadeIn 0.3s ease;
}

.ios-picker-modal {
  background: white;
  border-radius: 40rpx 40rpx 0 0;
  width: 100%;
  max-height: 60vh;
  overflow: hidden;
  box-shadow: 0 -20rpx 60rpx rgba(0, 0, 0, 0.1);
  animation: slideUp 0.3s ease;
}

.ios-picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #E5E5E7;
  background: #F8F9FA;
}

.ios-picker-btn {
  background: none;
  border: none;
  font-size: 32rpx;
  color: #007AFF;
  padding: 0;
  min-width: 80rpx;
}

.ios-picker-btn.confirm {
  font-weight: 600;
}

.ios-picker-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
  text-align: center;
  flex: 1;
}

.ios-picker-content {
  max-height: 50vh;
  overflow-y: scroll;
}

.ios-picker-item {
  display: flex;
  align-items: center;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #F2F2F7;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.ios-picker-item:last-child {
  border-bottom: none;
}

.ios-picker-item.active {
  background-color: #F0F9FF;
}

.ios-picker-item:active {
  background-color: #E5E5EA;
}

.picker-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
  width: 60rpx;
  text-align: center;
}

.picker-label {
  flex: 1;
  font-size: 34rpx;
  color: #000000;
}

.picker-check {
  font-size: 32rpx;
  color: #007AFF;
  font-weight: 600;
  width: 40rpx;
  text-align: center;
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}
