from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from sqlalchemy.sql import extract
from decimal import Decimal
from datetime import datetime, date

from app.modules.budgets.models import Budget
from app.modules.categories.models import Category
from app.modules.transactions.models import Transaction
from app.modules.budgets.schemas import BudgetCreate, BudgetUpdate, BudgetSummary

async def create_budget(
    db: AsyncSession, budget_data: BudgetCreate, user_id: int
) -> Budget:
    """创建预算"""
    budget = Budget(
        user_id=user_id,
        category_id=budget_data.category_id,
        amount=budget_data.amount,
        year=budget_data.year,
        month=budget_data.month,
    )
    db.add(budget)
    await db.commit()
    await db.refresh(budget)
    return budget

async def get_budget(
    db: AsyncSession, budget_id: int, user_id: int
) -> Optional[Budget]:
    """获取单个预算"""
    query = select(Budget).where(Budget.id == budget_id, Budget.user_id == user_id)
    result = await db.execute(query)
    return result.scalar_one_or_none()

async def update_budget(
    db: AsyncSession, budget: Budget, budget_data: BudgetUpdate
) -> Budget:
    """更新预算"""
    if budget_data.amount is not None:
        budget.amount = budget_data.amount
    
    await db.commit()
    await db.refresh(budget)
    return budget

async def delete_budget(
    db: AsyncSession, budget: Budget
) -> None:
    """删除预算"""
    await db.delete(budget)
    await db.commit()

async def get_monthly_budgets(
    db: AsyncSession, user_id: int, year: int, month: int
) -> List[Dict[str, Any]]:
    """获取用户某月的所有预算及其使用情况"""
    # 查询该月所有预算
    query = select(
        Budget, Category.name, Category.color
    ).join(
        Category, Budget.category_id == Category.id
    ).where(
        Budget.user_id == user_id,
        Budget.year == year,
        Budget.month == month
    )
    
    result = await db.execute(query)
    budget_results = result.all()
    
    budgets_with_categories = []
    for budget_tuple in budget_results:
        budget, category_name, category_color = budget_tuple
        budgets_with_categories.append({
            "id": budget.id,
            "user_id": budget.user_id,
            "category_id": budget.category_id,
            "category_name": category_name,
            "category_color": category_color,
            "amount": budget.amount,
            "spent": budget.spent,
            "remaining": budget.amount - budget.spent,
            "percentage_used": float((budget.spent / budget.amount) * 100) if budget.amount > 0 else 0,
            "year": budget.year,
            "month": budget.month
        })
    
    return budgets_with_categories

async def get_budget_summary(
    db: AsyncSession, user_id: int, year: int, month: int
) -> BudgetSummary:
    """获取预算汇总信息"""
    # 查询总预算
    budget_query = select(func.sum(Budget.amount)).where(
        Budget.user_id == user_id,
        Budget.year == year,
        Budget.month == month
    )
    budget_result = await db.execute(budget_query)
    total_budget = budget_result.scalar_one_or_none() or Decimal("0")
    
    # 查询总支出
    spent_query = select(func.sum(Budget.spent)).where(
        Budget.user_id == user_id,
        Budget.year == year,
        Budget.month == month
    )
    spent_result = await db.execute(spent_query)
    total_spent = spent_result.scalar_one_or_none() or Decimal("0")
    
    # 计算剩余预算和使用百分比
    remaining = total_budget - total_spent
    usage_percentage = float((total_spent / total_budget) * 100) if total_budget > 0 else 0
    
    return BudgetSummary(
        total_budget=total_budget,
        total_spent=total_spent,
        remaining=remaining,
        usage_percentage=usage_percentage
    )

async def update_budget_spent_amounts(
    db: AsyncSession, user_id: int, year: int, month: int
) -> None:
    """更新指定月份所有预算的已支出金额"""
    # 获取该月所有预算
    budgets_query = select(Budget).where(
        Budget.user_id == user_id,
        Budget.year == year,
        Budget.month == month
    )
    budgets_result = await db.execute(budgets_query)
    budgets = budgets_result.scalars().all()
    
    # 获取该月所有交易
    start_date = datetime(year, month, 1)
    if month == 12:
        end_date = datetime(year + 1, 1, 1)
    else:
        end_date = datetime(year, month + 1, 1)
    
    # 更新每个预算类别的已支出金额
    for budget in budgets:
        # 查询该类别在该月的总支出
        transactions_query = select(func.sum(Transaction.amount)).where(
            Transaction.user_id == user_id,
            Transaction.category_id == budget.category_id,
            Transaction.transaction_type == "expense",
            Transaction.transaction_date >= start_date,
            Transaction.transaction_date < end_date
        )
        result = await db.execute(transactions_query)
        total_spent = result.scalar_one_or_none() or Decimal("0")
        
        # 更新预算已支出金额
        budget.spent = abs(total_spent)  # 确保是正数
    
    await db.commit() 