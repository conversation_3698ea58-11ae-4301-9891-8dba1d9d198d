<script setup lang="ts">
import { ref, onMounted, reactive, computed, watchEffect, onBeforeUnmount, watch } from 'vue'
import AppLayout from '../layouts/AppLayout.vue'
import { ElCard, ElRow, ElCol, ElSelect, ElOption, ElDatePicker, ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { 
  getMonthlyIncomeExpense, 
  getExpenseDistribution, 
  getNetWorthTrend, 
  getAccountBalances, 
  getMonthlySummary
} from '../api/analytics'
import type { 
  MonthlyIncome, 
  ExpenseDistribution, 
  NetWorthTrend, 
  AccountBalance, 
  MonthlySummary 
} from '../api/types'
import { useResponsive } from '@/plugins/useResponsive'
import MobileMonthPicker from '@/components/MobileMonthPicker.vue'

// 使用响应式工具检测设备类型
const { isMobile, isTablet } = useResponsive()

// 图表容器的引用
const incomeExpenseChart = ref<HTMLElement | null>(null)
const categoryPieChart = ref<HTMLElement | null>(null)
const trendChart = ref<HTMLElement | null>(null)
const accountBalanceChart = ref<HTMLElement | null>(null)

// 图表实例
let incomeExpenseChartInstance: echarts.ECharts | null = null
let categoryPieChartInstance: echarts.ECharts | null = null
let trendChartInstance: echarts.ECharts | null = null
let accountBalanceChartInstance: echarts.ECharts | null = null

// 筛选条件
const filters = reactive({
  year: new Date().getFullYear(),
  month: new Date().getMonth() + 1,
  viewType: 'month', // 可选值: week, month, year
  period: 'monthly' as 'weekly' | 'monthly' | 'yearly',
  months: 12
})

// iOS风格月份选择器的数据
const selectedDateRange = ref<string[]>([])

// 初始化选择器的默认值
const initializeDatePicker = () => {
  const currentDate = new Date()
  const year = currentDate.getFullYear()
  const month = currentDate.getMonth() + 1
  selectedDateRange.value = [`${year}-${month.toString().padStart(2, '0')}-01`]
}

// 监听iOS选择器的变化
watch(selectedDateRange, (newValue) => {
  if (newValue && newValue.length > 0) {
    const dateStr = newValue[0]
    if (dateStr) {
      const date = new Date(dateStr)
      filters.year = date.getFullYear()
      filters.month = date.getMonth() + 1

      // 重新加载相关数据
      fetchMonthlyIncomeExpense()
      fetchExpenseDistribution()
      fetchMonthlySummary()
    }
  }
}, { deep: true })

// 数据状态
const monthlyData = ref<MonthlyIncome[]>([])
const expenseDistData = ref<ExpenseDistribution | null>(null)
const netWorthData = ref<NetWorthTrend[]>([])
const accountBalanceData = ref<AccountBalance[]>([])
const monthlySummaryData = ref<MonthlySummary | null>(null)

// 加载状态
const loading = reactive({
  monthlyData: false,
  expenseDistData: false,
  netWorthData: false,
  accountBalanceData: false,
  monthlySummaryData: false
})

// 错误状态
const error = reactive({
  monthlyData: '',
  expenseDistData: '',
  netWorthData: '',
  accountBalanceData: '',
  monthlySummaryData: ''
})

// 切换视图类型
const handleViewChange = (viewType: string) => {
  filters.viewType = viewType
  // 重新加载图表数据
  fetchNetWorthTrend()
}

// 切换年份
const handleYearChange = (year: number) => {
  filters.year = year
  // 重新加载相关数据
  fetchMonthlyIncomeExpense()
  fetchExpenseDistribution()
  fetchMonthlySummary()
}

// 切换月份
const handleMonthChange = (month: number) => {
  filters.month = month
  // 重新加载相关数据
  fetchExpenseDistribution()
  fetchMonthlySummary()
}

// 获取月度收入支出数据
const fetchMonthlyIncomeExpense = async () => {
  try {
    loading.monthlyData = true
    error.monthlyData = ''
    const data = await getMonthlyIncomeExpense({ year: filters.year })
    monthlyData.value = data
  } catch (err: any) {
    console.error('获取月度收入支出数据失败:', err)
    error.monthlyData = '获取数据失败: ' + (err.message || '未知错误')
    ElMessage.error('获取月度收入支出数据失败')
  } finally {
    loading.monthlyData = false
  }
}

// 获取支出分布数据
const fetchExpenseDistribution = async () => {
  try {
    loading.expenseDistData = true
    error.expenseDistData = ''
    const data = await getExpenseDistribution({ 
      year: filters.year,
      month: filters.month
    })
    expenseDistData.value = data
  } catch (err: any) {
    console.error('获取支出分布数据失败:', err)
    error.expenseDistData = '获取数据失败: ' + (err.message || '未知错误')
    ElMessage.error('获取支出分布数据失败')
  } finally {
    loading.expenseDistData = false
  }
}

// 获取净资产趋势数据
const fetchNetWorthTrend = async () => {
  try {
    loading.netWorthData = true
    error.netWorthData = ''
    const data = await getNetWorthTrend({ 
      period: filters.period,
      months: filters.months 
    })
    netWorthData.value = data
  } catch (err: any) {
    console.error('获取净资产趋势数据失败:', err)
    error.netWorthData = '获取数据失败: ' + (err.message || '未知错误')
    ElMessage.error('获取净资产趋势数据失败')
  } finally {
    loading.netWorthData = false
  }
}

// 获取账户余额数据
const fetchAccountBalances = async () => {
  try {
    loading.accountBalanceData = true
    error.accountBalanceData = ''
    const data = await getAccountBalances()
    accountBalanceData.value = data
  } catch (err: any) {
    console.error('获取账户余额数据失败:', err)
    error.accountBalanceData = '获取数据失败: ' + (err.message || '未知错误')
    ElMessage.error('获取账户余额数据失败')
  } finally {
    loading.accountBalanceData = false
  }
}

// 获取月度财务汇总数据
const fetchMonthlySummary = async () => {
  try {
    loading.monthlySummaryData = true
    error.monthlySummaryData = ''
    const data = await getMonthlySummary({
      year: filters.year,
      month: filters.month
    })
    monthlySummaryData.value = data
  } catch (err: any) {
    console.error('获取月度财务汇总数据失败:', err)
    error.monthlySummaryData = '获取数据失败: ' + (err.message || '未知错误')
    ElMessage.error('获取月度财务汇总数据失败')
  } finally {
    loading.monthlySummaryData = false
  }
}

// 检查当前是否为深色模式
const isDarkMode = computed(() => {
  return localStorage.getItem('darkMode') === 'true'
})

// 初始化收入支出图表
const initIncomeExpenseChart = () => {
  if (!incomeExpenseChart.value) {
    console.error('收入支出图表容器不存在')
    return
  }
  
  // 确保有数据
  if (!monthlyData.value || monthlyData.value.length === 0) {
    console.error('收入支出数据为空，无法初始化图表')
    return
  }
  
  try {
    console.log('正在初始化收入支出图表...')
    // 销毁已有实例
    if (incomeExpenseChartInstance) {
      incomeExpenseChartInstance.dispose()
    }
    
    // 获取当前模式下的文字颜色
    const textColor = isDarkMode.value ? '#e2e2e6' : '#333333'
    
    // 初始化图表实例
    incomeExpenseChartInstance = echarts.init(incomeExpenseChart.value)
    
    // 设置图表配置
    const option = {
      backgroundColor: isDarkMode.value ? '#282838' : 'transparent',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        textStyle: {
          color: textColor
        }
      },
      legend: {
        data: ['收入', '支出'],
        textStyle: {
          color: textColor // 为图例文字设置颜色
        }
      },
      xAxis: {
        type: 'category',
        data: monthlyData.value.map(item => `${item.month}月`),
        axisLabel: {
          color: textColor // 为X轴标签设置颜色
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: textColor // 为Y轴标签设置颜色
        }
      },
      series: [
        {
          name: '收入',
          type: 'bar',
          data: monthlyData.value.map(item => item.income),
          itemStyle: {
            color: '#4b7bec'
          }
        },
        {
          name: '支出',
          type: 'bar',
          data: monthlyData.value.map(item => item.expense),
          itemStyle: {
            color: '#fc5c65'
          }
        }
      ]
    }
    
    // 渲染图表
    incomeExpenseChartInstance.setOption(option)
    console.log('收入支出图表初始化完成')
  } catch (error) {
    console.error('初始化收入支出图表失败:', error)
  }
}

// 初始化支出分类饼图
const initCategoryPieChart = () => {
  if (!categoryPieChart.value || !expenseDistData.value) {
    console.error('支出分类图表容器不存在或数据为空')
    return
  }
  
  // 确保有数据
  if (!expenseDistData.value.expense_distribution || expenseDistData.value.expense_distribution.length === 0) {
    console.error('支出分布数据为空，无法初始化图表')
    return
  }
  
  try {
    console.log('正在初始化支出分类饼图...')
    // 销毁已有实例
    if (categoryPieChartInstance) {
      categoryPieChartInstance.dispose()
    }
    
    // 获取当前模式下的文字颜色
    const textColor = isDarkMode.value ? '#e2e2e6' : '#333333'
    
    // 初始化图表实例
    categoryPieChartInstance = echarts.init(categoryPieChart.value)
    
    // 准备数据
    const pieData = expenseDistData.value.expense_distribution.map(item => ({
      name: item.category_name,
      value: item.amount
    }))
    
    // 设置图表配置
    const option = {
      backgroundColor: isDarkMode.value ? '#282838' : 'transparent',
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
        textStyle: {
          color: textColor
        }
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        data: pieData.map(item => item.name),
        textStyle: {
          color: textColor // 为图例文字设置颜色
        }
      },
      series: [
        {
          name: '支出分布',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: pieData,
          color: ['#4b7bec', '#45aaf2', '#26de81', '#fd9644', '#fed330', '#fc5c65', '#a55eea', '#778ca3']
        }
      ]
    }
    
    // 渲染图表
    categoryPieChartInstance.setOption(option)
    console.log('支出分类饼图初始化完成')
  } catch (error) {
    console.error('初始化支出分类饼图失败:', error)
  }
}

// 初始化净资产趋势图
const initTrendChart = () => {
  if (!trendChart.value) {
    console.error('净资产趋势图表容器不存在')
    return
  }
  
  // 确保有数据
  if (!netWorthData.value || netWorthData.value.length === 0) {
    console.error('净资产趋势数据为空，无法初始化图表')
    return
  }
  
  try {
    console.log('正在初始化净资产趋势图...')
    // 销毁已有实例
    if (trendChartInstance) {
      trendChartInstance.dispose()
    }
    
    // 获取当前模式下的文字颜色
    const textColor = isDarkMode.value ? '#e2e2e6' : '#333333'
    
    // 初始化图表实例
    trendChartInstance = echarts.init(trendChart.value)
    
    // 设置图表配置
    const option = {
      backgroundColor: isDarkMode.value ? '#282838' : 'transparent',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        textStyle: {
          color: textColor
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: netWorthData.value.map(item => item.date),
        axisLabel: {
          color: textColor // 为X轴标签设置颜色
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: textColor // 为Y轴标签设置颜色
        }
      },
      series: [
        {
          name: '净资产',
          type: 'line',
          stack: '总量',
          emphasis: {
            focus: 'series'
          },
          data: netWorthData.value.map(item => item.net_worth),
          lineStyle: {
            color: '#26de81'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(38, 222, 129, 0.5)'
              },
              {
                offset: 1,
                color: 'rgba(38, 222, 129, 0.1)'
              }
            ])
          }
        }
      ]
    }
    
    // 渲染图表
    trendChartInstance.setOption(option)
    console.log('净资产趋势图初始化完成')
  } catch (error) {
    console.error('初始化净资产趋势图失败:', error)
  }
}

// 初始化账户余额图表
const initAccountBalanceChart = () => {
  if (!accountBalanceChart.value) {
    console.error('账户余额图表容器不存在')
    return
  }
  
  // 确保有数据
  if (!accountBalanceData.value || accountBalanceData.value.length === 0) {
    console.error('账户余额数据为空，无法初始化图表')
    return
  }
  
  try {
    console.log('正在初始化账户余额图表...')
    // 销毁已有实例
    if (accountBalanceChartInstance) {
      accountBalanceChartInstance.dispose()
    }
    
    // 获取当前模式下的文字颜色
    const textColor = isDarkMode.value ? '#e2e2e6' : '#333333'
    
    // 初始化图表实例
    accountBalanceChartInstance = echarts.init(accountBalanceChart.value)
    
    // 准备数据
    const accountNames = accountBalanceData.value.map(item => item.name)
    const accountBalances = accountBalanceData.value.map(item => item.balance)
    const colors = accountBalanceData.value.map(item => item.balance < 0 ? '#fc5c65' : '#4b7bec')
    
    // 设置图表配置
    const option = {
      backgroundColor: isDarkMode.value ? '#282838' : 'transparent',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        textStyle: {
          color: textColor
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        position: 'top',
        axisLabel: {
          color: textColor
        }
      },
      yAxis: {
        type: 'category',
        axisLine: { show: false },
        axisTick: { show: false },
        data: accountNames,
        axisLabel: {
          color: textColor
        }
      },
      series: [
        {
          name: '余额',
          type: 'bar',
          data: accountBalances,
          itemStyle: {
            color: function(params: any) {
              return colors[params.dataIndex]
            }
          },
          label: {
            show: true,
            formatter: '{c}'
          }
        }
      ]
    }
    
    // 渲染图表
    accountBalanceChartInstance.setOption(option)
    console.log('账户余额图表初始化完成')
  } catch (error) {
    console.error('初始化账户余额图表失败:', error)
  }
}

// 当深色模式改变时重绘图表
const refreshChartsOnThemeChange = () => {
  // 短暂延迟确保DOM已更新
  setTimeout(() => {
    // 重新初始化所有图表
    initIncomeExpenseChart()
    initCategoryPieChart()
    initTrendChart()
    initAccountBalanceChart()
    
    // 手动修复图例颜色
    fixChartLegendColors()
  }, 100)
}

// 手动修复图表图例颜色
const fixChartLegendColors = () => {
  if (!isDarkMode.value) return
  
  // 使用DOM操作直接设置图例文字颜色
  setTimeout(() => {
    try {
      // 查找所有图表容器内的图例文字元素
      const chartContainers = document.querySelectorAll('.chart-container')
      chartContainers.forEach(container => {
        // 查找图例文本元素
        const legendTexts = container.querySelectorAll('.ec-legend-item span')
        legendTexts.forEach(text => {
          if (text instanceof HTMLElement) {
            text.style.color = '#e2e2e6'
          }
        })
        
        // 查找SVG中的文本元素
        const svgTexts = container.querySelectorAll('text')
        svgTexts.forEach(text => {
          text.setAttribute('fill', '#e2e2e6')
        })
      })
      console.log('已修复图例文字颜色')
    } catch (error) {
      console.error('修复图例文字颜色失败:', error)
    }
  }, 200)
}

// 监听窗口大小变化，调整图表尺寸
const handleResize = () => {
  // 延迟一下重绘图表，确保容器尺寸已更新
  setTimeout(() => {
    incomeExpenseChartInstance?.resize()
    categoryPieChartInstance?.resize()
    trendChartInstance?.resize()
    accountBalanceChartInstance?.resize()
    
    // 移动端时调整图表配置
    if (isMobile.value) {
      // 收入支出图表优化
      if (incomeExpenseChartInstance) {
        const option = incomeExpenseChartInstance.getOption()
        // 移动端优化图表配置
        option.grid = { left: '5%', right: '5%', bottom: '15%', top: '15%', containLabel: true }
        incomeExpenseChartInstance.setOption(option)
      }
      
      // 支出分布饼图优化
      if (categoryPieChartInstance) {
        const option = categoryPieChartInstance.getOption()
        // 移动端优化饼图图例位置
        option.legend = { 
          orient: 'horizontal', 
          bottom: 0,
          left: 'center',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: { fontSize: 10 }
        }
        categoryPieChartInstance.setOption(option)
      }
      
      // 净资产趋势图优化
      if (trendChartInstance) {
        const option = trendChartInstance.getOption()
        // 移动端优化图表配置
        option.grid = { left: '5%', right: '5%', bottom: '15%', top: '15%', containLabel: true }
        trendChartInstance.setOption(option)
      }
      
      // 账户余额图优化
      if (accountBalanceChartInstance) {
        const option = accountBalanceChartInstance.getOption()
        // 移动端优化图表配置
        option.grid = { left: '5%', right: '15%', bottom: '15%', top: '15%', containLabel: true }
        accountBalanceChartInstance.setOption(option)
      }
    }
  }, 100)
}

// 计算当月收入增长率的显示类型
const incomeGrowthType = computed(() => {
  if (!monthlySummaryData.value) return 'positive'
  return monthlySummaryData.value.income_growth >= 0 ? 'positive' : 'negative'
})

// 计算当月支出增长率的显示类型（支出增加是负面的，支出减少是正面的）
const expenseGrowthType = computed(() => {
  if (!monthlySummaryData.value) return 'negative'
  return monthlySummaryData.value.expense_growth > 0 ? 'negative' : 'positive'
})

// 计算当月结余增长率的显示类型
const balanceGrowthType = computed(() => {
  if (!monthlySummaryData.value) return 'positive'
  return monthlySummaryData.value.balance_growth >= 0 ? 'positive' : 'negative'
})

// 计算净资产增长率的显示类型
const netWorthGrowthType = computed(() => {
  if (!monthlySummaryData.value) return 'positive'
  return monthlySummaryData.value.net_worth_growth >= 0 ? 'positive' : 'negative'
})

// 在组件挂载后初始化
onMounted(async () => {
  // 初始化iOS风格日期选择器
  initializeDatePicker()

  // 强制清除可能的缓存数据
  monthlyData.value = []
  expenseDistData.value = null
  netWorthData.value = []
  accountBalanceData.value = []
  monthlySummaryData.value = null

  // 获取所有数据
  try {
    console.log('开始获取数据...')
    await Promise.all([
      fetchMonthlyIncomeExpense(),
      fetchExpenseDistribution(),
      fetchNetWorthTrend(),
      fetchAccountBalances(),
      fetchMonthlySummary()
    ])
    console.log('所有数据已获取完成')
    
    // 确保在没有数据时不显示任何内容
    if (monthlySummaryData.value && 
        monthlySummaryData.value.monthly_income === 5000 &&
        monthlySummaryData.value.monthly_expense === 3500 &&
        monthlySummaryData.value.net_worth === 50000) {
      console.log('检测到模拟数据，清除...')
      monthlySummaryData.value = null
    }
    
    // 延迟一下初始化图表，确保DOM已经完全渲染
    setTimeout(() => {
      console.log('开始初始化图表...')
      console.log('收入支出图表容器:', incomeExpenseChart.value)
      console.log('支出分类图表容器:', categoryPieChart.value)
      console.log('净资产趋势图表容器:', trendChart.value)
      console.log('账户余额图表容器:', accountBalanceChart.value)
      
      // 初始化图表
      if (monthlyData.value && monthlyData.value.length > 0) {
        initIncomeExpenseChart()
      }
      
      if (expenseDistData.value && expenseDistData.value.expense_distribution && 
          expenseDistData.value.expense_distribution.length > 0) {
        initCategoryPieChart()
      }
      
      if (netWorthData.value && netWorthData.value.length > 0) {
        initTrendChart()
      }
      
      if (accountBalanceData.value && accountBalanceData.value.length > 0) {
        initAccountBalanceChart()
      }
    }, 500)
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
  
  // 监听主题变化
  window.addEventListener('storage', (e) => {
    if (e.key === 'darkMode') {
      refreshChartsOnThemeChange()
    }
  });

  // 检查并修复深色模式下的图例文字颜色
  if (isDarkMode.value) {
    fixChartLegendColors()
  }

  // 监听深色模式变化，立即更新图例
  watch(() => isDarkMode.value, () => {
    refreshChartsOnThemeChange()
  })

  // 在深色模式变化时，确保图表样式一致性
  watch(() => isDarkMode.value, () => {
    // 延迟执行，确保DOM已更新
    setTimeout(() => {
      refreshChartsOnThemeChange()
    }, 200)
  })

  // 监听移动设备状态变化
  watch(() => isMobile.value, () => {
    // 延迟执行，确保DOM已更新
    setTimeout(() => {
      handleResize()
    }, 200)
  })
})

// 当数据变化时更新图表
watchEffect(() => {
  if (monthlyData.value.length > 0 && incomeExpenseChartInstance) {
    initIncomeExpenseChart()
  }
  
  if (expenseDistData.value && categoryPieChartInstance) {
    initCategoryPieChart()
  }
  
  if (netWorthData.value.length > 0 && trendChartInstance) {
    initTrendChart()
  }
  
  if (accountBalanceData.value.length > 0 && accountBalanceChartInstance) {
    initAccountBalanceChart()
  }
})

// 组件销毁前清理
onBeforeUnmount(() => {
  // 移除事件监听
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('storage', refreshChartsOnThemeChange)
  
  // 销毁图表实例
  incomeExpenseChartInstance?.dispose()
  categoryPieChartInstance?.dispose()
  trendChartInstance?.dispose()
  accountBalanceChartInstance?.dispose()
})
</script>

<template>
  <AppLayout>
    <div class="page-header">
      <h1 class="page-title">财务分析</h1>
    </div>
    
    <!-- 筛选器区域 -->
    <div class="analytics-filters" :class="{ 'mobile-filters': isMobile }">
      <!-- 移动端：使用iOS风格月份选择器 -->
      <template v-if="isMobile">
        <div class="mobile-date-picker-container">
          <div class="date-picker-label">选择月份</div>
          <MobileMonthPicker
            v-model="selectedDateRange"
            placeholder="选择年份月份"
            class="mobile-month-picker"
          />
        </div>

        <div class="view-switcher">
          <el-button-group>
            <el-button
              :type="filters.viewType === 'week' ? 'primary' : ''"
              :class="{ 'el-button--primary': filters.viewType === 'week' }"
              @click="handleViewChange('week')"
            >
              周
            </el-button>
            <el-button
              :type="filters.viewType === 'month' ? 'primary' : ''"
              :class="{ 'el-button--primary': filters.viewType === 'month' }"
              @click="handleViewChange('month')"
            >
              月
            </el-button>
            <el-button
              :type="filters.viewType === 'year' ? 'primary' : ''"
              :class="{ 'el-button--primary': filters.viewType === 'year' }"
              @click="handleViewChange('year')"
            >
              年
            </el-button>
          </el-button-group>
        </div>
      </template>

      <!-- 桌面端：保持原有布局 -->
      <template v-else>
        <div class="filter-item date-picker">
          <div class="filter-label">年份:</div>
          <el-select
            v-model="filters.year"
            class="date-select"
            size="default"
          >
            <el-option
              v-for="year in availableYears"
              :key="year"
              :label="`${year}年`"
              :value="year"
            />
          </el-select>
        </div>

        <div class="filter-item date-picker">
          <div class="filter-label">月份:</div>
          <el-select
            v-model="filters.month"
            class="date-select"
            size="default"
          >
            <el-option
              v-for="month in 12"
              :key="month"
              :label="`${month}月`"
              :value="month"
            />
          </el-select>
        </div>

        <div class="view-switcher">
          <el-button-group>
            <el-button
              :type="filters.viewType === 'week' ? 'primary' : ''"
              size="small"
              @click="handleViewChange('week')"
            >
              周
            </el-button>
            <el-button
              :type="filters.viewType === 'month' ? 'primary' : ''"
              size="small"
              @click="handleViewChange('month')"
            >
              月
            </el-button>
            <el-button
              :type="filters.viewType === 'year' ? 'primary' : ''"
              size="small"
              @click="handleViewChange('year')"
            >
              年
            </el-button>
          </el-button-group>
        </div>
      </template>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="isMobile ? 12 : 20" class="summary-row">
      <el-col :xs="12" :sm="12" :md="6" :lg="6">
        <el-card class="summary-card" shadow="hover">
          <div class="summary-title">月度收入</div>
          <div class="summary-value income">
            {{ monthlySummaryData?.monthly_income?.toFixed(2) || '0.00' }}
          </div>
          <div class="summary-change" :class="incomeGrowthType">
            <i :class="monthlySummaryData?.income_growth >= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
            <span>{{ Math.abs(monthlySummaryData?.income_growth || 0).toFixed(2) }}%</span> 较上月
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="12" :md="6" :lg="6">
        <el-card class="summary-card" shadow="hover">
          <div class="summary-title">月度支出</div>
          <div class="summary-value expense">
            {{ monthlySummaryData?.monthly_expense?.toFixed(2) || '0.00' }}
          </div>
          <div class="summary-change" :class="expenseGrowthType">
            <i :class="monthlySummaryData?.expense_growth <= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
            <span>{{ Math.abs(monthlySummaryData?.expense_growth || 0).toFixed(2) }}%</span> 较上月
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="12" :md="6" :lg="6">
        <el-card class="summary-card" shadow="hover">
          <div class="summary-title">月度结余</div>
          <div class="summary-value" :class="{ 'income': monthlySummaryData?.monthly_balance >= 0, 'expense': monthlySummaryData?.monthly_balance < 0 }">
            {{ monthlySummaryData?.monthly_balance >= 0 ? '+' : '' }}{{ monthlySummaryData?.monthly_balance?.toFixed(2) || '0.00' }}
          </div>
          <div class="summary-change" :class="balanceGrowthType">
            <i :class="monthlySummaryData?.balance_growth >= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
            <span>{{ Math.abs(monthlySummaryData?.balance_growth || 0).toFixed(2) }}%</span> 较上月
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="12" :md="6" :lg="6">
        <el-card class="summary-card" shadow="hover">
          <div class="summary-title">净资产</div>
          <div class="summary-value" :class="{ 'income': monthlySummaryData?.net_worth >= 0, 'expense': monthlySummaryData?.net_worth < 0 }">
            {{ monthlySummaryData?.net_worth?.toFixed(2) || '0.00' }}
          </div>
          <div class="summary-change" :class="netWorthGrowthType">
            <i :class="monthlySummaryData?.net_worth_growth >= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
            <span>{{ Math.abs(monthlySummaryData?.net_worth_growth || 0).toFixed(2) }}%</span> 较上月
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="isMobile ? 0 : 20" class="chart-row">
      <!-- 收入支出趋势图 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" class="chart-column">
        <el-card class="chart-card" shadow="hover">
          <div class="card-header">
            <h3>收入与支出趋势</h3>
          </div>
          
          <div 
            v-if="!loading.monthlyData && !error.monthlyData && monthlyData.length > 0"
            ref="incomeExpenseChart" 
            class="chart-container"
          ></div>
          
          <div v-else-if="loading.monthlyData" class="chart-container loading-container">
            <div class="loading-indicator">加载中...</div>
          </div>
          
          <div v-else-if="error.monthlyData" class="chart-container error-container">
            <div class="error-message">{{ error.monthlyData }}</div>
          </div>
          
          <div v-else class="chart-container no-data-container">
            <div class="no-data-message">
              <div class="icon">📊</div>
              <div class="text">暂无收入支出数据</div>
              <div class="hint">添加交易后可查看趋势图</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 支出分类饼图 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" class="chart-column">
        <el-card class="chart-card" shadow="hover">
          <div class="card-header">
            <h3>支出分类</h3>
          </div>
          
          <div 
            v-if="!loading.expenseDistData && !error.expenseDistData && 
                 expenseDistData?.expense_distribution && expenseDistData.expense_distribution.length > 0"
            ref="categoryPieChart" 
            class="chart-container"
          ></div>
          
          <div v-else-if="loading.expenseDistData" class="chart-container loading-container">
            <div class="loading-indicator">加载中...</div>
          </div>
          
          <div v-else-if="error.expenseDistData" class="chart-container error-container">
            <div class="error-message">{{ error.expenseDistData }}</div>
          </div>
          
          <div v-else class="chart-container no-data-container">
            <div class="no-data-message">
              <div class="icon">🍕</div>
              <div class="text">暂无支出分类数据</div>
              <div class="hint">添加支出交易后可查看饼图</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 净资产趋势图 -->
      <el-col :xs="24" :sm="24" :md="24" :lg="12" class="chart-column">
        <el-card class="chart-card" shadow="hover">
          <div class="card-header">
            <h3>净资产趋势</h3>
          </div>
          
          <div 
            v-if="!loading.netWorthData && !error.netWorthData && netWorthData.length > 0"
            ref="trendChart" 
            class="chart-container"
          ></div>
          
          <div v-else-if="loading.netWorthData" class="chart-container loading-container">
            <div class="loading-indicator">加载中...</div>
          </div>
          
          <div v-else-if="error.netWorthData" class="chart-container error-container">
            <div class="error-message">{{ error.netWorthData }}</div>
          </div>
          
          <div v-else class="chart-container no-data-container">
            <div class="no-data-message">
              <div class="icon">📈</div>
              <div class="text">暂无净资产趋势数据</div>
              <div class="hint">添加交易后可查看净资产变化</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 账户余额图 -->
      <el-col :xs="24" :sm="24" :md="24" :lg="12" class="chart-column">
        <el-card class="chart-card" shadow="hover">
          <div class="card-header">
            <h3>账户余额</h3>
          </div>
          
          <div 
            v-if="!loading.accountBalanceData && !error.accountBalanceData && accountBalanceData.length > 0"
            ref="accountBalanceChart" 
            class="chart-container"
          ></div>
          
          <div v-else-if="loading.accountBalanceData" class="chart-container loading-container">
            <div class="loading-indicator">加载中...</div>
          </div>
          
          <div v-else-if="error.accountBalanceData" class="chart-container error-container">
            <div class="error-message">{{ error.accountBalanceData }}</div>
          </div>
          
          <div v-else class="chart-container no-data-container">
            <div class="no-data-message">
              <div class="icon">💰</div>
              <div class="text">暂无账户余额数据</div>
              <div class="hint">添加账户后可查看账户余额</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </AppLayout>
</template>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  
  @media (max-width: 768px) {
    margin-bottom: var(--spacing-md);
  }
}

.page-title {
  font-weight: 600;
  color: var(--apple-dark-gray);
  font-size: 28px;
  margin: 0;
  
  @media (max-width: 768px) {
    font-size: 20px;
  }
}

.analytics-filters {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: 20px;
  flex-wrap: wrap;

  &.mobile-filters {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 12px;
    margin-bottom: 24px;

    .filter-item {
      width: 100%;
      margin-right: 0;
      margin-bottom: 0;
    }

    // iOS风格月份选择器容器
    .mobile-date-picker-container {
      width: 100%;
      padding: 16px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .date-picker-label {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 12px;
        text-align: center;
      }

      .mobile-month-picker {
        width: 100%;

        :deep(.ios-month-picker-trigger) {
          width: 100%;
          height: 48px;
          background: #f8f9fa;
          border: 1px solid #e1e5e9;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 16px;
          font-size: 16px;
          color: #333;
          transition: all 0.2s ease;

          &:hover {
            background: #f0f1f2;
            border-color: #007AFF;
          }

          &:active {
            transform: scale(0.98);
          }
        }

        :deep(.ios-picker-text) {
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }

        :deep(.ios-picker-icon) {
          color: #666;
          font-size: 14px;
        }
      }
    }

    .view-switcher {
      width: 100%;
      display: flex;
      justify-content: center;
      margin-left: 0;
      margin-top: 0;

      .el-button-group {
        display: flex;
        width: 100%;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        height: 48px;

        .el-button {
          flex: 1;
          border-radius: 0 !important;
          border: none !important;
          font-size: 14px;
          font-weight: 500;
          height: 48px !important;
          min-height: 48px !important;
          max-height: 48px !important;
          background: white !important;
          color: #666 !important;
          transition: all 0.2s ease;
          padding: 0 !important;
          margin: 0 !important;
          box-sizing: border-box !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;

          &:first-child {
            border-radius: 8px 0 0 8px !important;
          }

          &:last-child {
            border-radius: 0 8px 8px 0 !important;
          }

          &.el-button--primary,
          &[type="primary"] {
            background: #007AFF !important;
            color: white !important;
            box-shadow: none !important;
            height: 48px !important;
            min-height: 48px !important;
            max-height: 48px !important;
          }

          &:hover:not(.el-button--primary):not([type="primary"]) {
            background: #f5f5f5 !important;
            color: #333 !important;
            height: 48px !important;
          }

          &:active {
            transform: scale(0.98);
          }

          // 强制覆盖Element Plus的默认样式
          &::before,
          &::after {
            display: none !important;
          }
        }
      }
    }
  }
}

.filter-item {
  margin-right: 20px;
}

/* 优化日期选择器样式 */
.date-picker {
  display: flex;
  align-items: center;

  .filter-label {
    margin-right: 8px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
  }

  .date-select {
    width: 120px;

    :deep(.el-select) {
      height: 36px;
    }

    :deep(.el-input) {
      height: 36px;
    }

    :deep(.el-input__wrapper) {
      border-radius: 6px;
      border: 1px solid #e1e5e9;
      transition: all 0.2s ease;
      height: 36px !important;
      min-height: 36px;
      display: flex;
      align-items: center;
      padding: 0 12px;

      &:hover {
        border-color: #007AFF;
      }
    }

    :deep(.el-input__inner) {
      font-size: 14px;
      text-align: center;
      height: 36px !important;
      line-height: 36px !important;
      padding: 0 20px 0 0;
      border: none;
      background: transparent;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    :deep(.el-select__caret) {
      font-size: 14px;
      color: #a8abb2;
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
    }

    :deep(.el-select__suffix) {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      align-items: center;
    }

    @media (max-width: 768px) {
      width: 100%;
    }
  }
}

.view-switcher {
  display: flex;
  margin-left: auto;

  .el-button-group {
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .el-button {
      border: none !important;
      font-size: 13px;
      font-weight: 500;
      padding: 8px 16px !important;
      background: white !important;
      color: #666 !important;
      transition: all 0.2s ease;
      height: 32px !important;
      min-height: 32px !important;
      max-height: 32px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      box-sizing: border-box !important;

      &:first-child {
        border-radius: 6px 0 0 6px !important;
      }

      &:last-child {
        border-radius: 0 6px 6px 0 !important;
      }

      &.el-button--primary,
      &[type="primary"] {
        background: #007AFF !important;
        color: white !important;
        box-shadow: none !important;
        height: 32px !important;
        min-height: 32px !important;
        max-height: 32px !important;
      }

      &:hover:not(.el-button--primary):not([type="primary"]) {
        background: #f5f5f5 !important;
        color: #333 !important;
        height: 32px !important;
      }

      // 强制覆盖Element Plus的默认样式
      &::before,
      &::after {
        display: none !important;
      }
    }
  }
}

/* 深色模式适配 */
.dark-mode {
  .analytics-filters.mobile-filters {
    background: #2a2a2a;

    .mobile-date-picker-container {
      background: #3a3a3a;

      .date-picker-label {
        color: #e2e2e6;
      }

      .mobile-month-picker {
        :deep(.ios-month-picker-trigger) {
          background: #4a4a4a !important;
          border-color: #5a5a5a !important;
          color: #e2e2e6 !important;

          &:hover {
            background: #5a5a5a !important;
            border-color: #007AFF !important;
          }
        }

        :deep(.ios-picker-text) {
          color: #e2e2e6 !important;
        }

        :deep(.ios-picker-icon) {
          color: #a0a0b0 !important;
        }
      }
    }

    .view-switcher .el-button-group .el-button {
      background: #3a3a3a !important;
      color: #a0a0b0 !important;
      height: 48px !important;
      min-height: 48px !important;
      max-height: 48px !important;

      &.el-button--primary,
      &[type="primary"] {
        background: #007AFF !important;
        color: white !important;
        height: 48px !important;
        min-height: 48px !important;
        max-height: 48px !important;
      }

      &:hover:not(.el-button--primary):not([type="primary"]) {
        background: #4a4a4a !important;
        color: #e2e2e6 !important;
        height: 48px !important;
      }
    }
  }

  .date-picker .filter-label {
    color: #e2e2e6;
  }

  .date-picker .date-select {
    :deep(.el-input__wrapper) {
      background-color: #3a3a3a !important;
      border-color: #5a5a5a !important;

      &:hover {
        border-color: #007AFF !important;
      }
    }

    :deep(.el-input__inner) {
      color: #e2e2e6 !important;
      background-color: transparent !important;
    }

    :deep(.el-select__caret) {
      color: #a0a0b0 !important;
    }

    :deep(.el-select__suffix) {
      color: #a0a0b0 !important;
    }
  }

  .view-switcher .el-button-group .el-button {
    background: #3a3a3a !important;
    color: #a0a0b0 !important;
    height: 32px !important;
    min-height: 32px !important;
    max-height: 32px !important;

    &.el-button--primary,
    &[type="primary"] {
      background: #007AFF !important;
      color: white !important;
      height: 32px !important;
      min-height: 32px !important;
      max-height: 32px !important;
    }

    &:hover:not(.el-button--primary):not([type="primary"]) {
      background: #4a4a4a !important;
      color: #e2e2e6 !important;
      height: 32px !important;
    }
  }
}

/* 移除调试相关样式 */
.debug-toggle {
  display: none;
}

.debug-info {
  display: none;
}

.chart-card {
  margin-bottom: var(--spacing-md);
  height: 350px;
  overflow: hidden;
  
  @media (max-width: 768px) {
    height: 300px;
  }
}

.chart-row {
  margin-top: var(--spacing-md);
  
  @media (max-width: 768px) {
    margin-top: 0;
  }
}

.chart-column {
  @media (max-width: 768px) {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-bottom: var(--spacing-md);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  
  h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--apple-dark-gray);
    margin: 0;
    
    @media (max-width: 768px) {
      font-size: 14px;
    }
  }
}

.chart-container {
  height: calc(100% - 40px);
  width: 100%;
  min-height: 300px;
  
  @media (max-width: 768px) {
    min-height: 250px;
  }
}

.summary-row {
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  
  @media (max-width: 768px) {
    margin-top: var(--spacing-md);
    margin-bottom: var(--spacing-md);
  }
}

.summary-card {
  padding: var(--spacing-md);
  height: 100%;
  
  @media (max-width: 768px) {
    padding: 10px;
    margin-bottom: var(--spacing-sm);
  }
  
  .summary-title {
    font-size: 14px;
    color: var(--apple-gray);
    margin-bottom: 8px;
    
    @media (max-width: 768px) {
      font-size: 12px;
      margin-bottom: 4px;
    }
  }
  
  .summary-value {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    position: relative;
    
    @media (max-width: 768px) {
      font-size: 18px;
      margin-bottom: 4px;
    }
    
    &.income {
      color: var(--apple-green);
    }
    
    &.expense {
      color: var(--apple-red);
    }
    
    .negative-marker {
      position: absolute;
      left: -12px;
      top: 0;
    }
  }
  
  .summary-change {
    font-size: 14px;
    
    @media (max-width: 768px) {
      font-size: 12px;
    }
    
    &.positive {
      color: var(--apple-green);
    }
    
    &.negative {
      color: var(--apple-red);
    }
    
    span {
      font-weight: 600;
    }
  }
}

.error-message {
  color: var(--apple-red);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 14px;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-indicator {
  color: var(--apple-gray);
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--apple-gray);
  
  .icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
    
    @media (max-width: 768px) {
      font-size: 36px;
      margin-bottom: 10px;
    }
  }
  
  .text {
    font-size: 16px;
    
    @media (max-width: 768px) {
      font-size: 14px;
    }
  }
  
  .hint {
    font-size: 14px;
    margin-top: 8px;
    color: var(--apple-light-gray);
    
    @media (max-width: 768px) {
      font-size: 12px;
      margin-top: 4px;
    }
  }
}
</style> 