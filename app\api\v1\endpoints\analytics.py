from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc, extract, and_
from decimal import Decimal
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import calendar

from app.db.session import get_db
from app.modules.accounts.models import Account
from app.modules.transactions.models import Transaction
from app.core.security import get_current_user
from app.modules.users.schemas import User
from app.modules.categories.models import Category

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/dashboard")
async def get_dashboard_data(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取仪表盘数据"""
    try:
        # 1. 计算总资产（所有非负债账户余额之和）
        asset_accounts_stmt = select(func.sum(Account.current_balance)).where(
            Account.user_id == current_user.id,
            Account.account_type != 'debt',
            Account.deleted == False
        )
        asset_result = await db.execute(asset_accounts_stmt)
        total_assets = asset_result.scalar_one_or_none() or Decimal(0)
        
        # 2. 计算总负债（所有负债账户余额之和）
        # 负债账户余额为正数，直接表示负债金额
        liabilities_accounts_stmt = select(func.sum(Account.current_balance)).where(
            Account.user_id == current_user.id,
            Account.account_type == 'debt',
            Account.deleted == False
        )
        liabilities_result = await db.execute(liabilities_accounts_stmt)
        total_liabilities = liabilities_result.scalar_one_or_none() or Decimal(0)
        
        # 3. 计算净资产
        net_worth = total_assets - total_liabilities
        
        # 4. 模拟资产增长率（实际应用中可以与上月比较）
        asset_growth = 0.0
        
        # 5. 获取近期交易（最新5条）
        recent_transactions_stmt = select(Transaction).filter(
            Transaction.user_id == current_user.id
        ).order_by(Transaction.transaction_date.desc()).limit(5)
        
        result = await db.execute(recent_transactions_stmt)
        recent_transactions_raw = result.scalars().all()
        
        # 转换格式
        recent_transactions = []
        for transaction in recent_transactions_raw:
            # 异步方式获取分类信息
            category_name = None
            if transaction.category_id:
                category_stmt = select(Category).where(Category.id == transaction.category_id)
                category_result = await db.execute(category_stmt)
                category = category_result.scalar_one_or_none()
                category_name = category.name if category else None
            
            transaction_dict = {
                "id": transaction.id,
                "date": transaction.transaction_date.strftime('%Y-%m-%d %H:%M:%S'),
                "type": transaction.transaction_type,
                "amount": float(transaction.amount),
                "category": category_name,
                "account_id": transaction.account_id,
                "description": transaction.description,
                "from_account_id": transaction.from_account_id,
                "to_account_id": transaction.to_account_id
            }
            recent_transactions.append(transaction_dict)
        
        return {
            "totalAssets": float(total_assets),
            "totalLiabilities": float(total_liabilities),
            "netWorth": float(net_worth),
            "assetGrowth": float(asset_growth),
            "recentTransactions": recent_transactions
        }
    except Exception as e:
        logger.error(f"获取仪表盘数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取仪表盘数据失败: {str(e)}")

@router.get("/monthly-income-expense")
async def get_monthly_income_expense(
    year: Optional[int] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取月度收入和支出数据"""
    try:
        # 如果未指定年份，使用当前年份
        current_year = datetime.now().year
        target_year = year or current_year
        
        # 获取指定年份的所有月份的收入和支出
        result = []
        
        for month in range(1, 13):
            # 构建月份的开始和结束日期
            start_date = datetime(target_year, month, 1)
            last_day = calendar.monthrange(target_year, month)[1]
            end_date = datetime(target_year, month, last_day, 23, 59, 59)
            
            # 收入查询
            income_stmt = select(func.sum(Transaction.amount)).where(
                Transaction.user_id == current_user.id,
                Transaction.transaction_type == 'income',
                Transaction.transaction_date >= start_date,
                Transaction.transaction_date <= end_date
            )
            income_result = await db.execute(income_stmt)
            total_income = income_result.scalar_one_or_none() or Decimal(0)
            
            # 支出查询
            expense_stmt = select(func.sum(Transaction.amount)).where(
                Transaction.user_id == current_user.id,
                Transaction.transaction_type == 'expense',
                Transaction.transaction_date >= start_date,
                Transaction.transaction_date <= end_date
            )
            expense_result = await db.execute(expense_stmt)
            total_expense = expense_result.scalar_one_or_none() or Decimal(0)
            
            result.append({
                "month": month,
                "income": float(total_income),
                "expense": float(total_expense)
            })
        
        return result
    except Exception as e:
        logger.error(f"获取月度收入支出数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取月度收入支出数据失败: {str(e)}")

@router.get("/expense-distribution")
async def get_expense_distribution(
    year: Optional[int] = None,
    month: Optional[int] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取支出分布数据"""
    try:
        # 获取当前日期
        now = datetime.now()
        target_year = year or now.year
        target_month = month or now.month
        
        # 构建查询条件
        filters = [
            Transaction.user_id == current_user.id,
            Transaction.transaction_type == 'expense'
        ]
        
        # 添加日期过滤条件
        if year and month:
            start_date = datetime(target_year, target_month, 1)
            last_day = calendar.monthrange(target_year, target_month)[1]
            end_date = datetime(target_year, target_month, last_day, 23, 59, 59)
            
            filters.append(Transaction.transaction_date >= start_date)
            filters.append(Transaction.transaction_date <= end_date)
        elif year:
            start_date = datetime(target_year, 1, 1)
            end_date = datetime(target_year, 12, 31, 23, 59, 59)
            
            filters.append(Transaction.transaction_date >= start_date)
            filters.append(Transaction.transaction_date <= end_date)
        
        # 按分类汇总支出
        stmt = select(
            Transaction.category_id,
            func.sum(Transaction.amount).label("total_amount")
        ).filter(
            *filters
        ).group_by(
            Transaction.category_id
        )
        
        result = await db.execute(stmt)
        expense_by_category_raw = result.all()
        
        # 处理结果
        total_expense = Decimal(0)
        expense_by_category = []
        
        for category_id, amount in expense_by_category_raw:
            total_expense += amount
            
            # 获取分类名称
            category_name = "未分类"
            if category_id:
                category_stmt = select(Category).where(Category.id == category_id)
                category_result = await db.execute(category_stmt)
                category = category_result.scalar_one_or_none()
                category_name = category.name if category else "未分类"
            
            expense_by_category.append({
                "category_id": category_id,
                "category_name": category_name,
                "amount": float(amount)
            })
        
        # 计算百分比
        expense_distribution = []
        for item in expense_by_category:
            percentage = (Decimal(item["amount"]) / total_expense * 100) if total_expense > 0 else 0
            expense_distribution.append({
                "category_name": item["category_name"],
                "amount": item["amount"],
                "percentage": float(percentage)
            })
        
        # 按百分比降序排序
        expense_distribution.sort(key=lambda x: x["percentage"], reverse=True)
        
        return {
            "total_expense": float(total_expense),
            "expense_distribution": expense_distribution
        }
    except Exception as e:
        logger.error(f"获取支出分布数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取支出分布数据失败: {str(e)}")

@router.get("/net-worth-trend")
async def get_net_worth_trend(
    period: str = "monthly",  # 'weekly', 'monthly', 'yearly'
    months: int = 12,  # 查询几个月的数据
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取净资产趋势数据"""
    try:
        # 计算开始日期
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30 * months)
        
        # 获取期间内的所有交易
        transactions_stmt = select(Transaction).filter(
            Transaction.user_id == current_user.id,
            Transaction.transaction_date >= start_date,
            Transaction.transaction_date <= end_date
        ).order_by(Transaction.transaction_date)
        
        result = await db.execute(transactions_stmt)
        transactions = result.scalars().all()
        
        # 获取期初账户余额
        accounts_stmt = select(Account).filter(
            Account.user_id == current_user.id,
            Account.is_active == True,
            Account.deleted == False
        )
        result = await db.execute(accounts_stmt)
        accounts = result.scalars().all()
        
        # 计算期初净资产
        initial_net_worth = sum(
            acc.initial_balance for acc in accounts if acc.account_type != 'debt'
        ) - abs(sum(
            acc.initial_balance for acc in accounts if acc.account_type == 'debt'
        ))
        
        # 生成净资产趋势数据
        trend_data = []
        
        if period == "monthly":
            # 按月计算净资产
            current_date = start_date.replace(day=1)
            net_worth = initial_net_worth
            
            while current_date <= end_date:
                next_month = current_date.month + 1 if current_date.month < 12 else 1
                next_year = current_date.year + 1 if current_date.month == 12 else current_date.year
                next_date = datetime(next_year, next_month, 1)
                
                # 计算该月净资产变化
                month_transactions = [
                    t for t in transactions 
                    if t.transaction_date >= current_date and t.transaction_date < next_date
                ]
                
                for tx in month_transactions:
                    if tx.transaction_type == 'income':
                        net_worth += tx.amount
                    elif tx.transaction_type == 'expense':
                        net_worth -= tx.amount
                    # 转账交易不影响净资产
                
                trend_data.append({
                    "date": current_date.strftime("%Y-%m"),
                    "net_worth": float(net_worth)
                })
                
                current_date = next_date
        
        return trend_data
    except Exception as e:
        logger.error(f"获取净资产趋势数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取净资产趋势数据失败: {str(e)}")

@router.get("/account-balances")
async def get_account_balances(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取账户余额数据"""
    try:
        # 查询所有活跃账户
        accounts_stmt = select(Account).filter(
            Account.user_id == current_user.id,
            Account.is_active == True,
            Account.deleted == False
        )
        
        result = await db.execute(accounts_stmt)
        accounts = result.scalars().all()
        
        # 处理结果
        account_balances = []
        for account in accounts:
            account_balances.append({
                "id": account.id,
                "name": account.account_name,
                "type": account.account_type,
                "institution": account.institution,
                "balance": float(account.current_balance),
                "currency": account.currency
            })
        
        # 按余额降序排序
        account_balances.sort(key=lambda x: abs(x["balance"]), reverse=True)
        
        return account_balances
    except Exception as e:
        logger.error(f"获取账户余额数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取账户余额数据失败: {str(e)}")

@router.get("/monthly")
async def get_monthly_stats(
    year: Optional[int] = None,
    month: Optional[int] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取月度统计数据（小程序端使用）"""
    try:
        # 获取当前日期
        now = datetime.now()
        target_year = year or now.year
        target_month = month or now.month

        # 构建月份的开始和结束日期
        start_date = datetime(target_year, target_month, 1)
        last_day = calendar.monthrange(target_year, target_month)[1]
        end_date = datetime(target_year, target_month, last_day, 23, 59, 59)

        # 查询月度收入
        income_stmt = select(func.sum(Transaction.amount)).where(
            Transaction.user_id == current_user.id,
            Transaction.transaction_type == 'income',
            Transaction.transaction_date >= start_date,
            Transaction.transaction_date <= end_date
        )
        income_result = await db.execute(income_stmt)
        total_income = income_result.scalar_one_or_none() or Decimal(0)

        # 查询月度支出
        expense_stmt = select(func.sum(Transaction.amount)).where(
            Transaction.user_id == current_user.id,
            Transaction.transaction_type == 'expense',
            Transaction.transaction_date >= start_date,
            Transaction.transaction_date <= end_date
        )
        expense_result = await db.execute(expense_stmt)
        total_expense = expense_result.scalar_one_or_none() or Decimal(0)

        return {
            "total_income": float(total_income),
            "total_expense": float(total_expense),
            "net_balance": float(total_income - total_expense)
        }
    except Exception as e:
        logger.error(f"获取月度统计数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取月度统计数据失败: {str(e)}")

@router.get("/categories")
async def get_category_stats(
    year: Optional[int] = None,
    month: Optional[int] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取分类统计数据（小程序端使用）"""
    try:
        # 获取当前日期
        now = datetime.now()
        target_year = year or now.year
        target_month = month or now.month

        # 构建月份的开始和结束日期
        start_date = datetime(target_year, target_month, 1)
        last_day = calendar.monthrange(target_year, target_month)[1]
        end_date = datetime(target_year, target_month, last_day, 23, 59, 59)

        # 查询支出分类统计
        expense_stmt = select(
            Category.id,
            Category.name,
            func.sum(Transaction.amount).label("total_amount")
        ).join(
            Transaction, Transaction.category_id == Category.id
        ).where(
            Transaction.user_id == current_user.id,
            Transaction.transaction_type == 'expense',
            Transaction.transaction_date >= start_date,
            Transaction.transaction_date <= end_date
        ).group_by(Category.id, Category.name)

        expense_result = await db.execute(expense_stmt)
        expense_categories = []
        for category_id, category_name, amount in expense_result.all():
            expense_categories.append({
                "id": category_id,
                "name": category_name,
                "amount": float(amount)
            })

        # 查询收入分类统计
        income_stmt = select(
            Category.id,
            Category.name,
            func.sum(Transaction.amount).label("total_amount")
        ).join(
            Transaction, Transaction.category_id == Category.id
        ).where(
            Transaction.user_id == current_user.id,
            Transaction.transaction_type == 'income',
            Transaction.transaction_date >= start_date,
            Transaction.transaction_date <= end_date
        ).group_by(Category.id, Category.name)

        income_result = await db.execute(income_stmt)
        income_categories = []
        for category_id, category_name, amount in income_result.all():
            income_categories.append({
                "id": category_id,
                "name": category_name,
                "amount": float(amount)
            })

        return {
            "expense_categories": expense_categories,
            "income_categories": income_categories
        }
    except Exception as e:
        logger.error(f"获取分类统计数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分类统计数据失败: {str(e)}")

@router.get("/monthly-summary")
async def get_monthly_summary(
    year: Optional[int] = None,
    month: Optional[int] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取月度财务汇总数据"""
    try:
        # 获取当前日期
        now = datetime.now()
        target_year = year or now.year
        target_month = month or now.month

        # 构建月份的开始和结束日期
        start_date = datetime(target_year, target_month, 1)
        last_day = calendar.monthrange(target_year, target_month)[1]
        end_date = datetime(target_year, target_month, last_day, 23, 59, 59)
        
        # 上个月的日期范围
        prev_month = target_month - 1 if target_month > 1 else 12
        prev_year = target_year if target_month > 1 else target_year - 1
        prev_start_date = datetime(prev_year, prev_month, 1)
        prev_last_day = calendar.monthrange(prev_year, prev_month)[1]
        prev_end_date = datetime(prev_year, prev_month, prev_last_day, 23, 59, 59)
        
        # 当月收入
        income_stmt = select(func.sum(Transaction.amount)).where(
            Transaction.user_id == current_user.id,
            Transaction.transaction_type == 'income',
            Transaction.transaction_date >= start_date,
            Transaction.transaction_date <= end_date
        )
        income_result = await db.execute(income_stmt)
        monthly_income = income_result.scalar_one_or_none() or Decimal(0)
        
        # 上月收入
        prev_income_stmt = select(func.sum(Transaction.amount)).where(
            Transaction.user_id == current_user.id,
            Transaction.transaction_type == 'income',
            Transaction.transaction_date >= prev_start_date,
            Transaction.transaction_date <= prev_end_date
        )
        prev_income_result = await db.execute(prev_income_stmt)
        prev_monthly_income = prev_income_result.scalar_one_or_none() or Decimal(0)
        
        # 计算收入增长率
        income_growth = Decimal(0)
        if prev_monthly_income > 0:
            income_growth = ((monthly_income - prev_monthly_income) / prev_monthly_income) * 100
        elif prev_monthly_income == 0 and monthly_income > 0:
            # 上月为0，本月有收入，视为100%增长
            income_growth = Decimal(100)
        
        # 当月支出
        expense_stmt = select(func.sum(Transaction.amount)).where(
            Transaction.user_id == current_user.id,
            Transaction.transaction_type == 'expense',
            Transaction.transaction_date >= start_date,
            Transaction.transaction_date <= end_date
        )
        expense_result = await db.execute(expense_stmt)
        monthly_expense = expense_result.scalar_one_or_none() or Decimal(0)
        
        # 上月支出
        prev_expense_stmt = select(func.sum(Transaction.amount)).where(
            Transaction.user_id == current_user.id,
            Transaction.transaction_type == 'expense',
            Transaction.transaction_date >= prev_start_date,
            Transaction.transaction_date <= prev_end_date
        )
        prev_expense_result = await db.execute(prev_expense_stmt)
        prev_monthly_expense = prev_expense_result.scalar_one_or_none() or Decimal(0)
        
        # 计算支出增长率
        expense_growth = Decimal(0)
        if prev_monthly_expense > 0:
            expense_growth = ((monthly_expense - prev_monthly_expense) / prev_monthly_expense) * 100
        elif prev_monthly_expense == 0 and monthly_expense > 0:
            # 上月为0，本月有支出，视为100%增长
            expense_growth = Decimal(100)
        
        # 月度结余
        monthly_balance = monthly_income - monthly_expense
        prev_monthly_balance = prev_monthly_income - prev_monthly_expense
        
        # 计算结余增长率
        balance_growth = Decimal(0)
        if prev_monthly_balance > 0:
            balance_growth = ((monthly_balance - prev_monthly_balance) / prev_monthly_balance) * 100
        elif prev_monthly_balance < 0 and monthly_balance > 0:
            balance_growth = Decimal(100)  # 从负转正，设为100%增长
        elif prev_monthly_balance < 0 and monthly_balance < 0:
            # 两者都是负数，如果月度结余赤字减少，则为正增长
            balance_growth = ((prev_monthly_balance - monthly_balance) / abs(prev_monthly_balance)) * 100
        elif prev_monthly_balance == 0:
            # 上月结余为0
            if monthly_balance > 0:
                balance_growth = Decimal(100)  # 正增长
            elif monthly_balance < 0:
                balance_growth = Decimal(-100)  # 负增长
        
        # 总净资产
        # 总资产（所有非负债账户余额之和）
        asset_accounts_stmt = select(func.sum(Account.current_balance)).where(
            Account.user_id == current_user.id,
            Account.account_type != 'debt',
            Account.deleted == False
        )
        asset_result = await db.execute(asset_accounts_stmt)
        total_assets = asset_result.scalar_one_or_none() or Decimal(0)
        
        # 总负债（所有负债账户余额之和）
        # 注意：负债账户余额存储为正数，直接求和即可得到总负债金额
        liabilities_accounts_stmt = select(func.sum(Account.current_balance)).where(
            Account.user_id == current_user.id,
            Account.account_type == 'debt',
            Account.deleted == False
        )
        liabilities_result = await db.execute(liabilities_accounts_stmt)
        total_liabilities = liabilities_result.scalar_one_or_none() or Decimal(0)
        
        # 计算净资产
        net_worth = total_assets - total_liabilities
        
        # 年初净资产
        # 获取年初账户余额快照 - 理想情况下应该有历史数据表
        # 这里我们尝试获取1月1日的第一笔交易前的账户余额总和作为年初净资产

        # 使用交易记录计算年初净资产
        year_start_date = datetime(target_year, 1, 1)
        year_end_date = datetime(target_year, 12, 31, 23, 59, 59)
        
        # 计算当年的收入和支出总额
        year_income_stmt = select(func.sum(Transaction.amount)).where(
            Transaction.user_id == current_user.id,
            Transaction.transaction_type == 'income',
            Transaction.transaction_date >= year_start_date,
            Transaction.transaction_date <= end_date
        )
        year_income_result = await db.execute(year_income_stmt)
        year_income = year_income_result.scalar_one_or_none() or Decimal(0)
        
        year_expense_stmt = select(func.sum(Transaction.amount)).where(
            Transaction.user_id == current_user.id,
            Transaction.transaction_type == 'expense',
            Transaction.transaction_date >= year_start_date,
            Transaction.transaction_date <= end_date
        )
        year_expense_result = await db.execute(year_expense_stmt)
        year_expense = year_expense_result.scalar_one_or_none() or Decimal(0)
        
        # 估算年初净资产
        # 方法：当前净资产 - 年内收入 + 年内支出
        start_year_net_worth = net_worth - year_income + year_expense
        
        # 确保年初净资产不会为0（避免除以0错误）
        if start_year_net_worth == Decimal(0):
            start_year_net_worth = Decimal(1)  # 设置为最小值
        
        # 计算净资产同比增长率
        net_worth_growth = Decimal(0)
        if start_year_net_worth > 0:
            net_worth_growth = ((net_worth - start_year_net_worth) / start_year_net_worth) * 100
        elif start_year_net_worth < 0 and net_worth > 0:
            # 从负变正
            net_worth_growth = Decimal(100)
        elif start_year_net_worth < 0 and net_worth < 0:
            # 负债减少
            if abs(net_worth) < abs(start_year_net_worth):
                net_worth_growth = ((abs(start_year_net_worth) - abs(net_worth)) / abs(start_year_net_worth)) * 100
            else:
                # 负债增加
                net_worth_growth = -((abs(net_worth) - abs(start_year_net_worth)) / abs(start_year_net_worth)) * 100
        
        return {
            "monthly_income": float(monthly_income),
            "income_growth": float(income_growth),
            "monthly_expense": float(monthly_expense),
            "expense_growth": float(expense_growth),
            "monthly_balance": float(monthly_balance),
            "balance_growth": float(balance_growth),
            "net_worth": float(net_worth),
            "net_worth_growth": float(net_worth_growth)
        }
    except Exception as e:
        logger.error(f"获取月度财务汇总数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取月度财务汇总数据失败: {str(e)}") 