<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage, ElDialog } from 'element-plus'
import { Calendar, CircleCheck, Close } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Array<string>,
    default: () => []
  },
  placeholder: {
    type: String,
    default: '选择月份'
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 显示状态
const showPicker = ref(false)

// 当前值
const currentValue = ref(props.modelValue || [])

// 生成月份选项（最近12个月）
const monthOptions = computed(() => {
  const options = []
  const now = new Date()
  
  for (let i = 0; i < 12; i++) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const monthStr = month.toString().padStart(2, '0')
    
    // 计算月份的开始和结束日期
    const startDate = `${year}-${monthStr}-01`
    const endDate = new Date(year, month, 0) // 下个月的第0天就是当月最后一天
    const endDateStr = `${year}-${monthStr}-${endDate.getDate().toString().padStart(2, '0')}`
    
    options.push({
      label: `${year}年${month}月`,
      value: [startDate, endDateStr],
      year,
      month
    })
  }
  
  return options
})

// 格式化显示文本
const displayText = computed(() => {
  if (!currentValue.value || currentValue.value.length === 0) {
    return props.placeholder
  }
  
  // 根据日期范围找到对应的月份
  const startDate = currentValue.value[0]
  if (startDate) {
    const date = new Date(startDate)
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    return `${year}年${month}月`
  }
  
  return props.placeholder
})

// 选择月份
const selectMonth = (option: any) => {
  currentValue.value = option.value
  emit('update:modelValue', option.value)
  emit('change', option.value)
  showPicker.value = false
  
  ElMessage.success(`已选择${option.label}`)
}

// 清空选择
const clearSelection = () => {
  currentValue.value = []
  emit('update:modelValue', [])
  emit('change', [])
  showPicker.value = false
  
  ElMessage.success('已清空选择')
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  currentValue.value = newValue || []
}, { immediate: true })

// 打开选择器
const openPicker = () => {
  if (!props.disabled) {
    showPicker.value = true
  }
}
</script>

<template>
  <div class="mobile-date-range-picker">
    <!-- 显示按钮 -->
    <div 
      class="picker-display"
      :class="{ 'disabled': disabled }"
      @click="openPicker"
    >
      <div class="display-content">
        <el-icon class="calendar-icon">
          <Calendar />
        </el-icon>
        <span class="display-text">{{ displayText }}</span>
        <el-icon 
          v-if="currentValue.length > 0" 
          class="clear-icon"
          @click.stop="clearSelection"
        >
          <Close />
        </el-icon>
      </div>
    </div>

    <!-- 月份选择对话框 -->
    <el-dialog
      v-model="showPicker"
      title="选择月份"
      :width="'90%'"
      :modal="true"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      class="mobile-month-picker-dialog"
    >
      <div class="picker-content">
        <div class="picker-header">
          <p>选择要查看的月份</p>
        </div>

        <div class="month-grid">
          <div
            v-for="option in monthOptions"
            :key="option.label"
            class="month-item"
            :class="{ 
              'selected': currentValue.length > 0 && currentValue[0] === option.value[0]
            }"
            @click="selectMonth(option)"
          >
            <div class="month-text">{{ option.label }}</div>
            <el-icon v-if="currentValue.length > 0 && currentValue[0] === option.value[0]" class="check-icon">
              <CircleCheck />
            </el-icon>
          </div>
        </div>

        <div class="picker-footer">
          <button 
            class="clear-btn"
            @click="clearSelection"
          >
            清空选择
          </button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
// iOS风格颜色变量
$ios-primary-color: #007AFF;
$ios-primary-light: #3395FF;
$ios-secondary-color: #34C759;
$ios-danger-color: #FF3B30;
$ios-gray-1: #F2F2F7;
$ios-gray-2: #E5E5EA;
$ios-gray-3: #C7C7CC;
$ios-gray-4: #8E8E93;
$ios-gray-5: #636366;
$ios-text-primary: #1C1C1E;
$ios-text-secondary: #8E8E93;
$ios-card-background: rgba(255, 255, 255, 0.95);
$ios-border-radius: 16px;
$ios-border-radius-small: 12px;

.mobile-date-range-picker {
  width: 100%;

  .picker-display {
    width: 100%;
    background: transparent;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: $ios-border-radius-small;
    transition: all 0.2s ease;
    height: 44px;
    box-sizing: border-box;
    cursor: pointer;

    &:hover {
      border-color: rgba(0, 0, 0, 0.2);
      background: rgba(255, 255, 255, 0.5);
    }

    &:active {
      transform: scale(0.98);
    }

    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .display-content {
      height: 100%;
      display: flex;
      align-items: center;
      padding: 0 12px;
      gap: 8px;

      .calendar-icon {
        color: $ios-primary-color;
        font-size: 16px;
        flex-shrink: 0;
      }

      .display-text {
        flex: 1;
        font-size: 16px;
        color: $ios-text-primary;
        font-weight: 500;
      }

      .clear-icon {
        color: $ios-gray-4;
        font-size: 16px;
        cursor: pointer;
        flex-shrink: 0;
        padding: 4px;
        border-radius: 50%;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(142, 142, 147, 0.1);
          color: $ios-gray-5;
        }
      }
    }
  }

  .picker-content {
    .picker-header {
      text-align: center;
      margin-bottom: 20px;

      p {
        margin: 0;
        font-size: 14px;
        color: $ios-text-secondary;
        font-weight: 500;
      }
    }

    .month-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      margin-bottom: 20px;

      .month-item {
        background: $ios-gray-1;
        border-radius: $ios-border-radius-small;
        padding: 16px 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 2px solid transparent;

        &:hover {
          background: $ios-gray-2;
        }

        &:active {
          transform: scale(0.95);
        }

        &.selected {
          background: rgba(0, 122, 255, 0.1);
          border-color: $ios-primary-color;

          .month-text {
            color: $ios-primary-color;
            font-weight: 600;
          }

          .check-icon {
            color: $ios-primary-color;
          }
        }

        .month-text {
          font-size: 16px;
          font-weight: 500;
          color: $ios-text-primary;
          transition: all 0.2s ease;
        }

        .check-icon {
          font-size: 18px;
          transition: all 0.2s ease;
        }
      }
    }

    .picker-footer {
      text-align: center;

      .clear-btn {
        width: 100%;
        height: 44px;
        border-radius: $ios-border-radius-small;
        font-size: 16px;
        font-weight: 500;
        background: $ios-gray-1;
        color: $ios-text-primary;
        border: 1px solid $ios-gray-2;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: $ios-gray-2;
        }

        &:active {
          transform: scale(0.98);
        }
      }
    }
  }
}

// Element Plus 对话框样式覆盖
:deep(.mobile-month-picker-dialog) {
  .el-dialog {
    border-radius: $ios-border-radius !important;
    margin-bottom: 10vh !important;
  }
  
  .el-dialog__header {
    text-align: center;
    padding: 20px 20px 10px !important;
    
    .el-dialog__title {
      font-size: 20px !important;
      font-weight: 600 !important;
      color: $ios-text-primary !important;
    }
  }
  
  .el-dialog__body {
    padding: 10px 20px 20px !important;
  }
  
  .el-dialog__close {
    font-size: 18px !important;
    color: $ios-gray-4 !important;
    
    &:hover {
      color: $ios-gray-5 !important;
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  :deep(.mobile-month-picker-dialog) {
    .el-dialog {
      width: 95% !important;
      margin: 5vh auto !important;
    }
  }
}
</style> 