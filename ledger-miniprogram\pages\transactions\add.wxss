/* pages/transactions/add.wxss */
.add-transaction-page {
  min-height: 100vh;
  background-color: #F2F2F7;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background-color: #FFFFFF;
  border-bottom: 1rpx solid #E5E5EA;
}

.header-left {
  display: flex;
  align-items: center;
  color: #007AFF;
  font-size: 32rpx;
}

.back-icon {
  font-size: 48rpx;
  margin-right: 8rpx;
  font-weight: 300;
}

.back-text {
  font-size: 32rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

.header-right {
  width: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  font-size: 32rpx;
  color: #8E8E93;
}

/* 表单容器 */
.form-container {
  padding: 40rpx 32rpx;
}

.form-group {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
}

.form-label {
  padding: 32rpx 32rpx 16rpx 32rpx;
  font-size: 28rpx;
  color: #8E8E93;
  font-weight: 500;
}

.form-input-container {
  position: relative;
  padding: 0 32rpx 32rpx 32rpx;
}

.form-input {
  width: 100%;
  font-size: 34rpx;
  color: #000000;
  background: transparent;
  border: none;
  outline: none;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #E5E5EA;
}

.form-input:focus {
  border-bottom-color: #007AFF;
}

.form-input.amount-input {
  padding-right: 60rpx;
}

.currency-symbol {
  position: absolute;
  right: 32rpx;
  bottom: 48rpx;
  font-size: 34rpx;
  color: #8E8E93;
}

.form-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 32rpx 32rpx 32rpx;
  cursor: pointer;
}

.picker-text {
  font-size: 34rpx;
  color: #000000;
}

.picker-text.placeholder {
  color: #C7C7CC;
}

.picker-arrow {
  font-size: 32rpx;
  color: #C7C7CC;
  font-weight: 300;
}

/* 底部按钮 */
.form-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background-color: #FFFFFF;
  border-top: 1rpx solid #E5E5EA;
}

.btn-primary {
  width: 100%;
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 32rpx;
  font-size: 36rpx;
  font-weight: 600;
}

.btn-primary:disabled {
  background-color: #C7C7CC;
  color: #FFFFFF;
}
