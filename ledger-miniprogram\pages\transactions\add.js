// pages/transactions/add.js
const api = require('../../utils/api.js')

Page({
  data: {
    form: {
      transaction_type: 'expense',
      amount: '',
      category_id: '',
      account_id: '',
      from_account_id: '',
      to_account_id: '',
      description: '',
      transaction_date: ''
    },
    transactionTypes: [
      { value: 'income', label: '收入', icon: '💰' },
      { value: 'expense', label: '支出', icon: '💸' },
      { value: 'transfer', label: '转账', icon: '🔄' }
    ],
    categories: [],
    accounts: [],
    showTypePicker: false,
    showCategoryPicker: false,
    showAccountPicker: false,
    showFromAccountPicker: false,
    showToAccountPicker: false,
    showDatePicker: false,
    submitting: false,
    loading: true,
    // 显示字段
    typeDisplay: '',
    categoryDisplay: '',
    accountDisplay: '',
    fromAccountDisplay: '',
    toAccountDisplay: '',
    canSubmit: false
  },

  onLoad(options) {
    console.log('添加交易页面加载', options)
    
    // 设置默认日期为今天
    const today = new Date()
    const dateStr = today.toISOString().split('T')[0]
    
    this.setData({
      'form.transaction_date': dateStr
    })

    // 如果有传入的类型，设置默认类型
    if (options.type) {
      this.setData({
        'form.transaction_type': options.type
      })
    }

    // 如果有传入的账户ID，设置默认账户
    if (options.account_id) {
      this.setData({
        'form.account_id': parseInt(options.account_id)
      })
    }

    this.loadInitialData()
  },

  // 加载初始数据
  async loadInitialData() {
    try {
      this.setData({ loading: true })
      
      // 并行加载分类和账户数据
      const [categories, accounts] = await Promise.all([
        api.categories.getList(),
        api.accounts.getList()
      ])

      console.log('加载的分类:', categories)
      console.log('加载的账户:', accounts)

      // 处理分类数据，添加emoji图标
      const categoriesWithEmoji = (categories || []).map(category => ({
        ...category,
        emoji: this.getEmojiIcon(category.icon)
      }))

      this.setData({
        categories: categories || [],
        categoriesWithEmoji: categoriesWithEmoji,
        accounts: accounts || []
      })

      this.updateComputedData()

    } catch (error) {
      console.error('加载初始数据失败:', error)
      wx.showToast({
        title: '加载数据失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 更新计算属性
  updateComputedData() {
    const { transaction_type, amount, account_id, category_id, from_account_id, to_account_id } = this.data.form

    // 检查表单是否可以提交
    let canSubmit = false
    if (transaction_type === 'transfer') {
      canSubmit = amount && from_account_id && to_account_id
    } else {
      // 分类字段可选，只要有金额和账户就可以提交
      canSubmit = amount && account_id
    }

    // 获取显示标签
    const typeObj = this.data.transactionTypes.find(t => t.value === transaction_type)
    const categoryObj = this.data.categories.find(c => c.id === category_id)
    const accountObj = this.data.accounts.find(a => a.id === account_id)
    const fromAccountObj = this.data.accounts.find(a => a.id === from_account_id)
    const toAccountObj = this.data.accounts.find(a => a.id === to_account_id)

    this.setData({
      canSubmit,
      typeDisplay: typeObj?.label || '',
      categoryDisplay: categoryObj?.name || '',
      accountDisplay: accountObj?.account_name || '',
      fromAccountDisplay: fromAccountObj?.account_name || '',
      toAccountDisplay: toAccountObj?.account_name || ''
    })
  },

  // 输入处理
  onAmountInput(e) {
    let value = e.detail.value
    // 验证数字格式
    if (value && !/^\d*\.?\d{0,2}$/.test(value)) {
      return
    }
    this.setData({
      'form.amount': value
    })
    this.updateComputedData()
  },

  onDescriptionInput(e) {
    this.setData({
      'form.description': e.detail.value
    })
  },

  onDateChange(e) {
    this.setData({
      'form.transaction_date': e.detail.value
    })
  },

  // 选择器控制
  showTypePicker() {
    this.setData({ showTypePicker: true })
  },

  hideTypePicker() {
    this.setData({ showTypePicker: false })
  },

  showCategoryPicker() {
    this.setData({ showCategoryPicker: true })
  },

  hideCategoryPicker() {
    this.setData({ showCategoryPicker: false })
  },

  showAccountPicker() {
    this.setData({ showAccountPicker: true })
  },

  hideAccountPicker() {
    this.setData({ showAccountPicker: false })
  },

  showFromAccountPicker() {
    this.setData({ showFromAccountPicker: true })
  },

  hideFromAccountPicker() {
    this.setData({ showFromAccountPicker: false })
  },

  showToAccountPicker() {
    this.setData({ showToAccountPicker: true })
  },

  hideToAccountPicker() {
    this.setData({ showToAccountPicker: false })
  },

  // 选择处理
  selectType(e) {
    const value = e.detail.value

    // 重置相关字段
    const resetData = {
      'form.transaction_type': value,
      'form.category_id': '',
      'form.account_id': '',
      'form.from_account_id': '',
      'form.to_account_id': '',
      showTypePicker: false
    }

    this.setData(resetData)
    this.updateComputedData()
  },

  selectCategory(e) {
    const value = e.detail.value
    this.setData({
      'form.category_id': value,
      showCategoryPicker: false
    })
    this.updateComputedData()
  },

  selectAccount(e) {
    const value = e.detail.value
    this.setData({
      'form.account_id': value,
      showAccountPicker: false
    })
    this.updateComputedData()
  },

  selectFromAccount(e) {
    const value = e.detail.value
    this.setData({
      'form.from_account_id': value,
      showFromAccountPicker: false
    })
    this.updateComputedData()
  },

  selectToAccount(e) {
    const value = e.detail.value
    this.setData({
      'form.to_account_id': value,
      showToAccountPicker: false
    })
    this.updateComputedData()
  },

  // 提交表单
  async saveTransaction() {
    if (this.data.submitting || !this.data.canSubmit) {
      return
    }

    this.setData({ submitting: true })

    try {
      const transactionData = {
        transaction_type: this.data.form.transaction_type,
        amount: parseFloat(this.data.form.amount),
        description: this.data.form.description || '',
        transaction_date: this.data.form.transaction_date
      }

      // 根据交易类型设置不同的字段
      if (this.data.form.transaction_type === 'transfer') {
        // 转账时使用转出和转入账户
        transactionData.from_account_id = this.data.form.from_account_id
        transactionData.to_account_id = this.data.form.to_account_id
        // 转账时也需要设置account_id为转出账户ID
        transactionData.account_id = this.data.form.from_account_id
      } else {
        // 收入/支出时使用普通账户
        transactionData.account_id = this.data.form.account_id

        // 只有选择了分类才添加category_id字段
        if (this.data.form.category_id) {
          transactionData.category_id = this.data.form.category_id
        }
      }

      console.log('准备发送的交易数据:', transactionData)

      const result = await api.transactions.create(transactionData)
      console.log('交易创建成功:', result)

      wx.showToast({
        title: '交易添加成功',
        icon: 'success'
      })

      // 返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)

    } catch (error) {
      console.error('创建交易失败:', error)
      wx.showToast({
        title: error.message || '添加失败',
        icon: 'none',
        duration: 3000
      })
    } finally {
      this.setData({ submitting: false })
    }
  },

  // 返回
  goBack() {
    wx.navigateBack()
  },

  // 将英文图标名称转换为emoji
  getEmojiIcon(iconName) {
    const iconMap = {
      'Food': '🍽️',
      'Coffee': '☕',
      'ShoppingCart': '🛍️',
      'Van': '🚗',
      'Suitcase': '🧳',
      'Present': '🎁',
      'Medal': '🏅',
      'Cellphone': '📱',
      'School': '📚',
      'House': '🏠',
      'Bell': '🔔',
      'VideoPlay': '🎮',
      'Headset': '🎧',
      'Basketball': '🏀',
      'OfficeBuilding': '🏢',
      'Lightning': '⚡',
      'Money': '💰',
      'Wallet': '👛',
      'Watch': '⌚',
      'DataAnalysis': '📊',
      'Promotion': '📈'
    }
    return iconMap[iconName] || '📝'
  }
})
