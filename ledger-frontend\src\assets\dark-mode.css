/* 深色模式样式 - 完全重写 */
/* 基础变量 */
:root {
  --bg-color: #ffffff;
  --text-color: #333333;
  --card-bg: #ffffff;
  --border-color: #e4e7ed;
  --sidebar-bg: #ffffff;
  --header-bg: #ffffff;
  --hover-bg: #f5f7fa;
  
  /* Element Plus 变量 */
  --el-color-primary: #1e88e5;
  --el-fill-color-blank: #ffffff;
  --el-bg-color: #ffffff;
  --el-text-color-primary: #303133;
  --el-border-color: #dcdfe6;
}

/* 全局样式重置 - 深色模式 */
.dark-mode {
  --bg-color: #1e1e2e;
  --text-color: #e2e2e6;
  --card-bg: #282838;
  --border-color: #363646;
  --sidebar-bg: #1e1e2e;
  --header-bg: #282838;
  --hover-bg: #313142;
  
  /* Element Plus 变量覆盖 */
  --el-fill-color-blank: #282838;
  --el-bg-color: #282838;
  --el-text-color-primary: #e2e2e6;
  --el-border-color: #363646;
  --el-border-color-light: #363646;
  --el-text-color-regular: #e2e2e6;
  
  /* 全局应用深色背景和文字颜色 */
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
}

/* 确保所有重要元素都应用正确的背景色 */
.dark-mode body,
.dark-mode #app,
.dark-mode .app-container,
.dark-mode .main-container,
.dark-mode .app-layout,
.dark-mode .content,
.dark-mode .el-main,
.dark-mode .el-container {
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
}

/* 卡片和面板样式 */
.dark-mode .el-card,
.dark-mode .el-card__header,
.dark-mode .el-card__body,
.dark-mode .settings-section,
.dark-mode .settings-container,
.dark-mode .settings-container > * {
  background-color: var(--card-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

/* 表单和输入框 */
.dark-mode .el-form,
.dark-mode .el-form-item,
.dark-mode .el-form-item__label,
.dark-mode .el-form-item__content {
  background-color: var(--card-bg) !important;
  color: var(--text-color) !important;
}

.dark-mode .el-input__wrapper,
.dark-mode .el-textarea__wrapper {
  background-color: #313142 !important;
  box-shadow: 0 0 0 1px var(--border-color) inset !important;
}

.dark-mode .el-input__inner,
.dark-mode .el-textarea__inner {
  background-color: transparent !important;
  color: var(--text-color) !important;
}

/* 下拉菜单和选择器 */
.dark-mode .el-select-dropdown,
.dark-mode .el-select-dropdown__item,
.dark-mode .el-dropdown-menu,
.dark-mode .el-dropdown-menu__item {
  background-color: var(--card-bg) !important;
  color: var(--text-color) !important;
}

.dark-mode .el-select-dropdown__item.selected,
.dark-mode .el-select-dropdown__item.hover,
.dark-mode .el-select-dropdown__item:hover,
.dark-mode .el-dropdown-menu__item:hover {
  background-color: var(--hover-bg) !important;
}

.dark-mode .el-select__wrapper {
  background-color: #313142 !important;
}

/* 按钮样式 */
.dark-mode .el-button--default {
  background-color: #313142 !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

.dark-mode .el-button--default:hover {
  background-color: #3a3a4c !important;
}

/* 全面增强按钮文字在深色模式下的可见性 */
.dark-mode .el-button {
  color: #e2e2e6 !important;
  border-color: #363646 !important;
}

.dark-mode .el-button--primary {
  color: #ffffff !important;
  background-color: #409eff !important;
  border-color: #409eff !important;
}

.dark-mode .el-button--success {
  color: #ffffff !important;
  background-color: #67c23a !important;
  border-color: #67c23a !important;
}

.dark-mode .el-button--warning {
  color: #ffffff !important;
  background-color: #e6a23c !important;
  border-color: #e6a23c !important;
}

.dark-mode .el-button--danger {
  color: #ffffff !important;
  background-color: #f56c6c !important;
  border-color: #f56c6c !important;
}

.dark-mode .el-button--info {
  color: #ffffff !important;
  background-color: #909399 !important;
  border-color: #909399 !important;
}

/* Plain按钮样式调整 */
.dark-mode .el-button--primary.is-plain {
  color: #a0cfff !important;
  background-color: #18223d !important;
  border-color: #409eff !important;
}

.dark-mode .el-button--success.is-plain {
  color: #b3e19d !important;
  background-color: #1c2c1b !important;
  border-color: #67c23a !important;
}

.dark-mode .el-button--warning.is-plain {
  color: #f3d19e !important;
  background-color: #2e261a !important;
  border-color: #e6a23c !important;
}

.dark-mode .el-button--danger.is-plain {
  color: #fab6b6 !important;
  background-color: #2c1a1a !important;
  border-color: #f56c6c !important;
}

/* 按钮悬停样式 */
.dark-mode .el-button:hover,
.dark-mode .el-button:focus {
  color: #409eff !important;
  border-color: #409eff !important;
  background-color: #18223d !important;
}

.dark-mode .el-button--primary:hover,
.dark-mode .el-button--primary:focus {
  background-color: #66b1ff !important;
  border-color: #66b1ff !important;
  color: #ffffff !important;
}

.dark-mode .el-button--success:hover,
.dark-mode .el-button--success:focus {
  background-color: #85ce61 !important;
  border-color: #85ce61 !important;
  color: #ffffff !important;
}

.dark-mode .el-button--warning:hover,
.dark-mode .el-button--warning:focus {
  background-color: #ebb563 !important;
  border-color: #ebb563 !important;
  color: #ffffff !important;
}

.dark-mode .el-button--danger:hover,
.dark-mode .el-button--danger:focus {
  background-color: #f78989 !important;
  border-color: #f78989 !important;
  color: #ffffff !important;
}

/* Plain按钮悬停样式 */
.dark-mode .el-button--primary.is-plain:hover,
.dark-mode .el-button--primary.is-plain:focus {
  color: #ffffff !important;
  background-color: #409eff !important;
}

.dark-mode .el-button--success.is-plain:hover,
.dark-mode .el-button--success.is-plain:focus {
  color: #ffffff !important;
  background-color: #67c23a !important;
}

.dark-mode .el-button--warning.is-plain:hover,
.dark-mode .el-button--warning.is-plain:focus {
  color: #ffffff !important;
  background-color: #e6a23c !important;
}

.dark-mode .el-button--danger.is-plain:hover,
.dark-mode .el-button--danger.is-plain:focus {
  color: #ffffff !important;
  background-color: #f56c6c !important;
}

/* 确保文字标签和标题可见 */
.dark-mode .el-form-item__label,
.dark-mode h1, 
.dark-mode h2, 
.dark-mode h3, 
.dark-mode h4, 
.dark-mode h5, 
.dark-mode h6,
.dark-mode .section-header h2,
.dark-mode .page-title,
.dark-mode label,
.dark-mode .el-form-item > label,
.dark-mode span,
.dark-mode p,
.dark-mode div {
  color: var(--text-color) !important;
}

/* 导航菜单 */
.dark-mode .el-menu,
.dark-mode .sidebar,
.dark-mode .el-aside {
  background-color: var(--sidebar-bg) !important;
  border-color: var(--border-color) !important;
}

.dark-mode .el-menu-item {
  color: var(--text-color) !important;
}

.dark-mode .el-menu-item.is-active {
  color: var(--el-color-primary) !important;
}

.dark-mode .el-menu-item:hover {
  background-color: var(--hover-bg) !important;
}

/* 标题和描述 */
.dark-mode .section-desc {
  color: #a0a0b0 !important;
}

/* 开关 */
.dark-mode .el-switch__core {
  background-color: #464658 !important;
}

/* 强制所有滚动区域背景 */
.dark-mode ::-webkit-scrollbar-track {
  background-color: var(--bg-color) !important;
}

.dark-mode ::-webkit-scrollbar-thumb {
  background-color: #464658 !important;
}

/* 确保覆盖所有可能的白色背景 */
.dark-mode .app-container > *,
.dark-mode .settings-container > *,
.dark-mode .el-card > *,
.dark-mode .el-form > * {
  background-color: inherit !important;
}

/* 特定页面的特殊处理 */
.dark-mode .system-settings-panel,
.dark-mode .password-change-panel,
.dark-mode .user-profile-panel,
.dark-mode .data-backup-panel,
.dark-mode .danger-zone {
  background-color: var(--card-bg) !important;
  color: var(--text-color) !important;
}

/* 最上层固定元素 */
body.dark-mode {
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
}

html.dark-mode {
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
} 

/* 移动端交易对话框深色模式样式 */
@media (max-width: 768px) {
  .dark-mode.mobile-transaction-dialog .el-dialog,
  .dark-mode .mobile-transaction-dialog .el-dialog {
    background: #282838 !important;
    border: 1px solid #363646 !important;
  }
  
  .dark-mode.mobile-transaction-dialog .el-dialog__header,
  .dark-mode .mobile-transaction-dialog .el-dialog__header {
    background: #282838 !important;
    border-bottom: 1px solid #363646 !important;
  }
  
  .dark-mode.mobile-transaction-dialog .el-dialog__title,
  .dark-mode .mobile-transaction-dialog .el-dialog__title {
    color: #e2e2e6 !important;
  }
  
  .dark-mode.mobile-transaction-dialog .el-dialog__body,
  .dark-mode .mobile-transaction-dialog .el-dialog__body {
    background: #282838 !important;
  }
  
  .dark-mode.mobile-transaction-dialog .el-form-item__label,
  .dark-mode .mobile-transaction-dialog .el-form-item__label {
    color: #e2e2e6 !important;
  }
  
  .dark-mode.mobile-transaction-dialog .el-input__wrapper,
  .dark-mode.mobile-transaction-dialog .el-select .el-input__wrapper,
  .dark-mode.mobile-transaction-dialog .el-textarea__inner,
  .dark-mode .mobile-transaction-dialog .el-input__wrapper,
  .dark-mode .mobile-transaction-dialog .el-select .el-input__wrapper,
  .dark-mode .mobile-transaction-dialog .el-textarea__inner {
    background: #1e1e2e !important;
    border: 1px solid #363646 !important;
    color: #e2e2e6 !important;
  }
  
  .dark-mode.mobile-transaction-dialog .el-input__wrapper:hover,
  .dark-mode.mobile-transaction-dialog .el-select .el-input__wrapper:hover,
  .dark-mode .mobile-transaction-dialog .el-input__wrapper:hover,
  .dark-mode .mobile-transaction-dialog .el-select .el-input__wrapper:hover {
    border-color: #007AFF !important;
  }
  
  .dark-mode.mobile-transaction-dialog .el-input__inner,
  .dark-mode.mobile-transaction-dialog .el-textarea__inner,
  .dark-mode .mobile-transaction-dialog .el-input__inner,
  .dark-mode .mobile-transaction-dialog .el-textarea__inner {
    background: transparent !important;
    color: #e2e2e6 !important;
  }
  
  .dark-mode.mobile-transaction-dialog .account-option .account-type,
  .dark-mode .mobile-transaction-dialog .account-option .account-type {
    background: #363646 !important;
    color: #b3b3b6 !important;
  }
  
  .dark-mode.mobile-transaction-dialog .dialog-bottom-actions,
  .dark-mode .mobile-transaction-dialog .dialog-bottom-actions {
    background: #282838 !important;
    border-top: 1px solid #363646 !important;
  }
  
  .dark-mode.mobile-transaction-dialog .cancel-btn,
  .dark-mode .mobile-transaction-dialog .cancel-btn {
    background: #363646 !important;
    color: #b3b3b6 !important;
  }
  
  .dark-mode.mobile-transaction-dialog .cancel-btn:active,
  .dark-mode .mobile-transaction-dialog .cancel-btn:active {
    background: #4a4a5a !important;
  }
  
  .dark-mode.mobile-transaction-dialog .highlight-form-item .el-form-item__label,
  .dark-mode .mobile-transaction-dialog .highlight-form-item .el-form-item__label {
    color: #007AFF !important;
  }
  
  .dark-mode.mobile-transaction-dialog .el-dialog__body::-webkit-scrollbar-thumb,
  .dark-mode .mobile-transaction-dialog .el-dialog__body::-webkit-scrollbar-thumb {
    background: #4a4a5a !important;
  }
  
  .dark-mode.mobile-transaction-dialog .el-dialog__body::-webkit-scrollbar-thumb:hover,
  .dark-mode .mobile-transaction-dialog .el-dialog__body::-webkit-scrollbar-thumb:hover {
    background: #5a5a6a !important;
  }
} 

/* 移动端添加账户对话框深色模式样式 */
@media (max-width: 768px) {
  .dark-mode.mobile-account-dialog .el-dialog,
  .dark-mode .mobile-account-dialog .el-dialog {
    background: #282838 !important;
    border: 1px solid #363646 !important;
  }
  
  .dark-mode.mobile-account-dialog .el-dialog__header,
  .dark-mode .mobile-account-dialog .el-dialog__header {
    background: #282838 !important;
    border-bottom: 1px solid #363646 !important;
  }
  
  .dark-mode.mobile-account-dialog .el-dialog__title,
  .dark-mode .mobile-account-dialog .el-dialog__title {
    color: #e2e2e6 !important;
  }
  
  .dark-mode.mobile-account-dialog .el-dialog__body,
  .dark-mode .mobile-account-dialog .el-dialog__body {
    background: #282838 !important;
  }
  
  .dark-mode.mobile-account-dialog .el-form-item__label,
  .dark-mode .mobile-account-dialog .el-form-item__label {
    color: #e2e2e6 !important;
  }
  
  .dark-mode.mobile-account-dialog .el-input__wrapper,
  .dark-mode.mobile-account-dialog .el-select .el-input__wrapper,
  .dark-mode .mobile-account-dialog .el-input__wrapper,
  .dark-mode .mobile-account-dialog .el-select .el-input__wrapper {
    background: #1e1e2e !important;
    border: 1px solid #363646 !important;
    color: #e2e2e6 !important;
  }
  
  .dark-mode.mobile-account-dialog .el-input__wrapper:hover,
  .dark-mode.mobile-account-dialog .el-select .el-input__wrapper:hover,
  .dark-mode .mobile-account-dialog .el-input__wrapper:hover,
  .dark-mode .mobile-account-dialog .el-select .el-input__wrapper:hover {
    border-color: #007AFF !important;
  }
  
  .dark-mode.mobile-account-dialog .el-input__inner,
  .dark-mode .mobile-account-dialog .el-input__inner {
    background: transparent !important;
    color: #e2e2e6 !important;
  }
  
  .dark-mode.mobile-account-dialog .dialog-bottom-actions,
  .dark-mode .mobile-account-dialog .dialog-bottom-actions {
    background: #282838 !important;
    border-top: 1px solid #363646 !important;
  }
  
  .dark-mode.mobile-account-dialog .cancel-btn,
  .dark-mode .mobile-account-dialog .cancel-btn {
    background: #363646 !important;
    color: #b3b3b6 !important;
  }
  
  .dark-mode.mobile-account-dialog .cancel-btn:active,
  .dark-mode .mobile-account-dialog .cancel-btn:active {
    background: #4a4a5a !important;
  }
  
  .dark-mode.mobile-account-dialog .el-dialog__body::-webkit-scrollbar-thumb,
  .dark-mode .mobile-account-dialog .el-dialog__body::-webkit-scrollbar-thumb {
    background: #4a4a5a !important;
  }
  
  .dark-mode.mobile-account-dialog .el-dialog__body::-webkit-scrollbar-thumb:hover,
  .dark-mode .mobile-account-dialog .el-dialog__body::-webkit-scrollbar-thumb:hover {
    background: #5a5a6a !important;
  }
} 