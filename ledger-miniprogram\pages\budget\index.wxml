<!-- 预算管理页面 -->
<view class="budget-page">
  <!-- 导航栏 -->
  <view class="nav-header" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-content">
      <view class="nav-back" catchtap="goBack" data-test="back-button">
        <text class="back-icon">‹</text>
      </view>
      <text class="nav-title">预算管理</text>
      <view class="nav-placeholder"></view>
    </view>
  </view>

  <!-- 调试：临时返回按钮 -->
  <view style="padding: 20rpx; text-align: center; background: #fff; margin: 20rpx;">
    <button bindtap="goBack" style="background: #007AFF; color: white; border: none; padding: 20rpx; border-radius: 10rpx;">
      测试返回按钮
    </button>
  </view>

  <!-- 月份选择 -->
  <view class="month-selector">
    <picker mode="date" fields="month" value="{{selectedMonth}}" bindchange="onMonthChange">
      <view class="month-picker">
        <text class="month-text">{{selectedMonthText}}</text>
        <text class="picker-arrow">▼</text>
      </view>
    </picker>
  </view>

  <!-- 预算总览 -->
  <view class="budget-overview">
    <view class="overview-card">
      <text class="overview-label">总预算</text>
      <text class="overview-value">¥{{totalBudget}}</text>
    </view>
    <view class="overview-card">
      <text class="overview-label">已使用</text>
      <text class="overview-value expense">¥{{totalSpent}}</text>
    </view>
    <view class="overview-card">
      <text class="overview-label">剩余</text>
      <text class="overview-value {{remaining >= 0 ? 'income' : 'expense'}}">
        ¥{{Math.abs(remaining).toFixed(2)}}
      </text>
    </view>
    <view class="overview-card">
      <text class="overview-label">使用率</text>
      <text class="overview-value {{usagePercentage <= 80 ? 'income' : usagePercentage <= 100 ? 'warning' : 'expense'}}">
        {{usagePercentage}}%
      </text>
    </view>
  </view>

  <!-- 预算列表 -->
  <view class="budget-list">
    <view wx:if="{{loading}}" class="loading">
      <text>加载中...</text>
    </view>

    <view wx:elif="{{budgets.length === 0}}" class="empty-state">
      <text class="empty-icon">📊</text>
      <text class="empty-text">暂无预算数据</text>
      <text class="empty-desc">点击右下角 + 号添加预算</text>
    </view>

    <view wx:else>
      <view class="budget-item" wx:for="{{budgets}}" wx:key="id">
        <view class="budget-info">
          <text class="budget-icon">{{item.icon}}</text>
          <view class="budget-details">
            <text class="budget-name">{{item.categoryName}}</text>
            <text class="budget-progress">¥{{item.spentText}} / ¥{{item.amountText}}</text>
          </view>
        </view>
        
        <view class="budget-status">
          <text class="usage-rate {{item.usagePercentage <= 80 ? 'safe' : item.usagePercentage <= 100 ? 'warning' : 'danger'}}">
            {{item.usagePercentage}}%
          </text>
          <view class="progress-bar">
            <view class="progress-fill {{item.usagePercentage <= 80 ? 'safe' : item.usagePercentage <= 100 ? 'warning' : 'danger'}}" 
                  style="width: {{Math.min(item.usagePercentage, 100)}}%"></view>
          </view>
        </view>
        
        <view class="budget-actions">
          <view class="action-btn edit" bindtap="editBudget" data-id="{{item.id}}">
            <text class="action-icon">✏️</text>
          </view>
          <view class="action-btn delete" bindtap="deleteBudget" data-id="{{item.id}}">
            <text class="action-icon">🗑️</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 添加/编辑预算弹窗 -->
  <view class="modal-overlay {{showModal ? 'show' : ''}}" bindtap="hideModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">{{editingBudget ? '编辑预算' : '添加预算'}}</text>
        <view class="modal-close" bindtap="hideModal">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="modal-body">
        <view class="form-item">
          <text class="form-label">分类</text>
          <picker bindchange="onCategoryChange" value="{{selectedCategoryIndex}}" range="{{categories}}" range-key="name">
            <view class="form-input">
              <text class="input-text">{{selectedCategory ? selectedCategory.name : '请选择分类'}}</text>
              <text class="input-arrow">▼</text>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="form-label">预算金额</text>
          <input class="form-input" type="digit" placeholder="请输入预算金额" 
                 value="{{budgetAmount}}" bindinput="onAmountInput" />
        </view>
      </view>
      
      <view class="modal-footer">
        <view class="modal-btn cancel" bindtap="hideModal">
          <text>取消</text>
        </view>
        <view class="modal-btn confirm" bindtap="saveBudget">
          <text>保存</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 浮动添加按钮 -->
  <view class="floating-add-btn" bindtap="showAddBudget">
    <text class="floating-add-icon">+</text>
  </view>
</view>
