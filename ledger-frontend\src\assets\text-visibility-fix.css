/**
 * 文本可见性修复
 * 专门解决深色模式下文本看不清的问题
 */

/* 普通文本元素可见性增强 */
.dark-mode p,
.dark-mode span,
.dark-mode div,
.dark-mode h1,
.dark-mode h2,
.dark-mode h3,
.dark-mode h4,
.dark-mode h5,
.dark-mode h6,
.dark-mode label,
.dark-mode .el-form-item__label {
  color: #e2e2e6 !important;
}

/* 按钮文本可见性增强 */
.dark-mode .el-button {
  color: #e2e2e6 !important;
}

/* Plain按钮特殊处理 */
.dark-mode .el-button--primary.is-plain {
  color: #a0cfff !important;
  background-color: rgba(64, 158, 255, 0.1) !important;
  border-color: rgba(64, 158, 255, 0.5) !important;
}

.dark-mode .el-button--success.is-plain {
  color: #b3e19d !important;
  background-color: rgba(103, 194, 58, 0.1) !important;
  border-color: rgba(103, 194, 58, 0.5) !important;
}

.dark-mode .el-button--warning.is-plain {
  color: #f3d19e !important;
  background-color: rgba(230, 162, 60, 0.1) !important;
  border-color: rgba(230, 162, 60, 0.5) !important;
}

.dark-mode .el-button--danger.is-plain {
  color: #fab6b6 !important;
  background-color: rgba(245, 108, 108, 0.1) !important;
  border-color: rgba(245, 108, 108, 0.5) !important;
}

.dark-mode .el-button--info.is-plain {
  color: #c8c9cc !important;
  background-color: rgba(144, 147, 153, 0.1) !important;
  border-color: rgba(144, 147, 153, 0.5) !important;
}

/* 账户管理页面特别处理 */
.dark-mode .account-card .account-actions .el-button {
  border-width: 2px !important;
}

.dark-mode .account-card .account-actions .el-button--primary.is-plain {
  color: #a0cfff !important;
  border-color: rgba(64, 158, 255, 0.7) !important;
  background-color: rgba(64, 158, 255, 0.1) !important;
}

.dark-mode .account-card .account-actions .el-button--success.is-plain {
  color: #b3e19d !important;
  border-color: rgba(103, 194, 58, 0.7) !important;
  background-color: rgba(103, 194, 58, 0.1) !important;
}

.dark-mode .account-card .account-actions .el-button--warning.is-plain {
  color: #f3d19e !important;
  border-color: rgba(230, 162, 60, 0.7) !important;
  background-color: rgba(230, 162, 60, 0.1) !important;
}

/* 链接文本增强 */
.dark-mode a {
  color: #409eff !important;
}

.dark-mode a:hover {
  color: #66b1ff !important;
}

/* 表单元素文本增强 */
.dark-mode .el-input__inner,
.dark-mode .el-textarea__inner,
.dark-mode .el-select-dropdown__item,
.dark-mode .el-dropdown-menu__item {
  color: #e2e2e6 !important;
}

/* 确保所有字体颜色至少有足够对比度 */
.dark-mode .text-muted,
.dark-mode .text-secondary,
.dark-mode .desc,
.dark-mode .description,
.dark-mode .helper,
.dark-mode .el-form-item__error {
  color: #a0a0b0 !important;
}

/* 确保卡片和容器中的文本清晰可见 */
.dark-mode .el-card,
.dark-mode .el-card__body {
  color: #e2e2e6 !important;
}

.dark-mode .el-card .el-button--text {
  color: #409eff !important;
}

/* 修复特定的按钮组合问题 */
.dark-mode .account-actions .el-button--primary.is-plain,
.dark-mode .account-actions .el-button--success.is-plain,
.dark-mode .account-actions .el-button--warning.is-plain {
  border-width: 1.5px !important; 
  font-weight: bold !important;
  text-shadow: 0px 0px 1px rgba(0, 0, 0, 0.5) !important;
}

/* 表格相关样式增强 */
.dark-mode .el-table {
  background-color: #282838 !important;
  color: #e2e2e6 !important;
}

.dark-mode .el-table th {
  background-color: #23232e !important;
  color: #e2e2e6 !important;
}

.dark-mode .el-table tr {
  background-color: #282838 !important;
  color: #e2e2e6 !important;
}

.dark-mode .el-table td {
  background-color: #282838 !important;
  color: #e2e2e6 !important;
  border-color: #363646 !important;
}

.dark-mode .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #2b2b3b !important;
}

.dark-mode .el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: #313142 !important;
}

/* 表格边框颜色 */
.dark-mode .el-table--border,
.dark-mode .el-table--group {
  border-color: #363646 !important;
}

.dark-mode .el-table--border th,
.dark-mode .el-table--border td {
  border-right-color: #363646 !important;
}

.dark-mode .el-table__fixed-right::before,
.dark-mode .el-table__fixed::before {
  background-color: #363646 !important;
}

/* 特别处理交易记录页面的元素 */
.dark-mode .transaction-list .transaction-item {
  background-color: #282838 !important;
  border-color: #363646 !important;
}

.dark-mode .transaction-list .transaction-item:hover {
  background-color: #313142 !important;
}

.dark-mode .el-table .cell {
  color: #e2e2e6 !important;
}

.dark-mode .el-table .el-button {
  color: #a0cfff !important;
}

/* 日期选择器和输入框 */
.dark-mode .el-date-editor.el-input,
.dark-mode .el-date-editor.el-input__inner {
  background-color: #313142 !important;
}

.dark-mode .el-date-editor .el-input__inner {
  color: #e2e2e6 !important;
  background-color: #313142 !important;
}

.dark-mode .el-input__prefix,
.dark-mode .el-input__suffix {
  color: #909399 !important;
}

.dark-mode .el-date-table th {
  color: #e2e2e6 !important;
}

/* 修复筛选下拉菜单 */
.dark-mode .el-select {
  background-color: transparent !important;
}

.dark-mode .el-select .el-input__inner {
  background-color: #313142 !important;
  color: #e2e2e6 !important;
}

.dark-mode .el-select-dropdown {
  background-color: #282838 !important;
  border-color: #363646 !important;
}

.dark-mode .el-select-dropdown__item {
  color: #e2e2e6 !important;
}

.dark-mode .el-select-dropdown__item.hover,
.dark-mode .el-select-dropdown__item:hover {
  background-color: #313142 !important;
}

/* 修复高级搜索框 */
.dark-mode .search-input .el-input__inner {
  background-color: #313142 !important;
  color: #e2e2e6 !important;
  border-color: #363646 !important;
}

/* 修复分页控件 */
.dark-mode .el-pagination {
  color: #e2e2e6 !important;
  background-color: #282838 !important;
}

.dark-mode .el-pagination .btn-next,
.dark-mode .el-pagination .btn-prev {
  background-color: #313142 !important;
  color: #e2e2e6 !important;
}

.dark-mode .el-pagination .el-input__inner {
  background-color: #313142 !important;
  color: #e2e2e6 !important;
  border-color: #363646 !important;
}

.dark-mode .el-pager li {
  background-color: #313142 !important;
  color: #e2e2e6 !important;
}

.dark-mode .el-pager li.active {
  color: #409eff !important;
}

/* 修复标签页 */
.dark-mode .el-tabs__item {
  color: #a0a0b0 !important;
}

.dark-mode .el-tabs__item.is-active {
  color: #409eff !important;
}

.dark-mode .el-tabs__active-bar {
  background-color: #409eff !important;
}

.dark-mode .el-tabs__nav-wrap::after {
  background-color: #363646 !important;
}

/* 修复财务分析页面元素 */
.dark-mode .filter-bar,
.dark-mode .chart-controls,
.dark-mode .data-summary {
  background-color: #282838 !important;
  color: #e2e2e6 !important;
  border-color: #363646 !important;
}

.dark-mode .el-radio-button__inner {
  background-color: #282838 !important;
  color: #e2e2e6 !important;
  border-color: #363646 !important;
}

.dark-mode .el-radio-button__original-radio:checked + .el-radio-button__inner {
  background-color: #409eff !important;
  color: #ffffff !important;
  border-color: #409eff !important;
}

/* 修复图表工具提示 */
.dark-mode .el-tooltip__popper {
  background-color: #282838 !important;
  color: #e2e2e6 !important;
  border-color: #363646 !important;
}

.dark-mode .el-tooltip__popper[x-placement^=top] .popper__arrow::after {
  border-top-color: #282838 !important;
}

.dark-mode .el-tooltip__popper[x-placement^=bottom] .popper__arrow::after {
  border-bottom-color: #282838 !important;
}

/* 修复表格中的操作按钮 */
.dark-mode .el-table .el-button--primary {
  color: #409eff !important;
}

.dark-mode .el-table .el-button--danger {
  color: #f56c6c !important;
} 