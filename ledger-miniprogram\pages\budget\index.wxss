/* pages/budget/index.wxss */
.budget-page {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding-bottom: 32rpx;
}

/* 导航栏 */
.nav-header {
  background-color: #FFFFFF;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #E5E5EA;
}

.nav-back {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F2F2F7;
  border-radius: 32rpx;
  transition: all 0.2s ease;
}

.nav-back:active {
  background-color: #E5E5EA;
  transform: scale(0.95);
}

.back-icon {
  font-size: 48rpx;
  color: #000000;
  font-weight: bold;
  line-height: 1;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

/* 浮动添加按钮 */
.floating-add-btn {
  position: fixed;
  right: 32rpx;
  bottom: 120rpx;
  width: 112rpx;
  height: 112rpx;
  background-color: #007AFF;
  border-radius: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);
  z-index: 100;
  transition: all 0.3s ease;
}

.floating-add-btn:active {
  transform: scale(0.95);
  background-color: #0056CC;
}

.floating-add-icon {
  font-size: 48rpx;
  color: white;
  font-weight: 300;
  line-height: 1;
}

/* 月份选择 */
.month-selector {
  background-color: #FFFFFF;
  padding: 32rpx;
  margin-bottom: 32rpx;
  text-align: center;
}

.month-picker {
  display: inline-flex;
  align-items: center;
  background-color: #F2F2F7;
  border-radius: 16rpx;
  padding: 16rpx 32rpx;
}

.month-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  margin-right: 16rpx;
}

.picker-arrow {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 预算总览 */
.budget-overview {
  display: flex;
  padding: 0 32rpx;
  margin-bottom: 32rpx;
}

.overview-card {
  flex: 1;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx 16rpx;
  margin-right: 16rpx;
  text-align: center;
}

.overview-card:last-child {
  margin-right: 0;
}

.overview-label {
  display: block;
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 12rpx;
}

.overview-value {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #000000;
}

.overview-value.income {
  color: #34C759;
}

.overview-value.expense {
  color: #FF3B30;
}

.overview-value.warning {
  color: #FF9500;
}

/* 预算列表 */
.budget-list {
  padding: 0 32rpx;
}

.loading {
  text-align: center;
  padding: 80rpx 0;
  color: #8E8E93;
  font-size: 30rpx;
}

.empty-state {
  text-align: center;
  padding: 80rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #8E8E93;
  margin-bottom: 16rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #C7C7CC;
}

.budget-item {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}

.budget-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.budget-icon {
  font-size: 36rpx;
  margin-right: 24rpx;
}

.budget-details {
  flex: 1;
}

.budget-name {
  display: block;
  font-size: 32rpx;
  color: #000000;
  margin-bottom: 8rpx;
}

.budget-progress {
  display: block;
  font-size: 26rpx;
  color: #8E8E93;
}

.budget-status {
  width: 120rpx;
  margin-right: 24rpx;
  text-align: right;
}

.usage-rate {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.usage-rate.safe {
  color: #34C759;
}

.usage-rate.warning {
  color: #FF9500;
}

.usage-rate.danger {
  color: #FF3B30;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #E5E5EA;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-fill.safe {
  background-color: #34C759;
}

.progress-fill.warning {
  background-color: #FF9500;
}

.progress-fill.danger {
  background-color: #FF3B30;
}

.budget-actions {
  display: flex;
  align-items: center;
}

.action-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
}

.action-btn.edit {
  background-color: #007AFF;
}

.action-btn.delete {
  background-color: #FF3B30;
}

.action-icon {
  font-size: 24rpx;
  color: white;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  width: 80%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  font-size: 36rpx;
  color: #8E8E93;
}

.modal-body {
  padding: 32rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #000000;
  margin-bottom: 16rpx;
}

.form-input {
  background-color: #F2F2F7;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 30rpx;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.input-text {
  flex: 1;
}

.input-arrow {
  font-size: 24rpx;
  color: #8E8E93;
}

.modal-footer {
  padding: 32rpx;
  border-top: 1rpx solid #E5E5EA;
  display: flex;
  justify-content: space-between;
}

.modal-btn {
  flex: 1;
  padding: 24rpx 0;
  text-align: center;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 600;
}

.modal-btn.cancel {
  background-color: #F2F2F7;
  color: #8E8E93;
  margin-right: 16rpx;
}

.modal-btn.confirm {
  background-color: #007AFF;
  color: white;
  margin-left: 16rpx;
}
