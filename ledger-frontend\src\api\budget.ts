import { get, post, put, del } from './config'
import type { BaseResponse, Budget, BudgetCreateParams, BudgetUpdateParams, MonthlyBudgetResponse } from './types'

// 获取月度预算及汇总
export function getMonthlyBudgets(year?: number, month?: number): Promise<any> {
  return get('/budgets/', { year, month }).catch(error => {
    console.error('获取预算失败:', error)
    return { budgets: [], summary: { total_budget: 0, total_spent: 0, remaining: 0, usage_percentage: 0 } }
  })
}

// 获取预算详情
export function getBudgetById(id: number): Promise<any> {
  return get(`/budgets/${id}`).catch(error => {
    console.error(`获取预算${id}失败:`, error)
    return null
  })
}

// 创建预算
export function createBudget(data: BudgetCreateParams): Promise<any> {
  return post('/budgets/', data).catch(error => {
    console.error('创建预算失败:', error)
    return { code: 500, message: '创建预算失败' }
  })
}

// 更新预算
export function updateBudget(id: number, amount: number): Promise<any> {
  return put(`/budgets/${id}`, { amount }).catch(error => {
    console.error(`更新预算${id}失败:`, error)
    return { code: 500, message: '更新预算失败' }
  })
}

// 删除预算
export function deleteBudget(id: number): Promise<any> {
  return del(`/budgets/${id}`).catch(error => {
    console.error(`删除预算${id}失败:`, error)
    return { code: 500, message: '删除预算失败' }
  })
}

// 获取当前月份的预算
export function getCurrentMonthBudgets(): Promise<any> {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1
  return getMonthlyBudgets(year, month)
} 