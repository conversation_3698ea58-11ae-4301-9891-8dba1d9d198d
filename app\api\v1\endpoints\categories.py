from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
import logging

from app.db.session import get_db
from app.core.security import get_current_user
from app.modules.users.schemas import User
from app.modules.categories import services as category_service
from app.modules.categories.models import Category

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/", response_model=List[dict])
async def get_categories(
    type: Optional[str] = Query(None, description="类别类型: income或expense"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取用户的所有分类"""
    try:
        categories = await category_service.get_categories_by_user(db, current_user.id)
        
        # 如果指定了类型，过滤结果
        if type:
            categories = [cat for cat in categories if cat.type == type]
        
        # 转换为字典列表
        result = []
        for category in categories:
            result.append({
                "id": category.id,
                "name": category.name,
                "type": category.type,
                "color": category.color,
                "icon": category.icon or "ShoppingCart",  # 如果icon为空，使用默认值
                "user_id": category.user_id
            })
            
        return result
    except Exception as e:
        logger.error(f"获取分类失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分类失败: {str(e)}")

@router.post("/", response_model=dict)
async def create_category(
    name: str = Query(..., description="分类名称"),
    type: str = Query(..., description="类别类型: income或expense"),
    color: str = Query("#3498db", description="分类颜色"),
    icon: str = Query("ShoppingCart", description="分类图标"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建新分类"""
    try:
        category = await category_service.create_category(
            db, name, type, color, icon, current_user.id
        )
        
        return {
            "id": category.id,
            "name": category.name,
            "type": category.type,
            "color": category.color,
            "icon": category.icon,
            "user_id": category.user_id
        }
    except Exception as e:
        logger.error(f"创建分类失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建分类失败: {str(e)}")

@router.get("/{category_id}", response_model=dict)
async def get_category(
    category_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取单个分类"""
    try:
        category = await category_service.get_category(db, category_id, current_user.id)
        if not category:
            raise HTTPException(status_code=404, detail="分类不存在")
            
        return {
            "id": category.id,
            "name": category.name,
            "type": category.type,
            "color": category.color,
            "user_id": category.user_id
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分类详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分类详情失败: {str(e)}")

@router.put("/{category_id}", response_model=dict)
async def update_category(
    category_id: int,
    name: Optional[str] = Query(None, description="分类名称"),
    color: Optional[str] = Query(None, description="分类颜色"),
    icon: Optional[str] = Query(None, description="分类图标"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新分类"""
    try:
        # 先获取分类
        category = await category_service.get_category(db, category_id, current_user.id)
        if not category:
            raise HTTPException(status_code=404, detail="分类不存在")
            
        # 更新分类
        updated_category = await category_service.update_category(
            db, category, name, color, icon
        )
        
        return {
            "id": updated_category.id,
            "name": updated_category.name,
            "type": updated_category.type,
            "color": updated_category.color,
            "icon": updated_category.icon,
            "user_id": updated_category.user_id
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新分类失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新分类失败: {str(e)}")

@router.delete("/{category_id}", status_code=204)
async def delete_category(
    category_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除分类"""
    try:
        # 先获取分类
        category = await category_service.get_category(db, category_id, current_user.id)
        if not category:
            raise HTTPException(status_code=404, detail="分类不存在")
            
        # 检查是否是系统默认分类
        if category.user_id is None:
            raise HTTPException(status_code=403, detail="无法删除系统默认分类")
            
        # 删除分类
        await category_service.delete_category(db, category)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除分类失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除分类失败: {str(e)}") 