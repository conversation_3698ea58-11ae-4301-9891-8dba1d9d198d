<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <title>金融账本</title>
    <style>
      /* 深色模式预加载样式 */
      html.dark-mode,
      html.dark-mode body {
        background-color: #1e1e2e !important;
        color: #e2e2e6 !important;
      }
      
      html.dark-mode:before,
      html.dark-mode body:before {
        content: "";
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #1e1e2e !important;
        z-index: -1;
      }
      
      /* 移动端优化 */
      body {
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
      }
      
      /* 适配底部安全区域（针对全面屏iPhone） */
      .has-safe-area-bottom {
        padding-bottom: env(safe-area-inset-bottom);
      }
      
      /* 禁用双击缩放 */
      * { 
        touch-action: manipulation; 
      }
    </style>
    <!-- 深色模式预加载脚本 -->
    <script>
      (function() {
        var darkMode = localStorage.getItem('darkMode') === 'true';
        if (darkMode) {
          document.documentElement.classList.add('dark-mode');
          document.body.classList.add('dark-mode');
          
          // 强制设置背景色
          document.documentElement.style.backgroundColor = '#1e1e2e';
          document.documentElement.style.color = '#e2e2e6';
          document.body.style.backgroundColor = '#1e1e2e';
          document.body.style.color = '#e2e2e6';
          
          // 创建一个覆盖层来防止白色闪烁
          var overlay = document.createElement('div');
          overlay.style.position = 'fixed';
          overlay.style.top = '0';
          overlay.style.left = '0';
          overlay.style.width = '100%';
          overlay.style.height = '100%';
          overlay.style.backgroundColor = '#1e1e2e';
          overlay.style.zIndex = '9999';
          overlay.style.opacity = '1';
          overlay.style.transition = 'opacity 0.5s ease';
          overlay.id = 'dark-mode-overlay';
          document.body.appendChild(overlay);
          
          // 稍后移除覆盖层
          setTimeout(function() {
            overlay.style.opacity = '0';
            setTimeout(function() {
              if (overlay && overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
              }
            }, 500);
          }, 200);
        }
        
        // 移动设备检测
        if (/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)) {
          document.documentElement.classList.add('mobile-device');
          
          // 检查是否为iOS设备且为全面屏iPhone (X或更新)
          var isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
          if (isIOS && window.innerHeight >= 812) {
            document.documentElement.classList.add('has-safe-area');
            document.body.classList.add('has-safe-area-bottom');
          }
        }
      })();
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
