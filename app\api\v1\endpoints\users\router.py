import logging
import traceback
import json
import datetime
from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request, File, UploadFile, Response
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import JSONResponse
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_
from sqlalchemy import select

from app.modules.users.schemas import (
    UserCreate, User, Token, UserSettings,
    EmailVerificationRequest, EmailVerificationConfirm,
    PasswordResetRequest, PasswordResetConfirm,
    RegisterEmailVerificationRequest
)
from app.modules.users.service import UserService
from app.core.security import get_current_user
from app.db.session import get_db
import app.modules.users.models as models

# 设置日志
logger = logging.getLogger(__name__)

router = APIRouter()


@router.post(
    "/register",
    response_model=User,
    status_code=status.HTTP_201_CREATED,
    summary="Register a new user",
    tags=["users"]
)
async def register_user(
    user_in: UserCreate,
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new user in the system.
    """
    try:
        # 记录注册请求
        logger.info(f"收到注册请求: {user_in.username}, {user_in.email}")
        
        user_service = UserService()
        
        # 检查用户名是否已存在
        db_user = await user_service.get_user_by_username(db, user_in.username)
        if db_user:
            logger.warning(f"用户名已存在: {user_in.username}")
            raise HTTPException(
                status_code=400,
                detail="该用户名已被注册，请选择其他用户名",
            )

        # 检查邮箱是否已存在（排除临时记录）
        db_email = await user_service.get_user_by_email(db, user_in.email)
        if db_email and db_email.username != "":  # 只有正式用户才算已注册
            logger.warning(f"邮箱已存在: {user_in.email}")
            raise HTTPException(
                status_code=400,
                detail="该邮箱已被注册，请使用其他邮箱或直接登录",
            )
        
        # 创建用户
        logger.info(f"创建新用户: {user_in.username}")
        try:
            user = await user_service.create_user(db=db, user=user_in)
            logger.info(f"用户创建成功: {user.id}")
        except ValueError as e:
            # 邮箱验证码验证失败
            logger.warning(f"邮箱验证码验证失败: {user_in.email}")
            raise HTTPException(
                status_code=400,
                detail="邮箱验证码无效或已过期，请重新获取验证码"
            )

        # 返回用户信息
        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content={
                "code": 0,
                "message": "注册成功！现在可以登录了",
                "data": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "email_verified": user.email_verified,
                    "created_at": user.created_at.isoformat(),
                    "updated_at": user.updated_at.isoformat()
                }
            }
        )
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        # 记录详细错误信息
        logger.error(f"注册过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"服务器内部错误，请稍后重试"
        )


@router.post(
    "/login/access-token",
    response_model=Token,
    summary="Get access token for user",
    tags=["users"]
)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db)
):
    """
    OAuth2 compatible token login, get an access token for future requests.
    """
    try:
        logger.info(f"尝试登录: {form_data.username}")
        user_service = UserService()
        
        # 查询用户，确保只获取未删除的用户
        result = await db.execute(
            select(models.User).filter(
                and_(
                    models.User.username == form_data.username,
                    models.User.deleted == False
                )
            )
        )
        user = result.scalars().first()
        
        # 验证用户存在且密码正确
        if not user or not user_service.verify_password(form_data.password, user.password_hash):
            logger.warning(f"登录失败: {form_data.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误，请检查后重试",
                headers={"WWW-Authenticate": "Bearer"},
            )

        logger.info(f"登录成功: {form_data.username}")
        access_token = user_service.create_access_token(data={"sub": user.username})
        return {"access_token": access_token, "token_type": "bearer"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"服务器内部错误，请稍后重试"
        )


@router.get(
    "/me",
    response_model=User,
    summary="Get current user",
    tags=["users"]
)
async def read_users_me(
    current_user: User = Depends(get_current_user)
):
    """
    Fetch the current logged in user.
    """
    # 如果用户名长度小于3，添加一个日志提示
    if len(current_user.username) < 3:
        logger.warning(f"检测到短用户名：{current_user.username}，此用户名不符合新的命名规则")
    
    return current_user 

@router.put(
    "/me",
    response_model=User,
    summary="Update current user information",
    tags=["users"]
)
async def update_user_me(
    user_data: dict,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update the current user's information.
    """
    try:
        logger.info(f"尝试更新用户信息: {current_user.username}")
        
        user_service = UserService()
        
        # 特殊处理：现有用户名长度不符合新规则的情况
        if "username" in user_data and len(current_user.username) < 3:
            logger.info(f"检测到短用户名 '{current_user.username}' 需要更新到新规则")
            if len(user_data["username"]) < 3:
                logger.warning(f"用户尝试更新为另一个不符合规则的短用户名: {user_data['username']}")
                raise HTTPException(
                    status_code=400,
                    detail="您的用户名需要更新为符合新规则的长度（3-20个字符）",
                )
        
        # 检查如果要更新邮箱，邮箱是否已被使用
        if "email" in user_data and user_data["email"] != current_user.email:
            email_exists = await user_service.get_user_by_email(db, user_data["email"])
            if email_exists:
                logger.warning(f"邮箱已存在: {user_data['email']}")
                raise HTTPException(
                    status_code=400,
                    detail="该邮箱已被其他用户使用，请选择其他邮箱",
                )
        
        # 更新用户信息
        updated_user = await user_service.update_user_info(
            db=db,
            user_id=current_user.id,
            user_data=user_data
        )
        
        logger.info(f"用户信息更新成功: {current_user.username}")
        return updated_user
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户信息过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"服务器内部错误，请稍后重试"
        )


@router.post(
    "/logout",
    summary="Log out current user",
    tags=["users"]
)
async def logout_user():
    """
    Log out the current user. 
    This endpoint is just for API consistency and client-side can handle the actual logout by removing the token.
    """
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={"code": 0, "message": "Successfully logged out", "data": None}
    )

@router.post(
    "/change-password",
    summary="Change user password",
    tags=["users"]
)
async def change_password(
    password_data: dict,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Change the password for the current user.
    """
    try:
        logger.info(f"尝试修改密码: {current_user.username}")
        
        # 验证当前密码
        user_service = UserService()
        if not user_service.verify_password(password_data["currentPassword"], current_user.password_hash):
            logger.warning(f"当前密码验证失败: {current_user.username}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password is incorrect"
            )
        
        # 更新密码
        await user_service.update_user_password(
            db=db, 
            user_id=current_user.id, 
            new_password=password_data["newPassword"]
        )
        
        logger.info(f"密码修改成功: {current_user.username}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={"code": 0, "message": "Password changed successfully", "data": None}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"修改密码过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

# 用户设置相关路由
@router.get(
    "/settings",
    response_model=UserSettings,
    summary="Get user settings",
    tags=["users"]
)
async def get_user_settings(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get the settings for the current user.
    """
    try:
        logger.info(f"获取用户设置: {current_user.username} (ID={current_user.id})")
        
        user_service = UserService()
        settings = await user_service.get_user_settings(db, current_user.id)
        
        logger.info(f"用户设置获取成功: {current_user.username}, 设置ID={settings.id if settings else 'None'}")
        return settings
    except Exception as e:
        logger.error(f"获取用户设置过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.put(
    "/settings",
    response_model=UserSettings,
    summary="Update user settings",
    tags=["users"]
)
async def update_user_settings(
    settings_data: dict,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update the settings for the current user.
    """
    try:
        logger.info(f"更新用户设置: {current_user.username} (ID={current_user.id})")
        logger.info(f"设置数据: {settings_data}")
        
        user_service = UserService()
        settings = await user_service.update_user_settings(db, current_user.id, settings_data)
        
        logger.info(f"用户设置更新成功: {current_user.username}, 设置ID={settings.id if settings else 'None'}")
        return settings
    except Exception as e:
        logger.error(f"更新用户设置过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

# 数据备份相关路由
@router.post(
    "/backup",
    response_model=UserSettings,
    summary="Backup user data",
    tags=["users"]
)
async def backup_user_data(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a backup for the current user's data and update the last backup time.
    """
    try:
        logger.info(f"Initiating backup for user: {current_user.username}")
        
        from app.services.backup_service import BackupService
        backup_service = BackupService(db)
        
        success = await backup_service.create_backup(current_user.id, current_user.username)
        
        if not success:
            raise HTTPException(
                status_code=500,
                detail="Failed to create backup."
            )
        
        # After a successful backup, fetch the updated settings to return
        user_service = UserService()
        settings = await user_service.get_user_settings(db, current_user.id)
        
        return settings
        
    except HTTPException as e:
        # Re-raise HTTPException to preserve status code and detail
        raise e
    except Exception as e:
        logger.error(f"Error during data backup for user {current_user.username}: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="An internal server error occurred during backup."
        )

@router.get(
    "/backups",
    summary="Get user backup files list",
    tags=["users"]
)
async def list_user_backups(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取用户的所有备份文件列表
    返回按时间排序的备份文件信息
    """
    try:
        logger.info(f"获取用户备份列表: {current_user.username}")
        
        from app.services.backup_service import BackupService
        backup_service = BackupService(db)
        
        backups = await backup_service.list_backups(current_user.id, current_user.username)
        
        return {
            "code": 0,
            "message": "Success",
            "data": backups
        }
        
    except Exception as e:
        logger.error(f"获取备份列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取备份列表失败: {str(e)}"
        )

@router.post(
    "/restore-latest",
    summary="Restore data from the latest backup",
    tags=["users"]
)
async def restore_latest_backup(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    从最新的备份文件恢复用户数据
    自动选择最新的备份文件进行恢复
    """
    try:
        logger.info(f"准备从最新备份恢复数据: {current_user.username}")
        
        # 获取备份列表
        from app.services.backup_service import BackupService
        backup_service = BackupService(db)
        
        backups = await backup_service.list_backups(current_user.id, current_user.username)
        
        if not backups:
            raise HTTPException(
                status_code=404,
                detail="未找到任何备份文件，无法恢复数据"
            )
        
        # 获取最新的备份文件（列表已按时间倒序排序，第一个即为最新）
        latest_backup = backups[0]
        backup_path = latest_backup["path"]
        
        logger.info(f"选择最新备份文件进行恢复: {backup_path}")
        
        # 读取备份文件
        try:
            with open(backup_path, "r", encoding="utf-8") as f:
                restore_data = json.loads(f.read())
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"读取备份文件出错: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"备份文件读取失败: {str(e)}"
            )
        
        # 恢复数据
        from app.services.export_service import ExportService
        export_service = ExportService()
        await export_service.restore_user_data(db, current_user.id, restore_data)
        
        logger.info(f"用户数据恢复成功: {current_user.username}")
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "code": 0, 
                "message": "数据恢复成功", 
                "data": {
                    "backup_info": latest_backup
                }
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"恢复最新备份数据时出错: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"恢复数据失败: {str(e)}"
        )

# 新增一个路由用于检查恢复后的数据状态
@router.get(
    "/restore-status",
    summary="Check data restoration status",
    tags=["users"]
)
async def check_restore_status(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Check the status of user data after restoration.
    Returns counts of user accounts, categories, budgets, and transactions.
    """
    try:
        from sqlalchemy import func, select
        from app.modules.accounts.models import Account
        from app.modules.categories.models import Category
        from app.modules.budgets.models import Budget
        from app.modules.transactions.models import Transaction
        
        # 获取账户数量
        accounts_result = await db.execute(
            select(func.count()).select_from(Account).where(Account.user_id == current_user.id)
        )
        accounts_count = accounts_result.scalar()
        
        # 获取分类数量
        categories_result = await db.execute(
            select(func.count()).select_from(Category).where(Category.user_id == current_user.id)
        )
        categories_count = categories_result.scalar()
        
        # 获取预算数量
        budgets_result = await db.execute(
            select(func.count()).select_from(Budget).where(Budget.user_id == current_user.id)
        )
        budgets_count = budgets_result.scalar()
        
        # 获取交易数量
        transactions_result = await db.execute(
            select(func.count()).select_from(Transaction).where(Transaction.user_id == current_user.id)
        )
        transactions_count = transactions_result.scalar()
        
        # 获取最近10条交易记录
        recent_transactions_result = await db.execute(
            select(Transaction).where(Transaction.user_id == current_user.id)
            .order_by(Transaction.transaction_date.desc()).limit(10)
        )
        recent_transactions = recent_transactions_result.scalars().all()
        
        # 转换为可序列化的格式
        recent_trans_data = []
        for trans in recent_transactions:
            trans_dict = {
                "id": trans.id,
                "transaction_date": trans.transaction_date.isoformat() if trans.transaction_date else None,
                "transaction_type": trans.transaction_type,
                "amount": str(trans.amount),
                "account_id": trans.account_id,
                "description": trans.description
            }
            recent_trans_data.append(trans_dict)
        
        return {
            "user_id": current_user.id,
            "username": current_user.username,
            "accounts_count": accounts_count,
            "categories_count": categories_count,
            "budgets_count": budgets_count,
            "transactions_count": transactions_count,
            "recent_transactions": recent_trans_data
        }
        
    except Exception as e:
        logger.error(f"检查数据恢复状态时出错: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="An error occurred while checking restoration status."
        )

# 添加清空用户数据的API端点
@router.delete(
    "/clear-data",
    summary="Clear all user data",
    tags=["users"],
    response_model=dict
)
async def clear_user_data(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    清空当前用户的所有数据，包括交易记录、账户、预算等，但保留用户账号。
    此操作不可恢复，请谨慎使用。
    """
    try:
        user_id = current_user.id
        logger.info(f"用户请求清空数据: {current_user.username} (ID={user_id})")

        # 1. 删除交易记录
        await db.execute(
            text("DELETE FROM transactions WHERE user_id = :user_id"),
            {"user_id": user_id}
        )
        logger.info(f"已删除用户 {user_id} 的所有交易记录")

        # 2. 删除预算
        await db.execute(
            text("DELETE FROM budgets WHERE user_id = :user_id"),
            {"user_id": user_id}
        )
        logger.info(f"已删除用户 {user_id} 的所有预算")

        # 3. 删除账户
        await db.execute(
            text("DELETE FROM accounts WHERE user_id = :user_id"),
            {"user_id": user_id}
        )
        logger.info(f"已删除用户 {user_id} 的所有账户")

        # 4. 删除用户自定义分类（保留系统默认分类）
        await db.execute(
            text("DELETE FROM categories WHERE user_id = :user_id"),
            {"user_id": user_id}
        )
        logger.info(f"已删除用户 {user_id} 的所有自定义分类")

        # 提交所有更改
        await db.commit()

        logger.info(f"用户 {current_user.username} (ID={user_id}) 的数据已清空")
        return {"code": 0, "message": "用户数据已清空", "data": None}
    
    except Exception as e:
        await db.rollback()
        logger.error(f"清空用户数据时出错: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="清空数据时发生错误"
        )

# 添加删除用户账号的API端点
@router.delete(
    "/account",
    summary="Delete user account",
    tags=["users"],
    response_model=dict
)
async def delete_user_account(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    永久删除用户账号及其所有相关数据。
    此操作不可恢复，请谨慎使用。
    """
    try:
        user_id = current_user.id
        username = current_user.username
        logger.info(f"用户请求删除账号: {username} (ID={user_id})")

        # 1. 删除用户的所有数据（与clear-data相同）
        # 删除交易记录
        await db.execute(
            text("DELETE FROM transactions WHERE user_id = :user_id"),
            {"user_id": user_id}
        )
        
        # 删除预算
        await db.execute(
            text("DELETE FROM budgets WHERE user_id = :user_id"),
            {"user_id": user_id}
        )
        
        # 删除账户
        await db.execute(
            text("DELETE FROM accounts WHERE user_id = :user_id"),
            {"user_id": user_id}
        )
        
        # 删除用户自定义分类
        await db.execute(
            text("DELETE FROM categories WHERE user_id = :user_id"),
            {"user_id": user_id}
        )

        # 2. 删除用户设置
        await db.execute(
            text("DELETE FROM user_settings WHERE user_id = :user_id"),
            {"user_id": user_id}
        )
        
        # 3. 删除用户账号
        await db.execute(
            text("UPDATE users SET deleted = 1 WHERE id = :user_id"),
            {"user_id": user_id}
        )

        # 提交所有更改
        await db.commit()

        logger.info(f"用户账号 {username} (ID={user_id}) 已标记为已删除")
        return {"code": 0, "message": "用户账号已删除", "data": None}
    
    except Exception as e:
        await db.rollback()
        logger.error(f"删除用户账号时出错: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="删除账号时发生错误"
        )

@router.get(
    "/export",
    summary="Export user data",
    tags=["users"],
    response_class=JSONResponse
)
async def export_user_data(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Export data for the current user as an XLSX file.
    """
    try:
        logger.info(f"Exporting user data for {current_user.username}")

        from app.services.excel_export_service import ExcelExportService
        from fastapi.responses import Response

        export_service = ExcelExportService()
        excel_data = await export_service.export_user_data_to_excel(db, current_user.id)

        filename = f"ledger_export_{current_user.username}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"

        return Response(
            content=excel_data,
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={
                'Content-Disposition': f'attachment; filename="{filename}"'
            }
        )
    except Exception as e:
        logger.error(f"Error exporting user data for {current_user.username}: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="An error occurred while exporting data."
        )

@router.get(
    "/export-json",
    summary="Export user data as JSON",
    tags=["users"]
)
async def export_user_data_json(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Export data for the current user as JSON format (for mobile apps).
    """
    try:
        logger.info(f"Exporting user data as JSON for {current_user.username}")

        from app.services.export_service import ExportService

        export_service = ExportService()
        export_data = await export_service.export_user_data(db, current_user.id)

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "code": 0,
                "message": "数据导出成功",
                "data": export_data
            }
        )
    except Exception as e:
        logger.error(f"Error exporting user data as JSON for {current_user.username}: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="An error occurred while exporting data."
        )

@router.post(
    "/restore",
    summary="Restore user data",
    tags=["users"]
)
async def restore_user_data(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Restore data from an uploaded JSON file.
    """
    try:
        logger.info(f"恢复用户数据: {current_user.username}")
        
        # 读取上传的文件内容
        file_content = await file.read()
        
        # 解析JSON数据
        import json
        try:
            import json
            restore_data = json.loads(file_content.decode('utf-8'))
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {str(e)}")
            raise HTTPException(
                status_code=400,
                detail="Invalid JSON format"
            )
        
        # 恢复数据
        from app.services.export_service import ExportService
        export_service = ExportService()
        await export_service.restore_user_data(db, current_user.id, restore_data)
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={"code": 0, "message": "Data restored successfully", "data": None}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"恢复用户数据过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


# 注册验证码相关接口
@router.post(
    "/send-register-verification",
    summary="Send registration verification code",
    tags=["users"]
)
async def send_register_verification_code(
    email_data: RegisterEmailVerificationRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    发送注册验证码
    """
    try:
        logger.info(f"发送注册验证码: {email_data.email}")

        user_service = UserService()
        success = await user_service.send_register_verification_code(
            db=db,
            email=email_data.email
        )

        if success:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={"code": 0, "message": "验证码已发送，请检查您的邮箱", "data": None}
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该邮箱已被注册或发送验证码失败，请检查邮箱地址"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发送注册验证码过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"服务器内部错误，请稍后重试"
        )


# 邮箱验证相关接口
@router.post(
    "/verify-email",
    summary="Verify email address",
    tags=["users"]
)
async def verify_email(
    verification_data: EmailVerificationConfirm,
    db: AsyncSession = Depends(get_db)
):
    """
    验证邮箱地址
    """
    try:
        logger.info(f"验证邮箱: {verification_data.email}")

        user_service = UserService()
        success = await user_service.verify_email(
            db=db,
            email=verification_data.email,
            verification_code=verification_data.verification_code
        )

        if success:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={"code": 0, "message": "邮箱验证成功", "data": None}
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="验证码无效或已过期，请重新获取验证码"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证邮箱过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.post(
    "/resend-verification",
    summary="Resend email verification",
    tags=["users"]
)
async def resend_verification_email(
    email_data: EmailVerificationRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    重新发送邮箱验证邮件
    """
    try:
        logger.info(f"重发验证邮件: {email_data.email}")

        user_service = UserService()
        success = await user_service.resend_verification_email(
            db=db,
            email=email_data.email
        )

        if success:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={"code": 0, "message": "验证邮件已发送，请检查您的邮箱", "data": None}
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="发送验证邮件失败，请稍后重试"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重发验证邮件过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.post(
    "/forgot-password",
    summary="Request password reset",
    tags=["users"]
)
async def forgot_password(
    email_data: PasswordResetRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    请求密码重置
    """
    try:
        logger.info(f"请求密码重置: {email_data.email}")

        user_service = UserService()
        success = await user_service.request_password_reset(
            db=db,
            email=email_data.email
        )

        # 为了安全，无论用户是否存在都返回成功
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={"code": 0, "message": "如果该邮箱已注册，密码重置邮件已发送", "data": None}
        )

    except Exception as e:
        logger.error(f"请求密码重置过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"服务器内部错误，请稍后重试"
        )


@router.post(
    "/reset-password",
    summary="Reset password with code",
    tags=["users"]
)
async def reset_password(
    reset_data: PasswordResetConfirm,
    db: AsyncSession = Depends(get_db)
):
    """
    使用重置码重置密码
    """
    try:
        logger.info(f"重置密码: {reset_data.email}")

        user_service = UserService()
        success = await user_service.reset_password_with_code(
            db=db,
            email=reset_data.email,
            reset_code=reset_data.reset_code,
            new_password=reset_data.new_password
        )

        if success:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={"code": 0, "message": "Password reset successfully", "data": None}
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid reset code or code expired"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置密码过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )