<!-- 添加账户页面 -->
<view class="add-account-page">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-left" bindtap="goBack">
      <text class="back-icon">‹</text>
      <text class="back-text">返回</text>
    </view>
    <text class="page-title">添加账户</text>
    <view class="header-right"></view>
  </view>

  <!-- 表单内容 -->
  <view class="form-container">
    <!-- 账户名称 -->
    <view class="form-group">
      <view class="form-label">账户名称</view>
      <view class="form-input-container">
        <input 
          class="form-input" 
          placeholder="请输入账户名称" 
          value="{{form.account_name}}"
          bindinput="onAccountNameInput"
          maxlength="50"
        />
      </view>
    </view>

    <!-- 账户类型 -->
    <view class="form-group">
      <view class="form-label">账户类型</view>
      <view class="form-picker" bindtap="showAccountTypePicker">
        <text class="picker-text {{form.account_type ? '' : 'placeholder'}}">
          {{accountTypeDisplay || '请选择账户类型'}}
        </text>
        <text class="picker-arrow">></text>
      </view>
    </view>

    <!-- 金融机构 -->
    <view class="form-group">
      <view class="form-label">金融机构</view>
      <view class="form-input-container">
        <input 
          class="form-input" 
          placeholder="如：中国银行、支付宝等（可选）" 
          value="{{form.institution}}"
          bindinput="onInstitutionInput"
          maxlength="50"
        />
      </view>
    </view>

    <!-- 账户号码 -->
    <view class="form-group">
      <view class="form-label">账户号码</view>
      <view class="form-input-container">
        <input 
          class="form-input" 
          placeholder="账户号码或卡号（可选）" 
          value="{{form.account_number}}"
          bindinput="onAccountNumberInput"
          maxlength="30"
        />
      </view>
    </view>

    <!-- 初始余额 -->
    <view class="form-group">
      <view class="form-label">初始余额</view>
      <view class="form-input-container">
        <input 
          class="form-input amount-input" 
          placeholder="0.00" 
          value="{{form.initial_balance}}"
          bindinput="onInitialBalanceInput"
          type="digit"
        />
        <text class="currency-symbol">¥</text>
      </view>
    </view>

    <!-- 货币类型 -->
    <view class="form-group">
      <view class="form-label">货币类型</view>
      <view class="form-picker" bindtap="showCurrencyPicker">
        <text class="picker-text">{{form.currency}}</text>
        <text class="picker-arrow">></text>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="form-footer">
    <button 
      class="btn-primary" 
      bindtap="saveAccount"
      disabled="{{submitting || !canSubmit}}"
    >
      {{submitting ? '保存中...' : '保存账户'}}
    </button>
  </view>

  <!-- 账户类型选择器 -->
  <ios-picker
    visible="{{showAccountTypePicker}}"
    title="选择账户类型"
    options="{{accountTypes}}"
    selected-value="{{form.account_type}}"
    value-key="value"
    label-key="label"
    icon-key="icon"
    bind:select="selectAccountType"
    bind:cancel="hideAccountTypePicker"
    bind:confirm="hideAccountTypePicker"
  />

  <!-- 货币类型选择器 -->
  <ios-picker
    visible="{{showCurrencyPicker}}"
    title="选择货币类型"
    options="{{currencyOptions}}"
    selected-value="{{form.currency}}"
    value-key="value"
    label-key="label"
    bind:select="selectCurrency"
    bind:cancel="hideCurrencyPicker"
    bind:confirm="hideCurrencyPicker"
  />
</view>
