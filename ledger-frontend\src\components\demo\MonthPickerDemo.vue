<template>
  <div class="month-picker-demo">
    <div class="demo-header">
      <h1>📅 iOS风格月份选择器</h1>
      <p>适用于移动端的现代化月份筛选组件</p>
    </div>
    
    <div class="demo-content">
      <!-- 基础用法 -->
      <div class="demo-section">
        <h2>基础用法</h2>
        <div class="demo-item">
          <label>选择月份：</label>
          <MobileMonthPicker 
            v-model="selectedMonth"
            placeholder="请选择月份"
            @change="onMonthChange"
          />
        </div>
        <div class="result">
          <p>选中的月份: {{ formatSelectedMonth }}</p>
        </div>
      </div>
      
      <!-- 禁用状态 -->
      <div class="demo-section">
        <h2>禁用状态</h2>
        <div class="demo-item">
          <label>禁用的选择器：</label>
          <MobileMonthPicker 
            v-model="disabledMonth"
            placeholder="禁用状态"
            disabled
          />
        </div>
      </div>
      
      <!-- 带初始值 -->
      <div class="demo-section">
        <h2>带初始值</h2>
        <div class="demo-item">
          <label>当前月份：</label>
          <MobileMonthPicker 
            v-model="currentMonth"
            placeholder="当前月份"
          />
        </div>
      </div>
    </div>
    
    <!-- 特性说明 -->
    <div class="features">
      <h2>✨ 主要特性</h2>
      <div class="feature-grid">
        <div class="feature-item">
          <div class="feature-icon">🎨</div>
          <h3>iOS设计语言</h3>
          <p>贴近Apple原生设计，使用San Francisco字体风格</p>
        </div>
        <div class="feature-item">
          <div class="feature-icon">📱</div>
          <h3>移动端优化</h3>
          <p>专为移动设备设计，支持触摸交互和手势操作</p>
        </div>
        <div class="feature-item">
          <div class="feature-icon">🌙</div>
          <h3>深色模式</h3>
          <p>自动适配系统深色模式，提供一致的视觉体验</p>
        </div>
        <div class="feature-item">
          <div class="feature-icon">⚡</div>
          <h3>流畅动画</h3>
          <p>丰富的过渡动画和微交互，提升用户体验</p>
        </div>
        <div class="feature-item">
          <div class="feature-icon">🎯</div>
          <h3>智能标记</h3>
          <p>自动标记当前月份，选中状态清晰明确</p>
        </div>
        <div class="feature-item">
          <div class="feature-icon">🔧</div>
          <h3>易于使用</h3>
          <p>简单的API设计，支持v-model双向绑定</p>
        </div>
      </div>
    </div>
    
    <!-- 代码示例 -->
    <div class="code-example">
      <h2>💻 使用示例</h2>
      <div class="code-block">
        <pre><code>&lt;template&gt;
  &lt;MobileMonthPicker 
    v-model="selectedMonth"
    placeholder="选择月份"
    @change="handleMonthChange"
  /&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue'
import MobileMonthPicker from '@/components/MobileMonthPicker.vue'

const selectedMonth = ref([])

const handleMonthChange = (value) => {
  console.log('选中的月份:', value)
}
&lt;/script&gt;</code></pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import MobileMonthPicker from '@/components/MobileMonthPicker.vue'

// 演示数据
const selectedMonth = ref([])
const disabledMonth = ref([])
const currentMonth = ref(() => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const startDate = `${year}-${month}-01`
  const endDate = new Date(year, now.getMonth() + 1, 0)
  const endDateStr = `${year}-${month}-${endDate.getDate().toString().padStart(2, '0')}`
  return [startDate, endDateStr]
})

// 格式化选中的月份
const formatSelectedMonth = computed(() => {
  if (!selectedMonth.value || selectedMonth.value.length === 0) {
    return '未选择'
  }
  
  const startDate = selectedMonth.value[0]
  if (startDate) {
    const date = new Date(startDate)
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    return `${year}年${month}月 (${startDate} 至 ${selectedMonth.value[1]})`
  }
  
  return '未选择'
})

// 月份变化处理
const onMonthChange = (value: string[]) => {
  console.log('月份选择变化:', value)
}
</script>

<style lang="scss" scoped>
$ios-primary: #007AFF;
$ios-success: #34C759;
$ios-gray-1: #F2F2F7;
$ios-gray-2: #E5E5EA;
$ios-gray-4: #8E8E93;
$ios-text-primary: #1C1C1E;
$ios-text-secondary: #8E8E93;

.month-picker-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  
  .demo-header {
    text-align: center;
    margin-bottom: 40px;
    
    h1 {
      font-size: 32px;
      font-weight: 700;
      color: $ios-text-primary;
      margin: 0 0 16px 0;
      letter-spacing: -0.5px;
    }
    
    p {
      font-size: 18px;
      color: $ios-text-secondary;
      margin: 0;
      font-weight: 500;
    }
  }
  
  .demo-content {
    margin-bottom: 50px;
    
    .demo-section {
      background: white;
      border-radius: 16px;
      padding: 24px;
      margin-bottom: 20px;
      box-shadow: 0 2px 16px rgba(0, 0, 0, 0.06);
      border: 1px solid $ios-gray-2;
      
      h2 {
        font-size: 20px;
        font-weight: 600;
        color: $ios-text-primary;
        margin: 0 0 20px 0;
        letter-spacing: -0.3px;
      }
      
      .demo-item {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-bottom: 20px;
        
        label {
          font-size: 16px;
          font-weight: 500;
          color: $ios-text-primary;
        }
      }
      
      .result {
        background: $ios-gray-1;
        border-radius: 12px;
        padding: 16px;
        
        p {
          margin: 0;
          font-size: 15px;
          color: $ios-text-secondary;
          font-weight: 500;
        }
      }
    }
  }
  
  .features {
    margin-bottom: 50px;
    
    h2 {
      font-size: 24px;
      font-weight: 700;
      color: $ios-text-primary;
      text-align: center;
      margin: 0 0 30px 0;
      letter-spacing: -0.4px;
    }
    
    .feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      
      .feature-item {
        background: white;
        border-radius: 16px;
        padding: 24px;
        text-align: center;
        box-shadow: 0 2px 16px rgba(0, 0, 0, 0.06);
        border: 1px solid $ios-gray-2;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }
        
        .feature-icon {
          font-size: 40px;
          margin-bottom: 16px;
        }
        
        h3 {
          font-size: 18px;
          font-weight: 600;
          color: $ios-text-primary;
          margin: 0 0 12px 0;
          letter-spacing: -0.2px;
        }
        
        p {
          font-size: 14px;
          color: $ios-text-secondary;
          margin: 0;
          line-height: 1.5;
          font-weight: 500;
        }
      }
    }
  }
  
  .code-example {
    h2 {
      font-size: 24px;
      font-weight: 700;
      color: $ios-text-primary;
      margin: 0 0 20px 0;
      letter-spacing: -0.4px;
    }
    
    .code-block {
      background: #1a1a1a;
      border-radius: 12px;
      padding: 24px;
      overflow-x: auto;
      
      pre {
        margin: 0;
        
        code {
          font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;
          font-size: 14px;
          line-height: 1.6;
          color: #e6e6e6;
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .month-picker-demo {
    padding: 16px;
    
    .demo-header {
      margin-bottom: 30px;
      
      h1 {
        font-size: 26px;
      }
      
      p {
        font-size: 16px;
      }
    }
    
    .demo-content .demo-section {
      padding: 20px;
      
      h2 {
        font-size: 18px;
      }
    }
    
    .features {
      .feature-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        
        .feature-item {
          padding: 20px;
          
          .feature-icon {
            font-size: 32px;
            margin-bottom: 12px;
          }
          
          h3 {
            font-size: 16px;
          }
          
          p {
            font-size: 13px;
          }
        }
      }
    }
    
    .code-example .code-block {
      padding: 16px;
      
      pre code {
        font-size: 12px;
      }
    }
  }
}
</style> 