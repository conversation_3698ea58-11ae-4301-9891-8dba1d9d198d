# API 测试文档

本文档提供了如何测试金融账本系统 API 的详细指南，包括不同测试工具的使用方法以及常见问题的解决方案。

## 目录

1. [环境准备](#环境准备)
2. [使用 Swagger UI 测试](#使用-swagger-ui-测试)
3. [使用 curl 测试](#使用-curl-测试)
4. [使用 Postman 测试](#使用-postman-测试)
5. [常见问题排查](#常见问题排查)
6. [API 测试用例](#api-测试用例)

## 环境准备

在开始测试之前，确保：

1. 后端服务已启动：`uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload`
2. 数据库已正确配置并连接
3. 必要的环境变量已设置（参见 `.env.example`）

## 使用 Swagger UI 测试

FastAPI 自带 Swagger UI，提供了可视化的 API 测试界面。

### 访问 Swagger UI

1. 启动服务后，打开浏览器访问：`http://127.0.0.1:8000/docs`
2. 您应该能看到所有可用的 API 端点列表

### 认证步骤

Swagger UI 提供两种认证方式：

#### 方式一：直接在 Authorize 弹窗中认证（推荐）

1. 首先，使用 `/api/v1/users/register` 接口注册一个用户
2. 点击 Swagger UI 右上角的 "Authorize" 按钮
3. 在弹出窗口中：
   - 输入您的用户名和密码
   - client_id 和 client_secret 可以留空
4. 点击 "Authorize" 按钮
5. 认证成功后，所有需要认证的接口都会自动带上令牌

#### 方式二：手动获取令牌并认证

1. 使用 `/api/v1/users/login/access-token` 接口获取令牌：
   - 点击 "Try it out"
   - 输入用户名和密码
   - 执行请求获取访问令牌
2. 复制返回的 `access_token` 值
3. 点击 Swagger UI 右上角的 "Authorize" 按钮
4. 在弹出窗口中，输入 `Bearer {你的令牌}` 到 value 字段（不需要填写用户名和密码）
5. 点击 "Authorize" 按钮

### 测试 API 端点

1. 在 Swagger UI 中找到要测试的端点
2. 点击展开该端点
3. 点击 "Try it out"
4. 填写必要的参数
5. 点击 "Execute" 执行请求
6. 查看响应结果

## 使用 curl 测试

如果您喜欢命令行工具，可以使用 curl 进行测试。

### 用户注册

```bash
curl -X POST "http://127.0.0.1:8000/api/v1/users/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "phone": "***********"
  }'
```

### 获取访问令牌

```bash
curl -X POST "http://127.0.0.1:8000/api/v1/users/login/access-token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=testuser&password=password123"
```

### 获取当前用户信息（需要认证）

```bash
curl -X GET "http://127.0.0.1:8000/api/v1/users/me" \
  -H "Authorization: Bearer {你的访问令牌}"
```

### 创建账户（需要认证）

```bash
curl -X POST "http://127.0.0.1:8000/api/v1/accounts/" \
  -H "Authorization: Bearer {你的访问令牌}" \
  -H "Content-Type: application/json" \
  -d '{
    "account_name": "我的储蓄账户",
    "account_type": "savings",
    "institution": "中国银行",
    "account_number": "*************",
    "currency": "CNY",
    "initial_balance": 10000
  }'
```

### 获取所有账户（需要认证）

```bash
curl -X GET "http://127.0.0.1:8000/api/v1/accounts/" \
  -H "Authorization: Bearer {你的访问令牌}"
```

## 使用 Postman 测试

Postman 是一个功能强大的 API 测试工具，提供了友好的 GUI 界面。

### 设置 Postman 环境变量

1. 创建一个新的环境，命名为 "金融账本系统"
2. 添加以下变量：
   - `base_url`: `http://127.0.0.1:8000`
   - `token`: 留空（稍后填写）

### 获取访问令牌

1. 创建一个新的 POST 请求：`{{base_url}}/api/v1/users/login/access-token`
2. 在 "Body" 标签页选择 "x-www-form-urlencoded"
3. 添加以下键值对：
   - `username`: `testuser`
   - `password`: `password123`
4. 发送请求
5. 从响应中提取 `access_token` 值
6. 将其保存到环境变量 `token` 中

### 设置认证

对于需要认证的请求：

1. 在 "Authorization" 标签页选择 "Bearer Token"
2. 在 Token 字段输入 `{{token}}`

### 测试 API 端点

按照类似步骤创建其他 API 请求，使用相应的 HTTP 方法、URL 和请求体。

## 常见问题排查

### Swagger UI 没有显示 "Authorize" 按钮

可能原因：
- OAuth2 安全方案未正确配置
- `security.py` 中的 `oauth2_scheme` 未正确设置
- API 路由中没有使用 `Depends(get_current_user)`

解决方案：
1. 检查 `app/core/security.py` 文件，确保 `oauth2_scheme` 正确配置
2. 确保至少有一个 API 端点使用了 `Depends(get_current_user)`
3. 检查 `app/main.py` 是否正确包含了 API 路由

### 认证失败（401 Unauthorized）

可能原因：
- 令牌格式不正确（缺少 "Bearer " 前缀）
- 令牌已过期
- 令牌签名无效
- 用户名或密码错误

解决方案：
1. 确保在 Authorization 头中使用 `Bearer {token}` 格式
2. 重新获取访问令牌
3. 检查用户名和密码是否正确
4. 检查服务器日志获取更多信息

### 找不到路由（404 Not Found）

可能原因：
- URL 路径不正确
- API 路由未正确注册到 FastAPI 应用

解决方案：
1. 检查 URL 是否正确（包括前缀 `/api/v1/`）
2. 检查 `app/api/v1/api.py` 和 `app/main.py` 中的路由注册

### 服务器错误（500 Internal Server Error）

可能原因：
- 数据库连接问题
- 代码逻辑错误
- 环境配置问题

解决方案：
1. 检查服务器日志获取详细错误信息
2. 验证数据库连接是否正常
3. 检查环境变量是否正确设置

## API 测试用例

### 用户模块测试

#### 1. 用户注册

**请求**：
- 方法：POST
- 路径：`/api/v1/users/register`
- 内容类型：application/json
- 请求体：
  ```json
  {
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "securepassword",
    "phone": "13900001111"
  }
  ```

**预期响应**：
- 状态码：201
- 包含用户信息（不含密码）

#### 2. 用户登录

**请求**：
- 方法：POST
- 路径：`/api/v1/users/login/access-token`
- 内容类型：application/x-www-form-urlencoded
- 请求体：
  - username=newuser
  - password=securepassword

**预期响应**：
- 状态码：200
- 包含 access_token 和 token_type

#### 3. 获取当前用户信息

**请求**：
- 方法：GET
- 路径：`/api/v1/users/me`
- 头部：Authorization: Bearer {token}

**预期响应**：
- 状态码：200
- 包含当前用户信息

### 账户模块测试

#### 1. 创建账户

**请求**：
- 方法：POST
- 路径：`/api/v1/accounts/`
- 头部：Authorization: Bearer {token}
- 内容类型：application/json
- 请求体：
  ```json
  {
    "account_name": "投资账户",
    "account_type": "investment",
    "institution": "证券公司",
    "account_number": "A123456789",
    "currency": "CNY",
    "initial_balance": 50000
  }
  ```

**预期响应**：
- 状态码：201
- 包含新创建的账户信息

#### 2. 获取所有账户

**请求**：
- 方法：GET
- 路径：`/api/v1/accounts/`
- 头部：Authorization: Bearer {token}

**预期响应**：
- 状态码：200
- 包含账户列表
