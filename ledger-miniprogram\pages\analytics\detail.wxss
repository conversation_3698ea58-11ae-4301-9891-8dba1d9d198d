/* pages/analytics/detail.wxss */
.detail-analytics-page {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding-bottom: 32rpx;
}

/* 导航栏 */
.nav-header {
  background-color: #FFFFFF;
  border-bottom: 1rpx solid #E5E5EA;
  position: relative;
  z-index: 100;
}

.nav-content {
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx; /* 固定导航栏内容高度 */
}

.nav-back {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 36rpx;
  color: #007AFF;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

.nav-right {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-icon {
  font-size: 32rpx;
}

/* 时间范围选择 */
.time-range-selector {
  background-color: #FFFFFF;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.range-tabs {
  display: flex;
  background-color: #F2F2F7;
  border-radius: 16rpx;
  padding: 8rpx;
}

.range-tab {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #8E8E93;
  transition: all 0.2s ease;
}

.range-tab.active {
  background-color: #FFFFFF;
  color: #007AFF;
  font-weight: 600;
}

.custom-date-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 24rpx;
}

.date-input {
  flex: 1;
  background-color: #F2F2F7;
  border-radius: 12rpx;
  padding: 24rpx;
  text-align: center;
  font-size: 28rpx;
  color: #000000;
}

.date-separator {
  margin: 0 16rpx;
  font-size: 28rpx;
  color: #8E8E93;
}

/* 核心指标卡片 */
.metrics-cards {
  display: flex;
  flex-wrap: wrap;
  padding: 0 32rpx;
  margin-bottom: 32rpx;
}

.metric-card {
  width: 48%;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  margin-bottom: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.metric-card:nth-child(odd) {
  margin-right: 4%;
}

.metric-label {
  font-size: 26rpx;
  color: #8E8E93;
  margin-bottom: 16rpx;
}

.metric-value {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.metric-value.income {
  color: #34C759;
}

.metric-value.expense {
  color: #FF3B30;
}

.metric-change {
  font-size: 24rpx;
  font-weight: 500;
}

.metric-change.positive {
  color: #34C759;
}

.metric-change.negative {
  color: #FF3B30;
}

/* 分析区块 */
.expense-analysis,
.income-analysis,
.monthly-comparison,
.financial-advice {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin: 0 32rpx 32rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

.view-toggle {
  display: flex;
  background-color: #F2F2F7;
  border-radius: 12rpx;
  padding: 4rpx;
}

.toggle-item {
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #8E8E93;
  transition: all 0.2s ease;
}

.toggle-item.active {
  background-color: #FFFFFF;
  color: #007AFF;
  font-weight: 600;
}

/* 分类排行 */
.category-rank {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 24rpx;
  background-color: #F8F9FA;
  border-radius: 16rpx;
}

.rank-number {
  width: 48rpx;
  height: 48rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 24rpx;
}

.category-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.category-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.category-details {
  flex: 1;
}

.category-name {
  display: block;
  font-size: 30rpx;
  color: #000000;
  margin-bottom: 8rpx;
}

.category-amount {
  display: block;
  font-size: 26rpx;
  color: #8E8E93;
}

.category-percentage {
  width: 120rpx;
  text-align: right;
}

.percentage-text {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 8rpx;
}

.percentage-bar {
  width: 100%;
  height: 8rpx;
  background-color: #E5E5EA;
  border-radius: 4rpx;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background-color: #FF3B30;
  border-radius: 4rpx;
}

/* 收入来源 */
.source-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F2F2F7;
}

.source-item:last-child {
  border-bottom: none;
}

.source-info {
  display: flex;
  align-items: center;
}

.source-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.source-name {
  font-size: 30rpx;
  color: #000000;
}

.source-amount {
  text-align: right;
}

.amount-text {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #34C759;
  margin-bottom: 4rpx;
}

/* 月度对比 */
.comparison-chart {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 300rpx;
  padding: 0 16rpx;
}

.month-comparison {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 8rpx;
}

.month-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.month-name {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 16rpx;
}

.month-bars {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  width: 60rpx;
  margin-bottom: 16rpx;
}

.income-bar-comp {
  width: 24rpx;
  background-color: #34C759;
  border-radius: 4rpx 4rpx 0 0;
  margin-right: 4rpx;
  min-height: 8rpx;
}

.expense-bar-comp {
  width: 24rpx;
  background-color: #FF3B30;
  border-radius: 4rpx 4rpx 0 0;
  margin-left: 4rpx;
  min-height: 8rpx;
}

.month-amounts {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.income-text {
  font-size: 20rpx;
  color: #34C759;
  margin-bottom: 4rpx;
}

.expense-text {
  font-size: 20rpx;
  color: #FF3B30;
}

/* 财务建议 */
.advice-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  padding: 24rpx;
  background-color: #F8F9FA;
  border-radius: 16rpx;
}

.advice-item:last-child {
  margin-bottom: 0;
}

.advice-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 24rpx;
}

.advice-icon.warning {
  background-color: #FFF3CD;
}

.advice-icon.success {
  background-color: #D4EDDA;
}

.advice-icon.info {
  background-color: #D1ECF1;
}

.advice-content {
  flex: 1;
}

.advice-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 8rpx;
}

.advice-description {
  display: block;
  font-size: 26rpx;
  color: #8E8E93;
  line-height: 1.4;
}
