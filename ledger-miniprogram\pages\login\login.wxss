/* pages/login/login.wxss */
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx;
  display: flex;
  flex-direction: column;
}

.login-header {
  text-align: center;
  padding: 120rpx 0 80rpx;
}

.logo {
  margin-bottom: 32rpx;
}

.logo-text {
  font-size: 120rpx;
  line-height: 1;
}

.app-name {
  font-size: 56rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
}

.app-desc {
  font-size: 30rpx;
  color: rgba(255, 255, 255, 0.8);
}

.login-form {
  flex: 1;
}

.form-section {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 40rpx;
}

.form-group {
  
}

.input-row {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.input-row:last-child {
  border-bottom: none;
}

.input-label {
  font-size: 34rpx;
  color: #000000;
  margin-right: 32rpx;
  min-width: 120rpx;
}

.form-input {
  flex: 1;
  font-size: 34rpx;
  color: #000000;
}

.password-toggle {
  color: #007AFF;
  font-size: 28rpx;
  padding: 0;
  background: none;
  border: none;
}

.error-text {
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  color: #FF3B30;
  background-color: #FFF5F5;
}

.login-actions {
  margin-bottom: 40rpx;
}

.login-btn {
  width: 100%;
  margin-bottom: 32rpx;
}

.btn-secondary {
  background-color: #FFFFFF;
  color: #007AFF;
  border: 1rpx solid #007AFF;
  border-radius: 20rpx;
  padding: 24rpx 32rpx;
  font-size: 34rpx;
  width: 100%;
}

.btn-secondary:active {
  background-color: #F0F8FF;
}

.login-footer {
  text-align: center;
}

.link-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.9);
  font-size: 26rpx;
  margin: 16rpx 0;
  display: block;
  width: 100%;
}


