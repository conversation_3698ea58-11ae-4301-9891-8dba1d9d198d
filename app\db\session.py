from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Construct the database URL from settings
DATABASE_URL = (
    f"mysql+aiomysql://{settings.MYSQL_USER}:{settings.MYSQL_PASSWORD}@"
    f"{settings.MYSQL_HOST}:{settings.MYSQL_PORT}/{settings.MYSQL_DATABASE}"
)

logger.info(f"连接数据库: {settings.MYSQL_HOST}:{settings.MYSQL_PORT}/{settings.MYSQL_DATABASE} (User: {settings.MYSQL_USER})")

# Create the async engine
engine = create_async_engine(
    DATABASE_URL,
    pool_size=settings.MYSQL_POOL_SIZE,
    pool_recycle=settings.MYSQL_POOL_RECYCLE,
    echo=settings.DEBUG,
)

# Create a sessionmaker
AsyncSessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False,
)

async def get_db() -> AsyncSession:
    """
    Dependency function to get a database session.
    """
    try:
        async with AsyncSessionLocal() as session:
            logger.info("数据库会话创建成功")
            yield session
            logger.info("数据库会话关闭")
    except Exception as e:
        logger.error(f"数据库会话错误: {e}")
        raise 