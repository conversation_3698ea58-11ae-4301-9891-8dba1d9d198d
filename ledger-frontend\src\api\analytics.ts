import { get } from './config'
import type { 
  BaseResponse, 
  AnalyticsData, 
  IncomeExpenseSummary, 
  CategorySummary,
  AccountSummary,
  AssetTrend,
  MonthlyIncome,
  ExpenseDistribution,
  NetWorthTrend,
  AccountBalance,
  MonthlySummary
} from './types'

type DashboardData = {
  totalAssets: number;
  totalLiabilities: number;
  netWorth: number;
  assetGrowth: number;
  recentTransactions: any[];
}

// 获取仪表盘数据
export function getDashboardData(): Promise<DashboardData> {
  return get('/analytics/dashboard')
}

// 获取月度收入支出数据
export function getMonthlyIncomeExpense(params?: { 
  year?: number 
}): Promise<MonthlyIncome[]> {
  console.log('调用月度收入支出API:', params)
  return get('/analytics/monthly-income-expense', params)
}

// 获取支出分布数据
export function getExpenseDistribution(params?: {
  year?: number;
  month?: number;
}): Promise<ExpenseDistribution> {
  console.log('调用支出分布API:', params)
  return get('/analytics/expense-distribution', params)
}

// 获取净资产趋势数据
export function getNetWorthTrend(params?: {
  period?: 'weekly' | 'monthly' | 'yearly';
  months?: number;
}): Promise<NetWorthTrend[]> {
  console.log('调用净资产趋势API:', params)
  return get('/analytics/net-worth-trend', params)
}

// 获取账户余额数据
export function getAccountBalances(): Promise<AccountBalance[]> {
  console.log('调用账户余额API')
  return get('/analytics/account-balances')
}

// 获取月度财务汇总数据
export function getMonthlySummary(params?: {
  year?: number;
  month?: number;
}): Promise<MonthlySummary> {
  console.log('调用月度财务汇总API:', params)
  return get('/analytics/monthly-summary', params)
}

// 以下是旧API，保留兼容性
// 获取分析数据
export function getAnalyticsData(params: { 
  dateRange?: [string, string]; 
  viewType?: 'week' | 'month' | 'year' 
}): Promise<BaseResponse<AnalyticsData>> {
  return get('/analytics/data', params)
}

// 获取收支对比
export function getIncomeExpenseSummary(params: {
  dateRange?: [string, string];
  viewType?: 'week' | 'month' | 'year'
}): Promise<BaseResponse<IncomeExpenseSummary[]>> {
  return get('/analytics/income-expense', params)
}

// 获取支出分类统计
export function getExpenseCategorySummary(params: {
  dateRange?: [string, string];
}): Promise<BaseResponse<CategorySummary[]>> {
  return get('/analytics/expense-category', params)
}

// 获取收入分类统计
export function getIncomeCategorySummary(params: {
  dateRange?: [string, string];
}): Promise<BaseResponse<CategorySummary[]>> {
  return get('/analytics/income-category', params)
}

// 获取账户余额统计
export function getAccountSummary(): Promise<BaseResponse<AccountSummary[]>> {
  return get('/analytics/account-summary')
}

// 获取资产趋势
export function getAssetTrend(params: {
  dateRange?: [string, string];
  viewType?: 'week' | 'month' | 'year'
}): Promise<BaseResponse<AssetTrend[]>> {
  return get('/analytics/asset-trend', params)
} 