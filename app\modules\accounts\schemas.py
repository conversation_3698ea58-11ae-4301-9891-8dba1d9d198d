from pydantic import BaseModel
from typing import Optional
from decimal import Decimal
import datetime

class AccountBase(BaseModel):
    account_name: str
    account_type: str
    institution: Optional[str] = None
    account_number: Optional[str] = None
    currency: str = 'CNY'
    initial_balance: Optional[Decimal] = 0

class AccountCreate(AccountBase):
    pass

class AccountUpdate(BaseModel):
    account_name: Optional[str] = None
    account_type: Optional[str] = None
    institution: Optional[str] = None
    account_number: Optional[str] = None
    currency: Optional[str] = None
    is_active: Optional[bool] = None

class Account(AccountBase):
    id: int
    user_id: int
    current_balance: Decimal
    is_active: bool
    created_at: datetime.datetime
    updated_at: datetime.datetime

    class Config:
        orm_mode = True 