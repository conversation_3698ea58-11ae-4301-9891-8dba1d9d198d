/* pages/transactions/list.wxss */
.transactions-page {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding-bottom: 120rpx; /* 为FAB留出空间 */
}

.filter-bar {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background-color: #FFFFFF;
  border-bottom: 1rpx solid #E5E5EA;
}

.date-picker {
  flex: 1;
  padding: 16rpx 24rpx;
  background-color: #F2F2F7;
  border-radius: 12rpx;
  text-align: center;
}

.date-text {
  font-size: 28rpx;
  color: #000000;
}

.date-separator {
  margin: 0 16rpx;
  font-size: 28rpx;
  color: #8E8E93;
}

.filter-btn {
  width: 64rpx;
  height: 64rpx;
  background-color: #007AFF;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
}

.filter-icon {
  font-size: 32rpx;
  color: white;
}

.eye-toggle-btn {
  width: 64rpx;
  height: 64rpx;
  background-color: #34C759;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
}

.eye-icon {
  font-size: 32rpx;
  color: white;
}

.stats-summary {
  display: flex;
  background-color: #FFFFFF;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 26rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
}

.stat-value.income {
  color: #34C759;
}

.stat-value.expense {
  color: #FF3B30;
}

.transactions-list {
  padding: 32rpx;
}

.loading {
  text-align: center;
  padding: 80rpx 0;
  color: #8E8E93;
  font-size: 30rpx;
}

.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #8E8E93;
  margin-bottom: 40rpx;
}

.transaction-group {
  margin-bottom: 32rpx;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 32rpx;
  background-color: #F2F2F7;
  border-radius: 16rpx 16rpx 0 0;
}

.group-date {
  font-size: 28rpx;
  color: #000000;
  font-weight: 500;
}

.group-amount {
  font-size: 28rpx;
  font-weight: 600;
}

.group-content {
  background-color: #FFFFFF;
  border-radius: 0 0 16rpx 16rpx;
  overflow: hidden;
}

.transaction-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #F2F2F7;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
}

.icon-text {
  font-size: 40rpx;
}

.transaction-info {
  flex: 1;
}

.transaction-desc {
  display: block;
  font-size: 34rpx;
  color: #000000;
  margin-bottom: 4rpx;
}

.transaction-category {
  font-size: 26rpx;
  color: #8E8E93;
}

.transaction-right {
  text-align: right;
}

.transaction-amount {
  display: block;
  font-size: 34rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.transaction-amount.income {
  color: #34C759;
}

.transaction-amount.expense {
  color: #FF3B30;
}

.transaction-time {
  font-size: 26rpx;
  color: #8E8E93;
}

.fab {
  position: fixed;
  bottom: 120rpx;
  right: 32rpx;
  width: 112rpx;
  height: 112rpx;
  background-color: #007AFF;
  border-radius: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);
  z-index: 1000;
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: 300;
}
