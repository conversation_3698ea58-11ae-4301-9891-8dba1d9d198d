<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElIcon } from 'element-plus'
import { ArrowDown, ArrowRight, Check, Food, ShoppingCart, VideoPlay, House, Van, Basketball, Suitcase,
  School, OfficeBuilding, Present, Money, Coin, DataAnalysis, Lightning, Coffee, Cellphone,
  CreditCard, Watch, Promotion, Bell, Umbrella, Wallet, Headset, Medal } from '@element-plus/icons-vue'

// 表单数据
const form = reactive({
  type: 'expense',
  amount: '',
  category: '',
  account_id: '',
  description: ''
})

// 选项数据
const transactionTypes = [
  { value: 'expense', label: '支出' },
  { value: 'income', label: '收入' },
  { value: 'transfer', label: '转账' }
]

const categories = [
  { value: 'food', label: '餐饮美食', icon: 'Food' },
  { value: 'transport', label: '交通出行', icon: 'Van' },
  { value: 'shopping', label: '购物消费', icon: 'ShoppingCart' },
  { value: 'life', label: '生活费', icon: 'Money' },
  { value: 'entertainment', label: '文化娱乐', icon: 'VideoPlay' },
  { value: 'medical', label: '医疗健康', icon: 'Bell' }
]

const accounts = [
  { id: 1, account_name: '招商银行储蓄卡', account_type: 'bank' },
  { id: 2, account_name: '支付宝', account_type: 'digital' },
  { id: 3, account_name: '微信钱包', account_type: 'digital' },
  { id: 4, account_name: '现金', account_type: 'cash' }
]

const getAccountTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'bank': '银行账户',
    'digital': '数字钱包',
    'cash': '现金'
  }
  return typeMap[type] || type
}

const showModal = ref(false)
const showTypePicker = ref(false)
const showCategoryPicker = ref(false)
const showAccountPicker = ref(false)

const openModal = () => {
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
}

const handleSave = () => {
  console.log('保存表单数据:', form)
  alert('交易保存成功！')
  closeModal()
}

// 选择器方法
const selectType = (type: string) => {
  form.type = type
  showTypePicker.value = false
}

const selectCategory = (category: string) => {
  form.category = category
  showCategoryPicker.value = false
}

const selectAccount = (accountId: number) => {
  form.account_id = accountId
  showAccountPicker.value = false
}

// 自动调整文本框高度
const descriptionTextarea = ref<HTMLTextAreaElement>()

const autoResizeTextarea = () => {
  const textarea = descriptionTextarea.value
  if (textarea) {
    textarea.style.height = 'auto'
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px' // 最大高度120px
  }
}

// 图标映射
const iconComponents = {
  'Food': Food,
  'ShoppingCart': ShoppingCart,
  'VideoPlay': VideoPlay,
  'House': House,
  'Van': Van,
  'Basketball': Basketball,
  'Suitcase': Suitcase,
  'School': School,
  'OfficeBuilding': OfficeBuilding,
  'Present': Present,
  'Money': Money,
  'Coin': Coin,
  'DataAnalysis': DataAnalysis,
  'Lightning': Lightning,
  'Coffee': Coffee,
  'Cellphone': Cellphone,
  'CreditCard': CreditCard,
  'Watch': Watch,
  'Promotion': Promotion,
  'Bell': Bell,
  'Umbrella': Umbrella,
  'Wallet': Wallet,
  'Headset': Headset,
  'Medal': Medal
}

// 获取图标组件
const getIconComponent = (iconName: string) => {
  return iconComponents[iconName as keyof typeof iconComponents] || null
}
</script>

<template>
  <div class="mobile-demo">
    <div class="demo-header">
      <h1>移动端交易表单优化演示</h1>
      <button class="demo-btn" @click="openModal">
        打开添加交易弹窗
      </button>
    </div>

    <!-- 优化后的移动端弹窗 -->
    <div v-if="showModal" class="ios-modal-overlay" @click="closeModal">
      <div class="ios-transaction-modal" @click.stop>
        <!-- 弹窗头部 -->
        <div class="ios-modal-header">
          <button class="ios-header-btn cancel-btn" @click="closeModal">
            取消
          </button>
          <h3 class="ios-modal-title">添加交易</h3>
          <button class="ios-header-btn confirm-btn" @click="handleSave">
            确定
          </button>
        </div>

        <!-- 弹窗内容 -->
        <div class="ios-modal-content">
          <!-- 类型字段 -->
          <div class="ios-form-group">
            <div class="ios-form-row" @click="showTypePicker = true">
              <div class="ios-form-label">类型</div>
              <div class="ios-form-value">
                <span class="ios-value-text">
                  {{ transactionTypes.find(t => t.value === form.type)?.label || '支出' }}
                </span>
                <el-icon class="ios-chevron"><ArrowRight /></el-icon>
              </div>
            </div>
          </div>

          <!-- 金额字段 -->
          <div class="ios-form-group">
            <div class="ios-form-row">
              <div class="ios-form-label">金额</div>
              <div class="ios-form-input">
                <input
                  v-model.number="form.amount"
                  type="number"
                  step="0.01"
                  min="0.01"
                  placeholder="0.00"
                  class="ios-amount-input"
                />
              </div>
            </div>
          </div>

          <!-- 分类字段 -->
          <div class="ios-form-group">
            <div class="ios-form-row" @click="showCategoryPicker = true">
              <div class="ios-form-label">分类</div>
              <div class="ios-form-value">
                <span class="ios-value-text" :class="{ 'ios-placeholder': !form.category }">
                  {{ categories.find(c => c.value === form.category)?.label || '请选择分类' }}
                </span>
                <el-icon class="ios-chevron"><ArrowRight /></el-icon>
              </div>
            </div>
          </div>

          <!-- 账户字段 -->
          <div class="ios-form-group">
            <div class="ios-form-row" @click="showAccountPicker = true">
              <div class="ios-form-label">账户</div>
              <div class="ios-form-value">
                <span class="ios-value-text" :class="{ 'ios-placeholder': !form.account_id }">
                  {{ accounts.find(a => a.id === form.account_id)?.account_name || '请选择账户' }}
                </span>
                <el-icon class="ios-chevron"><ArrowRight /></el-icon>
              </div>
            </div>
          </div>

          <!-- 描述字段 -->
          <div class="ios-form-group">
            <div class="ios-form-row ios-textarea-row">
              <div class="ios-form-label-with-count">
                <span class="ios-form-label">描述</span>
                <span class="char-count">{{ form.description?.length || 0 }}/50</span>
              </div>
              <div class="ios-form-textarea">
                <textarea
                  v-model="form.description"
                  placeholder="添加备注信息（可选）"
                  class="ios-textarea auto-resize"
                  maxlength="50"
                  @input="autoResizeTextarea"
                  ref="descriptionTextarea"
                ></textarea>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- iOS风格轻量选择器 -->
    <!-- 类型选择器 -->
    <div v-if="showTypePicker" class="ios-picker-overlay" @click="showTypePicker = false">
      <div class="ios-picker-modal" @click.stop>
        <div class="ios-picker-header">
          <button class="ios-picker-btn" @click="showTypePicker = false">取消</button>
          <span class="ios-picker-title">选择类型</span>
          <button class="ios-picker-btn confirm" @click="showTypePicker = false">完成</button>
        </div>
        <div class="ios-picker-content">
          <div
            v-for="type in transactionTypes"
            :key="type.value"
            class="ios-picker-item"
            :class="{ 'active': type.value === form.type }"
            @click="selectType(type.value)"
          >
            <span class="item-label">{{ type.label }}</span>
            <el-icon v-if="type.value === form.type" class="item-check">
              <Check />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 分类选择器 -->
    <div v-if="showCategoryPicker" class="ios-picker-overlay" @click="showCategoryPicker = false">
      <div class="ios-picker-modal" @click.stop>
        <div class="ios-picker-header">
          <button class="ios-picker-btn" @click="showCategoryPicker = false">取消</button>
          <span class="ios-picker-title">选择分类</span>
          <button class="ios-picker-btn confirm" @click="showCategoryPicker = false">完成</button>
        </div>
        <div class="ios-picker-content">
          <div
            v-for="category in categories"
            :key="category.value"
            class="ios-picker-item"
            :class="{ 'active': category.value === form.category }"
            @click="selectCategory(category.value)"
          >
            <div class="item-content">
              <div class="item-icon-wrapper">
                <el-icon v-if="getIconComponent(category.icon)" class="item-icon">
                  <component :is="getIconComponent(category.icon)" />
                </el-icon>
                <span v-else class="item-icon-text">{{ category.icon }}</span>
              </div>
              <span class="item-label">{{ category.label }}</span>
            </div>
            <el-icon v-if="category.value === form.category" class="item-check">
              <Check />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 账户选择器 -->
    <div v-if="showAccountPicker" class="ios-picker-overlay" @click="showAccountPicker = false">
      <div class="ios-picker-modal" @click.stop>
        <div class="ios-picker-header">
          <button class="ios-picker-btn" @click="showAccountPicker = false">取消</button>
          <span class="ios-picker-title">选择账户</span>
          <button class="ios-picker-btn confirm" @click="showAccountPicker = false">完成</button>
        </div>
        <div class="ios-picker-content">
          <div
            v-for="account in accounts"
            :key="account.id"
            class="ios-picker-item"
            :class="{ 'active': account.id === form.account_id }"
            @click="selectAccount(account.id)"
          >
            <div class="item-info">
              <span class="item-label">{{ account.account_name }}</span>
              <span class="item-type">{{ getAccountTypeLabel(account.account_type) }}</span>
            </div>
            <el-icon v-if="account.id === form.account_id" class="item-check">
              <Check />
            </el-icon>
          </div>
        </div>


      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.mobile-demo {
  padding: 20px;
  min-height: 100vh;
  background: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
  
  h1 {
    font-size: 24px;
    color: #333;
    margin-bottom: 20px;
  }
}

.demo-btn {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #0056CC;
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
}

/* iOS风格弹窗样式 */
.ios-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.ios-transaction-modal {
  background: white;
  border-radius: 20px;
  width: 100%;
  max-width: 400px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
}

.ios-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: white;
}

.ios-header-btn {
  background: none;
  border: none;
  font-size: 16px;
  color: #007AFF;
  cursor: pointer;
  padding: 4px 8px;
  
  &.cancel-btn {
    color: #007AFF;
  }

  &.confirm-btn {
    color: #007AFF;
    font-weight: 600;
  }
}

.ios-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}



.ios-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;

  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.ios-form-group {
  background: white;
  border-radius: 16px;
  margin: 0 16px 20px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.ios-form-row {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  min-height: 56px;
  transition: background-color 0.15s ease;

  &.ios-textarea-row {
    align-items: flex-start;
  }
}

.ios-form-label {
  font-size: 17px;
  color: #000000;
  font-weight: 400;
  min-width: 80px;
  flex-shrink: 0;
}

// iOS风格表单值样式
.ios-form-value {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  margin-left: 16px;

  .ios-value-text {
    font-size: 17px;
    color: #000000;
    text-align: right;

    &.ios-placeholder {
      color: #8e8e93;
    }
  }

  .ios-chevron {
    color: #c7c7cc;
    font-size: 14px;
  }
}

// iOS风格轻量选择器样式
.ios-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 3000;
  animation: fadeIn 0.3s ease;
}

.ios-picker-modal {
  background: white;
  border-radius: 20px 20px 0 0;
  width: 100%;
  max-width: 500px;
  max-height: 60vh;
  overflow: hidden;
  box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
  animation: slideUp 0.3s ease;
}

.ios-picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 0.5px solid #e5e5e7;
  background: #f8f9fa;
}

.ios-picker-btn {
  background: none;
  border: none;
  font-size: 16px;
  color: #007aff;
  cursor: pointer;
  padding: 4px 8px;

  &.confirm {
    font-weight: 600;
  }
}

.ios-picker-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.ios-picker-content {
  max-height: 300px;
  overflow-y: auto;
  background: white;

  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.ios-picker-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 0.5px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #f8f9fa;
  }

  &:active {
    background: #e9ecef;
  }

  &.active {
    background: rgba(0, 122, 255, 0.05);
  }

  &:last-child {
    border-bottom: none;
  }

  .item-content {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .item-icon-wrapper {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;

    .item-icon {
      font-size: 16px;
      color: #666;
    }

    .item-icon-text {
      font-size: 12px;
      color: #666;
    }
  }
}

.item-label {
  font-size: 16px;
  color: #333;
}

.item-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.item-type {
  font-size: 14px;
  color: #8e8e93;
}

.item-check {
  color: #007aff;
  font-size: 18px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.ios-form-input {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.ios-amount-input {
  border: none;
  background: transparent;
  font-size: 17px;
  color: #000;
  text-align: right;
  outline: none;
  width: 100%;
  
  &::placeholder {
    color: #8e8e93;
  }
}

.ios-form-label-with-count {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 80px;
  flex-shrink: 0;

  .ios-form-label {
    font-size: 17px;
    color: #000000;
    font-weight: 400;
  }

  .char-count {
    font-size: 12px;
    color: #8e8e93;
    margin-left: 8px;
  }
}

.ios-form-textarea {
  flex: 1;
  margin-left: 16px;
}

.ios-textarea {
  width: 100%;
  border: none;
  background: transparent;
  font-size: 16px;
  color: #333;
  outline: none;
  resize: none;
  font-family: inherit;
  min-height: 22px;
  max-height: 120px;
  overflow: hidden;

  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  &::placeholder {
    color: #8e8e93;
  }

  &.auto-resize {
    transition: height 0.2s ease;
  }
}


</style>
