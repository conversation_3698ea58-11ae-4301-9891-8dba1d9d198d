// pages/analytics/dashboard.js
const api = require('../../utils/api.js')

Page({
  data: {
    selectedMonth: '',
    selectedMonthText: '',
    monthlyIncome: '0.00',
    monthlyExpense: '0.00',
    monthlyBalance: 0,
    monthlyBalanceText: '¥0.00',
    expenseCategories: [],
    incomeCategories: [],
    loading: false,
    hideAmounts: false,
    trendData: [],
    trendLoading: false,
    budgetComparison: [],
    budgetLoading: false,
    budgetUsageRate: 0
  },

  onLoad() {
    console.log('数据分析页面加载')
    this.loadHideAmountsState()
    this.initMonth()
    // 暂时注释掉API调用，先确保页面能正常显示
    // this.loadAnalytics()
    // this.loadTrendData()
    // this.loadBudgetComparison()

    // 设置一些测试数据
    this.setData({
      monthlyIncome: '5000.00',
      monthlyExpense: '3000.00',
      monthlyBalance: 2000,
      monthlyBalanceText: '+¥2,000.00',
      expenseCategories: [
        {
          id: 1,
          name: '餐饮美食',
          icon: '🍽️',
          amount: 1200,
          amountText: '1,200.00',
          percentage: 40
        }
      ],
      incomeCategories: [
        {
          id: 1,
          name: '工资收入',
          icon: '💰',
          amount: 5000,
          amountText: '5,000.00',
          percentage: 100
        }
      ]
    })
  },

  onShow() {
    this.loadAnalytics()
    this.loadTrendData()
    this.loadBudgetComparison()
  },

  onPullDownRefresh() {
    this.loadAnalytics()
    this.loadTrendData()
    this.loadBudgetComparison()
  },

  // 初始化月份
  initMonth() {
    const now = new Date()
    const year = now.getFullYear()
    const month = now.getMonth() + 1
    const monthStr = `${year}-${month.toString().padStart(2, '0')}`

    this.setData({
      selectedMonth: monthStr,
      selectedMonthText: `${year}年${month}月`
    })
  },

  // 月份选择
  onMonthChange(e) {
    const selectedMonth = e.detail.value
    const [year, month] = selectedMonth.split('-')

    this.setData({
      selectedMonth: selectedMonth,
      selectedMonthText: `${year}年${parseInt(month)}月`
    })

    this.loadAnalytics()
    this.loadTrendData()
    this.loadBudgetComparison()
  },

  // 加载分析数据
  async loadAnalytics() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const [year, month] = this.data.selectedMonth.split('-')

      // 并行加载月度统计和分类统计
      const [monthlyStats, categoryStats] = await Promise.all([
        api.analytics.getMonthlyStats(parseInt(year), parseInt(month)),
        api.analytics.getCategoryStats({
          year: parseInt(year),
          month: parseInt(month)
        })
      ])

      // 处理月度统计
      const monthlyBalance = monthlyStats.total_income - monthlyStats.total_expense

      this.setData({
        monthlyIncome: this.formatAmount(monthlyStats.total_income || 0),
        monthlyExpense: this.formatAmount(monthlyStats.total_expense || 0),
        monthlyBalance: monthlyBalance,
        monthlyBalanceText: monthlyBalance >= 0 ?
          `+¥${this.formatAmount(monthlyBalance)}` :
          `-¥${this.formatAmount(Math.abs(monthlyBalance))}`
      })

      // 处理分类统计
      this.processCategoryStats(categoryStats)

    } catch (error) {
      console.error('加载分析数据失败:', error)

      if (!error.message.includes('登录已过期')) {
        wx.showToast({
          title: '数据加载失败',
          icon: 'none'
        })
      }
    } finally {
      this.setData({ loading: false })
      wx.stopPullDownRefresh()
    }
  },

  // 处理分类统计数据
  processCategoryStats(categoryStats) {
    const expenseCategories = []
    const incomeCategories = []

    // 处理支出分类
    if (categoryStats.expense_categories) {
      const totalExpense = categoryStats.expense_categories.reduce((sum, cat) => sum + cat.amount, 0)

      categoryStats.expense_categories.forEach(category => {
        const percentage = totalExpense > 0 ? ((category.amount / totalExpense) * 100).toFixed(1) : 0

        expenseCategories.push({
          id: category.id,
          name: category.name,
          icon: this.getCategoryIcon(category.name, 'expense'),
          amount: category.amount,
          amountText: this.formatAmount(category.amount),
          percentage: parseFloat(percentage)
        })
      })
    }

    // 处理收入分类
    if (categoryStats.income_categories) {
      const totalIncome = categoryStats.income_categories.reduce((sum, cat) => sum + cat.amount, 0)

      categoryStats.income_categories.forEach(category => {
        const percentage = totalIncome > 0 ? ((category.amount / totalIncome) * 100).toFixed(1) : 0

        incomeCategories.push({
          id: category.id,
          name: category.name,
          icon: this.getCategoryIcon(category.name, 'income'),
          amount: category.amount,
          amountText: this.formatAmount(category.amount),
          percentage: parseFloat(percentage)
        })
      })
    }

    this.setData({
      expenseCategories: expenseCategories.sort((a, b) => b.amount - a.amount),
      incomeCategories: incomeCategories.sort((a, b) => b.amount - a.amount)
    })
  },

  // 格式化金额
  formatAmount(amount) {
    return parseFloat(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  },

  // 获取分类图标
  getCategoryIcon(categoryName, type) {
    const iconMap = {
      '餐饮美食': '🍽️',
      '交通出行': '🚗',
      '购物消费': '🛍️',
      '生活服务': '🏠',
      '医疗健康': '🏥',
      '教育培训': '📚',
      '娱乐休闲': '🎮',
      '工资收入': '💰',
      '投资收益': '📈',
      '其他收入': '💵'
    }

    return iconMap[categoryName] || (type === 'income' ? '💰' : '💸')
  },

  // 加载隐藏金额状态
  loadHideAmountsState() {
    try {
      const hideAmounts = wx.getStorageSync('hideAmounts')
      this.setData({
        hideAmounts: hideAmounts === 'true' || hideAmounts === true
      })
    } catch (error) {
      console.error('加载隐藏金额状态失败:', error)
    }
  },

  // 切换金额显示/隐藏
  toggleAmountVisibility() {
    const newHideState = !this.data.hideAmounts
    this.setData({
      hideAmounts: newHideState
    })

    // 保存状态到本地存储
    try {
      wx.setStorageSync('hideAmounts', newHideState)
    } catch (error) {
      console.error('保存隐藏金额状态失败:', error)
    }
  },

  // 加载趋势数据
  async loadTrendData() {
    if (this.data.trendLoading) return

    this.setData({ trendLoading: true })

    try {
      const currentDate = new Date()
      const trendData = []
      let maxAmount = 0

      // 获取近6个月的数据
      for (let i = 5; i >= 0; i--) {
        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1)
        const year = date.getFullYear()
        const month = date.getMonth() + 1

        const monthlyStats = await api.analytics.getMonthlyStats(year, month)

        const income = monthlyStats.total_income || 0
        const expense = monthlyStats.total_expense || 0

        maxAmount = Math.max(maxAmount, income, expense)

        trendData.push({
          month: `${year}-${month.toString().padStart(2, '0')}`,
          monthText: `${month}月`,
          income: income,
          expense: expense,
          incomeText: this.formatAmount(income),
          expenseText: this.formatAmount(expense)
        })
      }

      // 计算柱状图高度百分比
      trendData.forEach(item => {
        item.incomeHeight = maxAmount > 0 ? (item.income / maxAmount * 100) : 0
        item.expenseHeight = maxAmount > 0 ? (item.expense / maxAmount * 100) : 0
      })

      this.setData({ trendData })
    } catch (error) {
      console.error('加载趋势数据失败:', error)
    } finally {
      this.setData({ trendLoading: false })
    }
  },

  // 预算管理
  goToBudget() {
    wx.navigateTo({
      url: '/pages/budget/index'
    })
  },

  // 跳转到详细分析页面
  goToDetailAnalysis() {
    wx.navigateTo({
      url: '/pages/analytics/detail'
    })
  },

  // 加载预算对比数据
  async loadBudgetComparison() {
    if (this.data.budgetLoading) return

    this.setData({ budgetLoading: true })

    try {
      const [year, month] = this.data.selectedMonth.split('-')

      // 调用预算API获取当月预算数据
      const budgetResponse = await api.budgets.getMonthlyBudgets(parseInt(year), parseInt(month))

      if (budgetResponse && budgetResponse.budgets && budgetResponse.budgets.length > 0) {
        // 处理真实的预算数据
        const budgetComparison = budgetResponse.budgets.map(budget => {
          const usageRate = budget.amount > 0 ? Math.round((budget.spent / budget.amount) * 100) : 0

          return {
            id: budget.id,
            name: budget.category?.name || '未分类',
            icon: this.getCategoryIcon(budget.category?.name || '未分类', 'expense'),
            budget: budget.amount,
            spent: budget.spent,
            usageRate: usageRate,
            budgetText: this.formatAmount(budget.amount),
            spentText: this.formatAmount(budget.spent)
          }
        })

        // 计算总体预算使用率
        const totalBudget = budgetResponse.summary?.total_budget || 0
        const totalSpent = budgetResponse.summary?.total_spent || 0
        const budgetUsageRate = totalBudget > 0 ? Math.round((totalSpent / totalBudget) * 100) : 0

        this.setData({
          budgetComparison,
          budgetUsageRate
        })
      } else {
        // 没有预算数据时清空
        this.setData({
          budgetComparison: [],
          budgetUsageRate: 0
        })
      }

    } catch (error) {
      console.error('加载预算对比数据失败:', error)

      // 如果API调用失败，使用模拟数据作为后备
      const mockBudgetData = [
        {
          id: 1,
          name: '餐饮美食',
          icon: '🍽️',
          budget: 2000,
          spent: 1800,
          usageRate: 90
        },
        {
          id: 2,
          name: '交通出行',
          icon: '🚗',
          budget: 1500,
          spent: 1200,
          usageRate: 80
        }
      ]

      const budgetComparison = mockBudgetData.map(item => ({
        ...item,
        budgetText: this.formatAmount(item.budget),
        spentText: this.formatAmount(item.spent)
      }))

      const totalBudget = mockBudgetData.reduce((sum, item) => sum + item.budget, 0)
      const totalSpent = mockBudgetData.reduce((sum, item) => sum + item.spent, 0)
      const budgetUsageRate = totalBudget > 0 ? Math.round((totalSpent / totalBudget) * 100) : 0

      this.setData({
        budgetComparison,
        budgetUsageRate
      })
    } finally {
      this.setData({ budgetLoading: false })
    }
  }
})
