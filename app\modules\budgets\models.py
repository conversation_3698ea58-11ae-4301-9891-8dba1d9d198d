from sqlalchemy import Column, Integer, String, Numeric, Foreign<PERSON>ey, Date, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime

from app.db.base_class import Base
from app.modules.users.models import User
from app.modules.categories.models import Category

class Budget(Base):
    """预算模型"""
    __tablename__ = "budgets"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    category_id = Column(Integer, ForeignKey("categories.id", ondelete="CASCADE"), nullable=False)
    amount = Column(Numeric(10, 2), nullable=False, comment="预算金额")
    spent = Column(Numeric(10, 2), default=0.00, comment="已使用金额")
    year = Column(Integer, nullable=False, comment="预算年份")
    month = Column(Integer, nullable=False, comment="预算月份")
    created_at = Column(Date, default=func.now(), nullable=False)
    updated_at = Column(Date, default=func.now(), onupdate=func.now(), nullable=False)
    
    # 关系
    user = relationship("User", back_populates="budgets")
    category = relationship("Category", back_populates="budgets")
    
    # 确保用户在同一年月的同一类别下只有一个预算
    __table_args__ = (
        UniqueConstraint('user_id', 'category_id', 'year', 'month', name='uix_budget_user_category_period'),
    )
    
    def remaining(self):
        """返回剩余预算"""
        return self.amount - self.spent
    
    def percentage_used(self):
        """返回预算使用百分比"""
        if self.amount > 0:
            return (self.spent / self.amount) * 100
        return 0 