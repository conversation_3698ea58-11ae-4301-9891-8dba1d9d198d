<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElIcon } from 'element-plus'
import { ArrowRight, ArrowDown, Close, Check, Food, ShoppingCart, VideoPlay, House, Van, Basketball, Suitcase,
  School, OfficeBuilding, Present, Money, Coin, DataAnalysis, Lightning, Coffee, Cellphone,
  CreditCard, Watch, Promotion, Bell, Umbrella, Wallet, Headset, Medal } from '@element-plus/icons-vue'

// 组件属性
interface Props {
  visible: boolean
  isEditing?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isEditing: false
})

// 事件定义
const emit = defineEmits<{
  close: []
  save: [data: any]
}>()

// 表单数据
const form = reactive({
  date: '2025/08/06 17:30',
  type: 'expense',
  amount: 0,
  category: '',
  account: '',
  description: ''
})

// 交易类型选项
const transactionTypes = [
  { value: 'expense', label: '支出' },
  { value: 'income', label: '收入' },
  { value: 'transfer', label: '转账' }
]

// 分类选项
const categories = [
  { value: '餐饮美食', label: '餐饮美食', icon: 'Food' },
  { value: '交通出行', label: '交通出行', icon: 'Van' },
  { value: '购物消费', label: '购物消费', icon: 'ShoppingCart' },
  { value: '生活服务', label: '生活服务', icon: 'House' },
  { value: '医疗健康', label: '医疗健康', icon: 'Bell' },
  { value: '文化娱乐', label: '文化娱乐', icon: 'VideoPlay' },
  { value: '其他', label: '其他', icon: 'OfficeBuilding' }
]

// 账户选项
const accounts = [
  '招商银行储蓄卡', '支付宝', '微信钱包', '现金'
]

// 状态管理
const submitting = ref(false)
const showDatePicker = ref(false)
const showTypePicker = ref(false)
const showCategoryPicker = ref(false)
const showAccountPicker = ref(false)

// 计算属性
const typeLabel = computed(() => {
  const type = transactionTypes.find(t => t.value === form.type)
  return type?.label || '支出'
})

// 方法
const handleOverlayClick = () => {
  emit('close')
}

const handleSave = async () => {
  if (!form.amount || form.amount <= 0) {
    alert('请输入有效金额')
    return
  }
  
  submitting.value = true
  
  try {
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    emit('save', { ...form })
    emit('close')
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    submitting.value = false
  }
}

const validateAmount = () => {
  if (form.amount < 0) {
    form.amount = 0
  }
}

// 选择器方法
const selectType = (type: string) => {
  form.type = type
  showTypePicker.value = false
}

const selectCategory = (category: string) => {
  form.category = category
  showCategoryPicker.value = false
}

const selectAccount = (account: string) => {
  form.account = account
  showAccountPicker.value = false
}

// 自动调整文本框高度
const descriptionTextarea = ref<HTMLTextAreaElement>()

const autoResizeTextarea = () => {
  const textarea = descriptionTextarea.value
  if (textarea) {
    textarea.style.height = 'auto'
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px' // 最大高度120px
  }
}

// 图标映射
const iconComponents = {
  'Food': Food,
  'ShoppingCart': ShoppingCart,
  'VideoPlay': VideoPlay,
  'House': House,
  'Van': Van,
  'Basketball': Basketball,
  'Suitcase': Suitcase,
  'School': School,
  'OfficeBuilding': OfficeBuilding,
  'Present': Present,
  'Money': Money,
  'Coin': Coin,
  'DataAnalysis': DataAnalysis,
  'Lightning': Lightning,
  'Coffee': Coffee,
  'Cellphone': Cellphone,
  'CreditCard': CreditCard,
  'Watch': Watch,
  'Promotion': Promotion,
  'Bell': Bell,
  'Umbrella': Umbrella,
  'Wallet': Wallet,
  'Headset': Headset,
  'Medal': Medal
}

// 获取图标组件
const getIconComponent = (iconName: string) => {
  return iconComponents[iconName as keyof typeof iconComponents] || null
}
</script>

<template>
  <!-- iOS风格弹窗遮罩 -->
  <div v-if="visible" class="ios-modal-overlay" @click="handleOverlayClick">
    <div class="ios-transaction-modal" @click.stop>
      <!-- 弹窗头部 -->
      <div class="ios-modal-header">
        <button class="ios-header-btn cancel-btn" @click="$emit('close')">
          取消
        </button>
        <h3 class="ios-modal-title">{{ isEditing ? '编辑交易' : '添加交易' }}</h3>
        <button class="ios-header-btn confirm-btn" @click="handleSave" :disabled="submitting">
          {{ submitting ? '保存中...' : '确定' }}
        </button>
      </div>

      <!-- 表单内容 -->
      <div class="ios-form-content">
        <!-- 日期字段 -->
        <div class="ios-form-group">
          <div class="ios-form-row" @click="showDatePicker = true">
            <div class="ios-form-label">日期</div>
            <div class="ios-form-value">
              <span class="ios-value-text">{{ form.date }}</span>
              <el-icon class="ios-chevron"><ArrowRight /></el-icon>
            </div>
          </div>
        </div>

        <!-- 类型字段 -->
        <div class="ios-form-group">
          <div class="ios-form-row" @click="showTypePicker = true">
            <div class="ios-form-label">类型</div>
            <div class="ios-form-value">
              <span class="ios-value-text">{{ typeLabel }}</span>
              <el-icon class="ios-chevron"><ArrowRight /></el-icon>
            </div>
          </div>
        </div>

        <!-- 金额字段 -->
        <div class="ios-form-group">
          <div class="ios-form-row">
            <div class="ios-form-label">金额</div>
            <div class="ios-form-input">
              <input
                v-model.number="form.amount"
                type="number"
                step="0.01"
                min="0.01"
                placeholder="0.00"
                class="ios-amount-input"
                @input="validateAmount"
              />
            </div>
          </div>
        </div>

        <!-- 分类字段 -->
        <div class="ios-form-group">
          <div class="ios-form-row" @click="showCategoryPicker = true">
            <div class="ios-form-label">分类</div>
            <div class="ios-form-value">
              <span class="ios-value-text" :class="{ 'ios-placeholder': !form.category }">
                {{ categories.find(c => c.value === form.category)?.label || '请选择分类' }}
              </span>
              <el-icon class="ios-chevron"><ArrowRight /></el-icon>
            </div>
          </div>
        </div>

        <!-- 账户字段 -->
        <div class="ios-form-group">
          <div class="ios-form-row" @click="showAccountPicker = true">
            <div class="ios-form-label">账户</div>
            <div class="ios-form-value">
              <span class="ios-value-text" :class="{ 'ios-placeholder': !form.account }">
                {{ form.account || '请选择账户' }}
              </span>
              <el-icon class="ios-chevron"><ArrowRight /></el-icon>
            </div>
          </div>
        </div>

        <!-- 描述字段 -->
        <div class="ios-form-group">
          <div class="ios-form-row ios-textarea-row">
            <div class="ios-form-label-with-count">
              <span class="ios-form-label">描述</span>
              <span class="char-count">{{ form.description?.length || 0 }}/50</span>
            </div>
            <div class="ios-form-textarea">
              <textarea
                v-model="form.description"
                placeholder="添加备注信息（可选）"
                class="ios-textarea auto-resize"
                maxlength="50"
                @input="autoResizeTextarea"
                ref="descriptionTextarea"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="ios-modal-footer">
        <button class="ios-btn ios-btn-cancel" @click="$emit('close')">
          取消
        </button>
        <button
          class="ios-btn ios-btn-primary"
          @click="handleSave"
          :disabled="submitting"
        >
          {{ submitting ? '保存中...' : '添加' }}
        </button>
      </div>
    </div>

    <!-- iOS风格轻量选择器 -->
    <!-- 类型选择器 -->
    <div v-if="showTypePicker" class="ios-picker-overlay" @click="showTypePicker = false">
      <div class="ios-picker-modal" @click.stop>
        <div class="ios-picker-header">
          <button class="ios-picker-btn" @click="showTypePicker = false">取消</button>
          <span class="ios-picker-title">选择类型</span>
          <button class="ios-picker-btn confirm" @click="showTypePicker = false">完成</button>
        </div>
        <div class="ios-picker-content">
          <div
            v-for="type in transactionTypes"
            :key="type.value"
            class="ios-picker-item"
            :class="{ 'active': type.value === form.type }"
            @click="selectType(type.value)"
          >
            <span class="item-label">{{ type.label }}</span>
            <el-icon v-if="type.value === form.type" class="item-check">
              <Check />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 分类选择器 -->
    <div v-if="showCategoryPicker" class="ios-picker-overlay" @click="showCategoryPicker = false">
      <div class="ios-picker-modal" @click.stop>
        <div class="ios-picker-header">
          <button class="ios-picker-btn" @click="showCategoryPicker = false">取消</button>
          <span class="ios-picker-title">选择分类</span>
          <button class="ios-picker-btn confirm" @click="showCategoryPicker = false">完成</button>
        </div>
        <div class="ios-picker-content">
          <div
            v-for="category in categories"
            :key="category.value"
            class="ios-picker-item"
            :class="{ 'active': category.value === form.category }"
            @click="selectCategory(category.value)"
          >
            <div class="item-content">
              <div class="item-icon-wrapper">
                <el-icon v-if="getIconComponent(category.icon)" class="item-icon">
                  <component :is="getIconComponent(category.icon)" />
                </el-icon>
                <span v-else class="item-icon-text">{{ category.icon }}</span>
              </div>
              <span class="item-label">{{ category.label }}</span>
            </div>
            <el-icon v-if="category.value === form.category" class="item-check">
              <Check />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 账户选择器 -->
    <div v-if="showAccountPicker" class="ios-picker-overlay" @click="showAccountPicker = false">
      <div class="ios-picker-modal" @click.stop>
        <div class="ios-picker-header">
          <button class="ios-picker-btn" @click="showAccountPicker = false">取消</button>
          <span class="ios-picker-title">选择账户</span>
          <button class="ios-picker-btn confirm" @click="showAccountPicker = false">完成</button>
        </div>
        <div class="ios-picker-content">
          <div
            v-for="account in accounts"
            :key="account"
            class="ios-picker-item"
            :class="{ 'active': account === form.account }"
            @click="selectAccount(account)"
          >
            <span class="item-label">{{ account }}</span>
            <el-icon v-if="account === form.account" class="item-check">
              <Check />
            </el-icon>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* iOS风格弹窗样式 */
.ios-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.ios-transaction-modal {
  width: 100%;
  max-width: 375px;
  max-height: 85vh;
  background: #f2f2f7;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

/* 弹窗头部 */
.ios-modal-header {
  background: #ffffff;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 0.5px solid #e5e5e7;
  flex-shrink: 0;

  .ios-header-btn {
    background: none;
    border: none;
    font-size: 17px;
    font-weight: 400;
    cursor: pointer;
    padding: 0;
    min-width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &.cancel-btn {
      color: #007aff;
    }

    &.confirm-btn {
      color: #007aff;
      font-weight: 600;

      &:disabled {
        color: #8e8e93;
        cursor: not-allowed;
      }
    }
  }

  .ios-modal-title {
    font-size: 17px;
    font-weight: 600;
    color: #000000;
    margin: 0;
    text-align: center;
  }

  .ios-header-placeholder {
    min-width: 44px;
  }
}

/* 表单内容区域 */
.ios-form-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 0;

  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.ios-form-group {
  background: #ffffff;
  border-radius: 12px;
  margin: 0 16px 20px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.ios-form-row {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  min-height: 56px;
  cursor: pointer;
  transition: background-color 0.15s ease;

  &:active {
    background-color: #f8f8f8;
  }

  &.ios-textarea-row {
    align-items: flex-start;
    cursor: default;
    
    &:active {
      background-color: transparent;
    }
  }
}

.ios-form-label {
  font-size: 17px;
  color: #000000;
  font-weight: 400;
  min-width: 80px;
  flex-shrink: 0;
}

.ios-form-value {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  margin-left: 16px;

  .ios-value-text {
    font-size: 17px;
    color: #000000;
    text-align: right;
    
    &.ios-placeholder {
      color: #8e8e93;
    }
  }

  .ios-chevron {
    color: #c7c7cc;
    font-size: 14px;
    flex-shrink: 0;
  }
}

.ios-form-input {
  flex: 1;
  margin-left: 16px;

  .ios-amount-input {
    width: 100%;
    border: none;
    background: transparent;
    font-size: 17px;
    color: #000000;
    text-align: right;
    outline: none;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

    &::placeholder {
      color: #8e8e93;
    }

    &:focus {
      color: #007aff;
    }
  }
}

.ios-form-label-with-count {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 80px;
  flex-shrink: 0;

  .ios-form-label {
    font-size: 17px;
    color: #000000;
    font-weight: 400;
  }

  .char-count {
    font-size: 12px;
    color: #8e8e93;
    margin-left: 8px;
  }
}

.ios-form-textarea {
  flex: 1;
  margin-left: 16px;
  margin-top: 4px;

  .ios-textarea {
    width: 100%;
    border: none;
    background: transparent;
    font-size: 17px;
    color: #000000;
    resize: none;
    outline: none;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.4;
    min-height: 22px;
    max-height: 120px;
    overflow: hidden;

    /* 隐藏滚动条 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }

    &::placeholder {
      color: #8e8e93;
    }

    &:focus {
      color: #000000;
    }

    &.auto-resize {
      transition: height 0.2s ease;
    }
  }
}

/* iOS风格轻量选择器样式 */
.ios-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 3000;
  animation: fadeIn 0.3s ease;
}

.ios-picker-modal {
  background: white;
  border-radius: 20px 20px 0 0;
  width: 100%;
  max-width: 500px;
  max-height: 60vh;
  overflow: hidden;
  box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
  animation: slideUp 0.3s ease;
}

.ios-picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 0.5px solid #e5e5e7;
  background: #f8f9fa;
}

.ios-picker-btn {
  background: none;
  border: none;
  font-size: 16px;
  color: #007aff;
  cursor: pointer;
  padding: 4px 8px;

  &.confirm {
    font-weight: 600;
  }
}

.ios-picker-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.ios-picker-content {
  max-height: 300px;
  overflow-y: auto;
  background: white;

  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.ios-picker-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 0.5px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #f8f9fa;
  }

  &:active {
    background: #e9ecef;
  }

  &.active {
    background: rgba(0, 122, 255, 0.05);
  }

  &:last-child {
    border-bottom: none;
  }

  .item-content {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .item-icon-wrapper {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;

    .item-icon {
      font-size: 16px;
      color: #666;
    }

    .item-icon-text {
      font-size: 12px;
      color: #666;
    }
  }
}

.item-label {
  font-size: 16px;
  color: #333;
}

.item-check {
  color: #007aff;
  font-size: 18px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 底部按钮 */
.ios-modal-footer {
  background: #ffffff;
  padding: 16px 20px 20px;
  display: flex;
  gap: 12px;
  flex-shrink: 0;
  border-top: 0.5px solid #e5e5e7;
}

.ios-btn {
  flex: 1;
  height: 50px;
  border: none;
  border-radius: 12px;
  font-size: 17px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.15s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  &.ios-btn-cancel {
    background: #f2f2f7;
    color: #000000;

    &:hover {
      background: #e5e5ea;
    }

    &:active {
      background: #d1d1d6;
      transform: scale(0.98);
    }
  }

  &.ios-btn-primary {
    background: #007aff;
    color: #ffffff;

    &:hover {
      background: #0056cc;
    }

    &:active {
      background: #004499;
      transform: scale(0.98);
    }

    &:disabled {
      background: #c7c7cc;
      color: #ffffff;
      cursor: not-allowed;
      transform: none;
    }
  }
}
</style>
