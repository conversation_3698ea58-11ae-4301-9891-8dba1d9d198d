<!-- iOS风格表单字段组件 -->
<view class="ios-form-field">
  <!-- 输入框类型 -->
  <view wx:if="{{type === 'input'}}" class="ios-field-group">
    <view class="ios-field-label">{{label}}</view>
    <view class="ios-input-container">
      <input 
        class="ios-input {{inputClass}}"
        placeholder="{{placeholder}}"
        value="{{value}}"
        type="{{inputType || 'text'}}"
        maxlength="{{maxlength || -1}}"
        bindinput="handleInput"
        bindfocus="handleFocus"
        bindblur="handleBlur"
      />
      <text wx:if="{{suffix}}" class="ios-input-suffix">{{suffix}}</text>
    </view>
  </view>

  <!-- 选择器类型 -->
  <view wx:elif="{{type === 'picker'}}" class="ios-field-group">
    <view class="ios-field-row" bindtap="handlePickerTap">
      <view class="ios-field-label">{{label}}</view>
      <view class="ios-field-value">
        <text class="ios-value-text {{value ? '' : 'placeholder'}}">
          {{displayText || placeholder}}
        </text>
        <text class="ios-chevron">></text>
      </view>
    </view>
  </view>

  <!-- 开关类型 -->
  <view wx:elif="{{type === 'switch'}}" class="ios-field-group">
    <view class="ios-field-row">
      <view class="ios-field-label">{{label}}</view>
      <view class="ios-field-value">
        <switch 
          checked="{{checked}}" 
          bindchange="handleSwitchChange"
          color="#007AFF"
        />
      </view>
    </view>
  </view>

  <!-- 文本显示类型 -->
  <view wx:elif="{{type === 'text'}}" class="ios-field-group">
    <view class="ios-field-row" bindtap="handleTextTap">
      <view class="ios-field-label">{{label}}</view>
      <view class="ios-field-value">
        <text class="ios-value-text">{{displayText || value}}</text>
        <text wx:if="{{showArrow}}" class="ios-chevron">></text>
      </view>
    </view>
  </view>
</view>
