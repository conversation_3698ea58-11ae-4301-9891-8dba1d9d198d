from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import JSONResponse
import logging
import traceback

from app.modules.users.schemas import User<PERSON><PERSON>, User, Token
from app.modules.users.service import UserService
from app.core.security import get_current_user
from app.db.session import get_db

# 设置日志
logger = logging.getLogger(__name__)

router = APIRouter()


@router.post(
    "/register",
    response_model=User,
    status_code=status.HTTP_201_CREATED,
    summary="Register a new user",
    tags=["users"]
)
async def register_user(
    user_in: UserCreate,
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new user in the system.
    """
    try:
        # 记录注册请求
        logger.info(f"收到注册请求: {user_in.username}, {user_in.email}")
        
        user_service = UserService()
        
        # 检查用户名是否已存在
        db_user = await user_service.get_user_by_username(db, user_in.username)
        if db_user:
            logger.warning(f"用户名已存在: {user_in.username}")
            raise HTTPException(
                status_code=400,
                detail="Username already registered",
            )
            
        # 检查邮箱是否已存在
        db_email = await user_service.get_user_by_email(db, user_in.email)
        if db_email:
            logger.warning(f"邮箱已存在: {user_in.email}")
            raise HTTPException(
                status_code=400,
                detail="Email already registered",
            )
        
        # 创建用户
        logger.info(f"创建新用户: {user_in.username}")
        user = await user_service.create_user(db=db, user=user_in)
        logger.info(f"用户创建成功: {user.id}")
        return user
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        # 记录详细错误信息
        logger.error(f"注册过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.post(
    "/login/access-token",
    response_model=Token,
    summary="Get access token for user",
    tags=["users"]
)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db)
):
    """
    OAuth2 compatible token login, get an access token for future requests.
    """
    try:
        logger.info(f"尝试登录: {form_data.username}")
        user_service = UserService()
        user = await user_service.get_user_by_username(db, form_data.username)
        if not user or not user_service.verify_password(form_data.password, user.password_hash):
            logger.warning(f"登录失败: {form_data.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect username or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        logger.info(f"登录成功: {form_data.username}")
        access_token = user_service.create_access_token(data={"sub": user.username})
        return {"access_token": access_token, "token_type": "bearer"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.get(
    "/me",
    response_model=User,
    summary="Get current user",
    tags=["users"]
)
async def read_users_me(
    current_user: User = Depends(get_current_user)
):
    """
    Fetch the current logged in user.
    """
    return current_user 


@router.post(
    "/logout",
    summary="Log out current user",
    tags=["users"]
)
async def logout_user():
    """
    Log out the current user. 
    This endpoint is just for API consistency and client-side can handle the actual logout by removing the token.
    """
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={"code": 0, "message": "Successfully logged out", "data": None}
    )
