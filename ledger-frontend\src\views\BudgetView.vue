<script setup lang="ts">
import { ref, computed, reactive, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import AppLayout from '../layouts/AppLayout.vue'
import { 
  ElCard, 
  ElRow, 
  ElCol, 
  ElProgress, 
  ElButton, 
  ElDialog, 
  ElForm, 
  ElFormItem, 
  ElInput, 
  ElSelect, 
  ElOption,
  ElDatePicker // 导入 ElDatePicker
} from 'element-plus'
import { getMonthlyBudgets, createBudget, updateBudget, deleteBudget } from '../api/budget'
import { getExpenseCategories } from '../api/category'
import type { Category } from '../api/category'
import type { BudgetWithCategory, BudgetSummary } from '../api/types'
// 移除 CustomDatePicker 的导入
// import CustomDatePicker from '../components/CustomDatePicker.vue'

// 数据加载状态
const loading = ref(false)

// 分类数据
const expenseCategories = ref<Category[]>([])

// 预算数据
const budgets = ref<BudgetWithCategory[]>([])
const budgetSummary = ref<BudgetSummary>({
  total_budget: 0,
  total_spent: 0,
  remaining: 0,
  usage_percentage: 0
})

// 当前选择的年月
const selectedDate = ref<Date>(new Date())

// 计算年月
const selectedYear = computed(() => selectedDate.value.getFullYear())
const selectedMonth = computed(() => selectedDate.value.getMonth() + 1)

// 格式化为YYYY-MM
const formattedMonth = computed(() => {
  return `${selectedYear.value}-${String(selectedMonth.value).padStart(2, '0')}`
})

// 特别添加: 清理底部导航栏的函数
const cleanupBottomElements = () => {
  // 寻找底部固定元素
  const bottomElements = document.querySelectorAll(
    '[style*="position:fixed"][style*="bottom"], ' +
    '[style*="position: fixed"][style*="bottom"], ' +
    '[class*="bottom-nav"], ' +
    '[class*="bottom-bar"], ' +
    '[class*="fab"], ' +
    '[class*="cursor"], ' +
    '.fixed-bottom'
  );
  
  // 移除找到的元素
  bottomElements.forEach(el => {
    try {
      if (el instanceof HTMLElement) {
        el.style.display = 'none';
        el.style.opacity = '0';
        el.style.visibility = 'hidden';
        el.style.zIndex = '-9999';
        el.style.height = '0';
        el.style.transform = 'translateY(100%)';
      }
    } catch (e) {
      console.error('清理底部元素失败:', e);
    }
  });
}

// 在组件挂载和更新后清理底部元素
onMounted(async () => {
  // 首先加载数据
  await Promise.all([loadCategories(), loadBudgetData()])
  
  // 然后处理底部元素
  // 延迟执行，确保所有DOM都已渲染
  setTimeout(cleanupBottomElements, 300);
  // 定期检查是否有新的底部元素
  const intervalId = setInterval(cleanupBottomElements, 2000);
  
  // 存储intervalId以便在组件卸载时清除
  (window as any).bottomCleanupInterval = intervalId;
})

// 在组件卸载时清除定时器
onUnmounted(() => {
  if ((window as any).bottomCleanupInterval) {
    clearInterval((window as any).bottomCleanupInterval);
  }
})

// 加载预算数据
const loadBudgetData = async () => {
  try {
    loading.value = true
    const response = await getMonthlyBudgets(selectedYear.value, selectedMonth.value)
    console.log('预算数据响应:', response)
    
    // 检查响应中是否直接包含所需的数据
    if (response && response.budgets && response.summary) {
      // 确保预算列表中的金额是数字
      budgets.value = (response.budgets || []).map((b: any) => ({
        ...b,
        amount: Number(b.amount) || 0,
        spent: Number(b.spent) || 0,
      }));
      // 确保汇总信息中的金额是数字
      budgetSummary.value = {
        total_budget: Number(response.summary.total_budget) || 0,
        total_spent: Number(response.summary.total_spent) || 0,
        remaining: Number(response.summary.remaining) || 0,
        usage_percentage: Number(response.summary.usage_percentage) || 0
      };
    } else {
      // 如果数据结构不匹配，进行降级处理
      console.warn('预算数据格式不符合预期, 进行手动计算');
      const budgetList = Array.isArray(response) ? response : (response && response.data) ? (response.data.budgets || []) : [];
      budgets.value = budgetList.map((b: any) => ({
        ...b,
        amount: Number(b.amount) || 0,
        spent: Number(b.spent) || 0,
      }));
      
      let totalBudget = 0
      let totalSpent = 0
      budgets.value.forEach(budget => {
        totalBudget += budget.amount;
        totalSpent += budget.spent;
      })
      budgetSummary.value = {
        total_budget: totalBudget,
        total_spent: totalSpent,
        remaining: totalBudget - totalSpent,
        usage_percentage: totalBudget > 0 ? (totalSpent / totalBudget * 100) : 0
      }
    }
  } catch (error) {
    console.error('获取预算数据出错:', error)
    // 发生错误时，确保数据被清空
    budgets.value = []
    budgetSummary.value = {
      total_budget: 0,
      total_spent: 0,
      remaining: 0,
      usage_percentage: 0
    }
  } finally {
    loading.value = false
  }
}

// 可用于创建预算的分类（即本月尚未设置预算的分类）
const availableCategories = computed(() => {
  const existingCategoryIds = new Set(budgets.value.map(b => b.category_id));
  return expenseCategories.value.filter(c => !existingCategoryIds.has(c.id));
});

// 加载分类数据
const loadCategories = async () => {
  try {
    const response = await getExpenseCategories()
    console.log('分类数据响应:', response)
    
    // 处理不同格式的响应
    if (Array.isArray(response)) {
      expenseCategories.value = response || []
    } else if (response.code === 200) {
      expenseCategories.value = Array.isArray(response.data) ? response.data : []
    } else {
      expenseCategories.value = []
    }
  } catch (error) {
    console.error('获取分类数据出错:', error)
    expenseCategories.value = []
  }
}

// 添加一个状态，跟踪日期选择器是否打开
const isDatePickerOpen = ref(false)

// 处理日期变更
const handleDateChange = (date: Date) => {
  console.log('日期变更:', date);
  selectedDate.value = date;
  loadBudgetData();
  
  // 关闭日期选择器弹窗
  setTimeout(() => {
    // 更强力的关闭方法
    const poppers = document.querySelectorAll('.el-picker__popper, .el-date-picker, .el-date-range-picker, .custom-date-picker__popper');
    poppers.forEach(popper => {
      if (popper instanceof HTMLElement) {
        popper.style.display = 'none';
        popper.style.visibility = 'hidden';
        popper.style.opacity = '0';
        popper.setAttribute('aria-hidden', 'true');
      }
    });
    
    // 清除遮罩层
    const masks = document.querySelectorAll('.el-overlay, .el-overlay-dialog');
    masks.forEach(mask => {
      if (mask instanceof HTMLElement) {
        mask.style.display = 'none';
      }
    });
  }, 100);
}

// 强制关闭日期选择器
const forceDatePickerClose = () => {
  // 查找所有日期选择器弹窗
  const poppers = document.querySelectorAll('.el-picker__popper, .el-picker-panel, .custom-date-picker__popper');
  poppers.forEach(popper => {
    if (popper instanceof HTMLElement) {
      popper.style.display = 'none';
      popper.style.opacity = '0';
      popper.style.visibility = 'hidden';
    }
  });
  
  // 重置状态
  isDatePickerOpen.value = false;
}

// 监听日期选择器打开状态
const checkDatePickerStatus = () => {
  const poppers = document.querySelectorAll('.el-picker__popper, .el-picker-panel, .custom-date-picker__popper');
  isDatePickerOpen.value = Array.from(poppers).some(popper => {
    if (popper instanceof HTMLElement) {
      const style = window.getComputedStyle(popper);
      return style.display !== 'none' && style.visibility !== 'hidden';
    }
    return false;
  });
}

// 新预算对话框
const budgetFormVisible = ref(false)
const budgetForm = reactive({
  category_id: 0,
  amount: 0
})

// 设置默认分类
const setDefaultCategory = () => {
  // 重置金额
  budgetForm.amount = 0
  
  // 设置默认分类为第一个可用分类
  if (availableCategories.value.length > 0) {
    budgetForm.category_id = availableCategories.value[0].id
  } else {
    budgetForm.category_id = 0
  }
}

// 监听表单可见性变化，确保打开时选择默认分类
watch(() => budgetFormVisible.value, (isVisible) => {
  if (isVisible) {
    // 使用nextTick确保在DOM更新后执行，这样availableCategories已经计算完毕
    nextTick(() => {
      setDefaultCategory();
    });
  }
});

// 打开预算表单
const openBudgetForm = () => {
  setDefaultCategory()
  budgetFormVisible.value = true
}

// 添加新预算
const addBudget = async () => {
  // 验证表单
  if (!budgetForm.category_id || budgetForm.amount <= 0) {
    ElMessage.warning('请选择分类并输入有效的预算金额')
    return
  }
  
  try {
    const response = await createBudget({
      category_id: budgetForm.category_id,
      amount: budgetForm.amount,
      year: selectedYear.value,
      month: selectedMonth.value
    })
    
    console.log('添加预算响应:', response)
    
    // 处理各种可能的成功响应格式
    if (response && (response.code === 200 || response.code === 201 || response.id)) {
      ElMessage.success('预算添加成功')
      resetBudgetForm()
      loadBudgetData() // 重新加载预算数据
    } else {
      console.error('添加预算失败, 服务器响应:', response)
      ElMessage.error(response?.message || '添加预算失败')
    }
  } catch (error) {
    console.error('添加预算出错:', error)
    ElMessage.error('添加预算失败，请稍后重试')
  }
}

// 更新预算
const updateBudgetAmount = async (id: number, newAmount: number) => {
  try {
    const response = await updateBudget(id, newAmount)
    if (response.code === 200) {
      ElMessage.success('预算更新成功')
      loadBudgetData() // 重新加载预算数据
    } else {
      ElMessage.error(response.message || '更新预算失败')
    }
  } catch (error) {
    console.error('更新预算出错:', error)
    ElMessage.error('更新预算失败，请稍后重试')
  }
}

// 删除预算
const removeBudget = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个预算吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await deleteBudget(id)
    if (response.code === 200) {
      ElMessage.success('预算删除成功')
      loadBudgetData() // 重新加载预算数据
    } else {
      ElMessage.error(response.message || '删除预算失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除预算出错:', error)
      ElMessage.error('删除预算失败，请稍后重试')
    }
  }
}

// 重置预算表单
const resetBudgetForm = () => {
  budgetForm.category_id = 0
  budgetForm.amount = 0
  budgetFormVisible.value = false
}

// 编辑预算
const editBudget = (budget: BudgetWithCategory) => {
  ElMessageBox.prompt('请输入新的预算金额', '编辑预算', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^\d+(\.\d{1,2})?$/,
    inputErrorMessage: '请输入有效的金额',
    inputValue: budget.amount.toString()
  }).then(({ value }) => {
    updateBudgetAmount(budget.id, parseFloat(value))
  }).catch(() => {})
}

// 获取进度条状态
const getProgressStatus = (percentage: number) => {
  if (percentage >= 100) {
    return 'exception'
  } else if (percentage >= 80) {
    return 'warning'
  } else {
    return 'success'
  }
}

// 组件挂载时加载数据
// onMounted(async () => {
//   await Promise.all([loadCategories(), loadBudgetData()])
// })

// 添加强制关闭所有日期选择器的方法
const forceCloseAllDatePickers = () => {
  // 移除所有日期选择器弹窗
  const poppers = document.querySelectorAll('.el-picker__popper, .el-date-picker, .el-date-range-picker, .custom-date-picker__popper');
  poppers.forEach(popper => {
    if (popper instanceof HTMLElement) {
      popper.style.display = 'none';
      popper.style.visibility = 'hidden';
      popper.style.opacity = '0';
      
      // 同时移除该弹窗的相关数据属性
      document.querySelectorAll('[data-v-owner-id], [aria-owns]').forEach(el => {
        el.removeAttribute('data-v-owner-id');
        el.removeAttribute('aria-owns');
        el.removeAttribute('aria-expanded');
      });
    }
  });
  
  // 尝试清除遮罩层
  const masks = document.querySelectorAll('.el-overlay, .el-overlay-dialog');
  masks.forEach(mask => {
    if (mask instanceof HTMLElement) {
      mask.style.display = 'none';
      mask.style.visibility = 'hidden';
      mask.style.opacity = '0';
    }
  });
};
</script>

<template>
  <AppLayout>
    <!-- 页面标题和操作按钮部分保持不变 -->
    <div class="page-header">
      <h1 class="page-title">预算管理</h1>
      <div class="page-actions">
        <!-- 直接使用 ElDatePicker，并设置 size="large" -->
        <el-date-picker
          v-model="selectedDate"
          type="month"
          placeholder="选择月份"
          format="YYYY-MM"
          @change="handleDateChange"
          class="budget-date-picker"
          size="large"
        />
        <el-button type="primary" @click="openBudgetForm" class="add-budget-btn" size="large">
          添加预算
        </el-button>
      </div>
    </div>
    
    <!-- 总体预算卡片 -->
    <el-card class="total-budget-card" v-loading="loading">
      <div class="total-budget-info">
        <div class="budget-summary">
          <h3>本月总预算</h3>
          <div class="budget-amount">¥{{ budgetSummary.total_budget.toFixed(2) }}</div>
        </div>
        <div class="budget-summary">
          <h3>已使用</h3>
          <div class="budget-amount" :class="{ 'over-budget': budgetSummary.remaining < 0 }">
            ¥{{ budgetSummary.total_spent.toFixed(2) }}
          </div>
        </div>
        <div class="budget-summary">
          <h3>剩余预算</h3>
          <div class="budget-amount" :class="{ 'over-budget': budgetSummary.remaining < 0 }">
            ¥{{ budgetSummary.remaining.toFixed(2) }}
          </div>
        </div>
      </div>
      
      <div class="total-progress-container">
        <div class="progress-labels">
          <span>总体使用进度: {{ Math.round(budgetSummary.usage_percentage) }}%</span>
          <span>{{ budgetSummary.remaining < 0 ? '超出预算' : '' }}</span>
        </div>
        <el-progress 
          :percentage="budgetSummary.usage_percentage > 100 ? 100 : budgetSummary.usage_percentage" 
          :status="getProgressStatus(budgetSummary.usage_percentage)" 
          :stroke-width="20"
        />
      </div>
    </el-card>
    
    <!-- 分类预算卡片 -->
    <div class="budget-container" v-loading="loading">
      <template v-if="budgets.length > 0">
        <div v-for="budget in budgets" :key="budget.id" class="budget-item">
          <div class="budget-card">
            <!-- 预算卡片头部 -->
            <div class="budget-card-header">
              <div class="budget-title-section">
                <div class="category-icon" :style="{ backgroundColor: budget.category_color }">
                  <span class="icon-wallet">💰</span>
                </div>
                <div class="category-info">
                  <h3>{{ budget.category_name }}</h3>
                  <div class="budget-period">{{ formattedMonth }}</div>
                </div>
              </div>
              <div class="budget-actions">
                <button class="icon-button" @click="editBudget(budget)">
                  <span class="icon-edit">✏️</span>
                </button>
                <button class="icon-button" @click="removeBudget(budget.id)">
                  <span class="icon-delete">🗑️</span>
                </button>
              </div>
            </div>
            
            <!-- 预算进度条 -->
            <div class="budget-progress">
              <div class="progress-info">
                <span>使用进度</span>
                <span class="progress-percentage" :class="{ 'over-budget': budget.spent > budget.amount }">
                  {{ Math.round(budget.spent / budget.amount * 100) }}%
                </span>
              </div>
              <el-progress 
                :percentage="budget.spent / budget.amount * 100 > 100 ? 100 : budget.spent / budget.amount * 100" 
                :status="getProgressStatus(budget.spent / budget.amount * 100)"
                :stroke-width="10"
              />
            </div>
            
            <!-- 预算详情 -->
            <div class="budget-details">
              <div class="budget-detail">
                <div class="label">预算金额</div>
                <div class="amount">¥{{ budget.amount.toFixed(2) }}</div>
              </div>
              <div class="budget-detail">
                <div class="label">已使用</div>
                <div class="amount" :class="{ 'over-budget': budget.spent > budget.amount }">
                  ¥{{ budget.spent.toFixed(2) }}
                </div>
              </div>
              <div class="budget-detail">
                <div class="label">剩余</div>
                <div class="amount" :class="{ 'over-budget': budget.spent > budget.amount }">
                  ¥{{ (budget.amount - budget.spent).toFixed(2) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      
      <el-card v-else class="empty-card">
        <div class="empty-tip">
          <div class="empty-icon">
            <span class="icon-money">💵</span>
          </div>
          <h3>暂无预算数据</h3>
          <p>您尚未为 {{ formattedMonth }} 设置任何预算。</p>
          <el-button type="primary" @click="openBudgetForm">创建预算</el-button>
        </div>
      </el-card>
    </div>
    
    <!-- 添加预算对话框部分保持不变 -->
    <el-dialog 
      v-model="budgetFormVisible" 
      title="添加预算" 
      width="90%" 
      custom-class="mobile-dialog"
      center
      modal-class="mobile-dialog-modal"
    >
      <el-form :model="budgetForm" label-position="top">
        <el-form-item label="分类">
          <el-select 
            v-model="budgetForm.category_id" 
            style="width: 100%" 
            placeholder="请选择一个分类"
            filterable
          >
            <el-option
              v-for="category in availableCategories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            >
              <div style="display: flex; align-items: center;">
                <span class="color-dot" :style="{ backgroundColor: category.color }"></span>
                <span>{{ category.name }}</span>
              </div>
            </el-option>
          </el-select>
          <div v-if="availableCategories.length === 0 && expenseCategories.length > 0" class="empty-categories-tip">
            所有分类本月均已设置预算。
          </div>
          <div v-else-if="availableCategories.length > 0" class="category-hint">
            已自动选择第一个可用分类
          </div>
        </el-form-item>
        
        <el-form-item label="预算金额">
          <el-input v-model="budgetForm.amount" type="number" min="0" step="0.01" />
        </el-form-item>
        
        <el-form-item label="预算月份">
          <el-input v-model="formattedMonth" disabled />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetBudgetForm">取消</el-button>
          <el-button type="primary" @click="addBudget">添加</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 底部遮罩元素，用于遮盖可能出现的底部导航 -->
    <div class="bottom-overlay"></div>
    
    <!-- 添加一个遮罩层，用于在日期选择器打开时点击关闭 -->
    <div v-if="isDatePickerOpen" class="date-picker-overlay" @click="forceDatePickerClose"></div>
  </AppLayout>
</template>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.page-title {
  font-weight: 600;
  color: var(--apple-dark-gray);
  font-size: 28px;
  margin: 0;
}

.page-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.total-budget-card {
  margin-bottom: var(--spacing-lg);
  
  .total-budget-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
    
    .budget-summary {
      text-align: center;
      
      h3 {
        font-size: 14px;
        color: var(--apple-gray);
        margin: 0 0 8px 0;
        font-weight: normal;
      }
      
      .budget-amount {
        font-size: 24px;
        font-weight: 600;
        
        &.over-budget {
          color: var(--apple-red);
        }
      }
    }
  }
  
  .total-progress-container {
    .progress-labels {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 14px;
      color: var(--apple-gray);
      
      span:last-child {
        color: var(--apple-red);
        font-weight: 600;
      }
    }
  }
}

.budget-container {
  margin-bottom: var(--spacing-lg);
}

.budget-item {
  margin-bottom: var(--spacing-lg);
}

.budget-card {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  background-color: #fff;
  
  .budget-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .budget-title-section {
    display: flex;
    align-items: center;
  }
  
  .category-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    margin-right: var(--spacing-md);
  }
  
  .category-info {
    h3 {
      margin: 0 0 4px 0;
      font-size: 16px;
    }
    
    .budget-period {
      font-size: 12px;
      color: var(--apple-gray);
    }
  }
  
  .budget-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .icon-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 6px;
    height: 36px;
    width: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }
  
  .icon-edit, .icon-delete {
    font-size: 18px;
    display: block;
    line-height: 1;
  }
  
  .budget-progress {
    margin-bottom: 16px;
  }
  
  .progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
    color: var(--apple-gray);
  }
  
  .progress-percentage {
    font-weight: 600;
    font-size: 14px;
    
    &.over-budget {
      color: var(--apple-red);
    }
  }
  
  .budget-details {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    
    .budget-detail {
      text-align: center;
      
      .label {
        font-size: 13px;
        color: var(--apple-gray);
        margin-bottom: 4px;
      }
      
      .amount {
        font-weight: 600;
        font-size: 15px;
        
        &.over-budget {
          color: var(--apple-red);
        }
      }
    }
  }
}

.empty-card {
  .empty-tip {
    padding: 40px 0;
    text-align: center;
    
    .empty-icon {
      font-size: 40px;
      color: #ccc;
      margin-bottom: 16px;
    }
    
    h3 {
      font-size: 18px;
      margin: 0 0 8px 0;
    }
    
    p {
      color: #999;
      margin-bottom: 20px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

.color-dot {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
}

.empty-categories-tip {
  color: var(--el-text-color-secondary);
  font-size: 12px;
  margin-top: 4px;
}

.category-hint {
  color: var(--apple-blue);
  font-size: 12px;
  margin-top: 4px;
  font-style: italic;
}

/* 移动端适配样式 */
@media screen and (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
  
  .page-actions {
    width: 100%;
    flex-direction: column;
    gap: var(--spacing-sm);
    
    .add-budget-btn {
      width: 100%;
      // 移除固定的高度和字体大小样式
    }
  }
  
  .total-budget-card {
    .total-budget-info {
      flex-direction: column;
      gap: var(--spacing-md);
      
      .budget-summary {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        h3 {
          margin: 0;
        }
        
        .budget-amount {
          font-size: 18px;
        }
      }
    }
  }
  
  .budget-card {
    margin-bottom: 16px;
    padding: 14px;
    
    .budget-card-header {
      margin-bottom: 14px;
    }
    
    .category-icon {
      width: 36px;
      height: 36px;
      font-size: 16px;
    }
    
    .category-info {
      h3 {
        font-size: 15px;
        font-weight: 600;
      }
      
      .budget-period {
        font-size: 11px;
      }
    }
    
    .icon-button {
      height: 32px;
      width: 32px;
      padding: 4px;
    }
    
    .icon-edit, .icon-delete {
      font-size: 16px;
    }
    
    .budget-progress {
      margin: 12px 0;
    }
    
    .progress-info {
      margin-bottom: 6px;
    }
    
    .progress-percentage {
      font-size: 14px;
    }
    
    .budget-details {
      grid-template-columns: repeat(3, 1fr);
      gap: 5px;
      
      .budget-detail {
        .label {
          font-size: 12px;
          margin-bottom: 3px;
        }
        
        .amount {
          font-size: 14px;
        }
      }
    }
  }
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-md);
  }
  
  .icon-wallet, .icon-money {
    font-size: 18px;
  }
  
  .empty-card .empty-tip {
    padding: var(--spacing-lg) var(--spacing-md);
  }
  
  .mobile-dialog {
    .el-dialog {
      margin: 0 !important;
      max-height: 90vh;
      display: flex;
      flex-direction: column;
    }

    .el-dialog__body {
      padding: var(--spacing-md);
      flex: 1;
      overflow-y: auto;
    }
    
    .dialog-footer {
      width: 100%;
      display: flex;
      gap: 24px;
      padding: 16px var(--spacing-md);
      
      button {
        flex: 1;
        padding: 16px 20px;
        font-size: 16px;
        border-radius: 12px;
      }
    }
  }

  .mobile-dialog-modal {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  // 移除底部多余的导航栏和按钮
  .app-layout {
    padding-bottom: 0;
    margin-bottom: 0;
  }
  
  // 确保内容区域不被底部遮挡
  .content {
    padding-bottom: 20px;
    margin-bottom: 0;
    min-height: 100vh;
  }
  
  // 消除底部固定元素
  .fixed-bottom, 
  [class*="bottom-bar"],
  [class*="bottom-nav"],
  .footer-actions {
    display: none !important;
  }
  
  // 确保卡片容器不会被遮挡
  .budget-container {
    padding-bottom: 20px;
    margin-bottom: 0;
  }
  
  // 避免内容区域被截断
  .app-layout > .main-container {
    overflow: visible;
  }
}

/* 添加底部遮罩元素样式 */
.bottom-overlay {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: #f9f9fb;
  z-index: 9999;
  display: none;
}

/* 添加日期选择器相关样式 */
.date-picker-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
}

.date-picker-close-btn {
  position: relative;
  margin-top: 5px;
  background-color: #f56c6c;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  
  &:hover {
    background-color: #f78989;
  }
}

/* 遮罩层样式 */
.date-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 2000;
}

/* 确保日期选择器弹窗在遮罩层之上 */
:deep(.el-picker-panel),
:deep(.el-date-picker),
:deep(.custom-date-picker__popper) {
  z-index: 2001 !important;
}

@media screen and (max-width: 768px) {
  .bottom-overlay {
    display: block;
  }
  
  /* 确保内容不被遮挡 */
  .budget-container {
    padding-bottom: 60px;
  }
  
  /* 深色模式适配 */
  :global(.dark-mode) .bottom-overlay {
    background-color: #1e1e2e;
  }
  
  /* 其他移动端样式保持不变 */

  /* 调整日期选择器在移动端的样式 */
  .date-picker-wrapper {
    width: 100%;
    margin-bottom: 10px;
  }
  
  .date-picker-close-btn {
    width: 100%;
    height: 36px;
  }
}

/* 为新的日期选择器添加样式 */
.budget-date-picker {
  width: 150px; // 设置一个合适的宽度
  transition: width 0.3s ease-in-out;
}

/* 确保在移动端下正确显示 */
@media screen and (max-width: 768px) {
  .page-actions {
    .budget-date-picker {
      width: 100%; // 移动端撑满宽度
      // 移除固定的高度样式

      /* 直接深度选择Element Plus组件内部的input框 */
      &:deep(.el-input__wrapper) {
        width: 100% !important;
      }
      
      &:deep(.el-input) {
        width: 100% !important;
      }
    }

    .add-budget-btn {
      width: 100%;
      // 移除固定的高度和字体大小样式
    }
  }
}
</style> 