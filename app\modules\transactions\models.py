from sqlalchemy import Column, Integer, String, Numeric, DateTime, Enum, ForeignKey
from sqlalchemy.orm import relationship
from app.db.base_class import Base
from app.modules.accounts.models import Account
from app.modules.users.models import User
from app.modules.categories.models import Category
import datetime


class Transaction(Base):
    __tablename__ = "transactions"
    
    id = Column(Integer, primary_key=True, index=True, comment='交易ID')
    account_id = Column(Integer, ForeignKey('accounts.id', ondelete="CASCADE"), nullable=False, comment='关联的账户ID')
    user_id = Column(Integer, ForeignKey('users.id', ondelete="CASCADE"), nullable=False, comment='关联的用户ID')
    category_id = Column(Integer, ForeignKey('categories.id'), nullable=True, comment='关联的分类ID')
    transaction_date = Column(DateTime, nullable=False, comment='交易日期时间')
    amount = Column(Numeric(15, 2), nullable=False, comment='交易金额')
    transaction_type = Column(
        Enum('income', 'expense', 'transfer', 'investment', 'interest', 'dividend', 'fee', name='transaction_type_enum'),
        nullable=False,
        comment='交易类型'
    )
    description = Column(String(255), nullable=True, comment='交易描述')
    from_account_id = Column(Integer, ForeignKey('accounts.id', ondelete="SET NULL"), nullable=True, comment='转账来源账户ID')
    to_account_id = Column(Integer, ForeignKey('accounts.id', ondelete="SET NULL"), nullable=True, comment='转账目标账户ID')
    created_at = Column(DateTime, default=datetime.datetime.utcnow, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow, comment='更新时间')
    
    # 关系 - 修改为延迟加载以避免异步问题
    user = relationship("User")
    account = relationship("Account", foreign_keys=[account_id])
    # 不要在异步环境中直接使用这个关系，通过ID查询替代
    category = relationship("Category", lazy="selectin")
    from_account = relationship("Account", foreign_keys=[from_account_id])
    to_account = relationship("Account", foreign_keys=[to_account_id])