<script setup lang="ts">
import { ref, reactive, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElForm, ElFormItem, ElInput, ElButton, ElCard, ElMessage, ElCheckbox, ElDialog } from 'element-plus'
import { useUserStore } from '@/stores/user'
import axios from '@/api/config'
import { sendRegisterVerificationCode, verifyEmail, resendVerificationEmail, forgotPassword, resetPassword } from '@/api/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()



// 登录表单
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

// 注册表单
const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  email_verification_code: ''
})

// 注册表单校验规则
const registerRules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度需在3到20个字符之间', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { 
      validator: (rule: any, value: string, callback: any) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!value || emailRegex.test(value)) {
          callback()
        } else {
          callback(new Error('请输入有效的邮箱地址'))
        }
      }, 
      trigger: 'blur' 
    }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 30, message: '密码长度需在6到30个字符之间', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value !== registerForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  email_verification_code: [
    { required: true, message: '请输入邮箱验证码', trigger: 'blur' },
    { len: 6, message: '验证码必须是6位数字', trigger: 'blur' }
  ]
})

// 控制显示登录还是注册表单
const isLogin = ref(true)
const isLoading = ref(false)
const registerFormRef = ref()

// 验证码发送状态
const isCodeSending = ref(false)
const codeCountdown = ref(0)
const codeTimer = ref<NodeJS.Timeout | null>(null)

// 密码重置相关状态
const showForgotPasswordDialog = ref(false)
const showResetPasswordDialog = ref(false)

// 忘记密码表单
const forgotPasswordForm = reactive({
  email: ''
})

// 重置密码表单
const resetPasswordForm = reactive({
  email: '',
  reset_code: '',
  new_password: '',
  confirm_password: ''
})



// 登录处理
const handleLogin = async () => {
  if (!loginForm.username || !loginForm.password) {
    ElMessage.warning('请输入用户名和密码')
    return
  }
  
  isLoading.value = true
  
  try {
    // 调用store的登录方法
    const success = await userStore.userLogin({
      username: loginForm.username,
      password: loginForm.password
    })

    if (success) {
      // 如果有重定向路径，则跳转到该路径
      const redirectPath = route.query.redirect as string || '/'
      router.push(redirectPath)
    }
    // 登录失败的错误提示已在axios拦截器中处理，这里不需要重复显示
  } catch (error: any) {
    console.error('登录过程中发生错误:', error)
    // 网络错误等非HTTP错误的处理
    if (!error.response) {
      ElMessage.error('网络连接失败，请检查网络后重试')
    }
  } finally {
    isLoading.value = false
  }
}

// 注册处理
const handleRegister = async () => {
  if (!registerFormRef.value) return
  
  try {
    // 表单验证
    await registerFormRef.value.validate()
    
    isLoading.value = true
    
    console.log('正在注册用户...');
    
    // 直接使用我们配置好的axios实例发送请求
    // 路径应该是相对于baseURL ('/api/v1') 的
    const response = await axios.post('/users/register', {
      username: registerForm.username,
      email: registerForm.email,
      password: registerForm.password,
      email_verification_code: registerForm.email_verification_code
    });

    if (response) { // 后端成功（201）会直接返回用户对象
      ElMessage.success('注册成功！现在可以登录了')
      // 切换到登录表单
      isLogin.value = true
      // 预填充用户名
      loginForm.username = registerForm.username
      // 清空注册表单
      Object.assign(registerForm, {
        username: '',
        email: '',
        password: '',
        confirmPassword: '',
        email_verification_code: ''
      })
    }
  } catch(err: any) {
    console.error('注册过程中捕获到错误:', err)
    // 错误处理逻辑已在axios拦截器中实现
    // 此处无需重复显示ElMessage
  } finally {
    isLoading.value = false
  }
}

// 发送注册验证码
const sendRegisterCode = async () => {
  if (!registerForm.email) {
    ElMessage.warning('请先输入邮箱地址')
    return
  }

  // 简单的邮箱格式验证
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(registerForm.email)) {
    ElMessage.warning('请输入有效的邮箱地址')
    return
  }

  try {
    isCodeSending.value = true
    await sendRegisterVerificationCode({ email: registerForm.email })
    ElMessage.success('验证码已发送，请检查您的邮箱')

    // 开始倒计时
    codeCountdown.value = 60
    codeTimer.value = setInterval(() => {
      codeCountdown.value--
      if (codeCountdown.value <= 0) {
        clearInterval(codeTimer.value!)
        codeTimer.value = null
      }
    }, 1000)

  } catch (error: any) {
    console.error('发送验证码失败:', error)
    // 虽然axios拦截器会处理错误，但为了确保用户看到明确的提示，这里也添加处理
    if (error.response?.status === 400) {
      const errorMsg = error.response?.data?.detail || ''
      if (errorMsg.includes('已被注册') || errorMsg.includes('already registered')) {
        ElMessage.error('该邮箱已被注册，请输入新的邮箱地址')
      } else {
        ElMessage.error('发送验证码失败，请检查邮箱地址')
      }
    } else if (!error.response) {
      ElMessage.error('网络连接失败，请检查网络后重试')
    }
  } finally {
    isCodeSending.value = false
  }
}



// 忘记密码处理
const handleForgotPassword = async () => {
  if (!forgotPasswordForm.email) {
    ElMessage.warning('请输入邮箱地址')
    return
  }

  try {
    isLoading.value = true
    await forgotPassword({ email: forgotPasswordForm.email })
    ElMessage.success('密码重置邮件已发送，请检查您的邮箱')
    showForgotPasswordDialog.value = false
    // 准备重置密码表单
    resetPasswordForm.email = forgotPasswordForm.email
    showResetPasswordDialog.value = true
  } catch (error) {
    console.error('发送密码重置邮件失败:', error)
  } finally {
    isLoading.value = false
  }
}

// 重置密码处理
const handleResetPassword = async () => {
  if (!resetPasswordForm.email || !resetPasswordForm.reset_code || !resetPasswordForm.new_password) {
    ElMessage.warning('请填写所有必填字段')
    return
  }

  if (resetPasswordForm.new_password !== resetPasswordForm.confirm_password) {
    ElMessage.warning('两次输入的密码不一致')
    return
  }

  try {
    isLoading.value = true
    await resetPassword({
      email: resetPasswordForm.email,
      reset_code: resetPasswordForm.reset_code,
      new_password: resetPasswordForm.new_password
    })

    ElMessage.success('密码重置成功！请使用新密码登录')
    showResetPasswordDialog.value = false
    // 清空表单
    resetPasswordForm.reset_code = ''
    resetPasswordForm.new_password = ''
    resetPasswordForm.confirm_password = ''
  } catch (error) {
    console.error('密码重置失败:', error)
  } finally {
    isLoading.value = false
  }
}

// 切换到登录表单
const showLogin = () => {
  isLogin.value = true
}

// 切换到注册表单
const showRegister = () => {
  isLogin.value = false
}

// 组件销毁时清理定时器
onUnmounted(() => {
  if (codeTimer.value) {
    clearInterval(codeTimer.value)
  }
})

</script>

<template>
  <div class="login-container">
    <div class="login-wrapper">
      <div class="logo-section">
        <div class="logo">金融账本</div>
        <div class="slogan">轻松管理您的财务</div>
      </div>
      
      <el-card class="login-card">
        <div class="form-header">
          <h2>{{ isLogin ? '登录' : '注册' }}</h2>
          <div class="form-switcher">
            <span :class="{ active: isLogin }" @click="showLogin">登录</span>
            <span :class="{ active: !isLogin }" @click="showRegister">注册</span>
          </div>
        </div>
        
        <!-- 登录表单 -->
        <el-form v-if="isLogin" :model="loginForm" label-position="top">
          <el-form-item label="用户名">
            <el-input v-model="loginForm.username" placeholder="请输入用户名" />
          </el-form-item>
          
          <el-form-item label="密码">
            <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" />
          </el-form-item>
          
          <div class="form-options">
            <el-checkbox v-model="loginForm.remember">记住我</el-checkbox>
            <a href="#" class="forget-link" @click.prevent="showForgotPasswordDialog = true">忘记密码?</a>
          </div>
          
          <el-button type="primary" class="submit-button" @click="handleLogin" :loading="isLoading">登录</el-button>
          
          <div class="form-footer">
            <p>没有账号? <a href="#" @click.prevent="showRegister">立即注册</a></p>
          </div>
        </el-form>
        
        <!-- 注册表单 -->
        <el-form v-else 
          ref="registerFormRef"
          :model="registerForm" 
          :rules="registerRules"
          label-position="top">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="registerForm.username" placeholder="请输入用户名(3-20个字符)" />
          </el-form-item>
          
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="registerForm.email" type="email" placeholder="请输入邮箱" />
          </el-form-item>

          <el-form-item label="邮箱验证码" prop="email_verification_code">
            <div style="display: flex; gap: 10px;">
              <el-input
                v-model="registerForm.email_verification_code"
                placeholder="请输入6位验证码"
                maxlength="6"
                style="flex: 1;"
              />
              <el-button
                @click="sendRegisterCode"
                :loading="isCodeSending"
                :disabled="codeCountdown > 0"
                style="width: 120px;"
              >
                {{ codeCountdown > 0 ? `${codeCountdown}s后重发` : '获取验证码' }}
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="密码" prop="password">
            <el-input v-model="registerForm.password" type="password" placeholder="请输入密码(至少6个字符)" />
          </el-form-item>
          
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input v-model="registerForm.confirmPassword" type="password" placeholder="请确认密码" />
          </el-form-item>
          
          <el-button type="primary" class="submit-button" @click="handleRegister" :loading="isLoading">注册</el-button>
          
          <div class="form-footer">
            <p>已有账号? <a href="#" @click.prevent="showLogin">返回登录</a></p>
          </div>
        </el-form>
      </el-card>
      

    </div>
  </div>

  <!-- 忘记密码对话框 -->
  <el-dialog v-model="showForgotPasswordDialog" title="忘记密码" width="400px">
    <el-form :model="forgotPasswordForm" label-position="top">
      <el-form-item label="邮箱地址">
        <el-input v-model="forgotPasswordForm.email" placeholder="请输入注册时的邮箱地址" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showForgotPasswordDialog = false">取消</el-button>
        <el-button type="primary" @click="handleForgotPassword" :loading="isLoading">发送重置邮件</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 重置密码对话框 -->
  <el-dialog v-model="showResetPasswordDialog" title="重置密码" width="400px" :close-on-click-modal="false">
    <el-form :model="resetPasswordForm" label-position="top">
      <el-form-item label="邮箱地址">
        <el-input v-model="resetPasswordForm.email" placeholder="邮箱地址" disabled />
      </el-form-item>
      <el-form-item label="重置码">
        <el-input v-model="resetPasswordForm.reset_code" placeholder="请输入邮件中的6位重置码" maxlength="6" />
      </el-form-item>
      <el-form-item label="新密码">
        <el-input v-model="resetPasswordForm.new_password" type="password" placeholder="请输入新密码" />
      </el-form-item>
      <el-form-item label="确认密码">
        <el-input v-model="resetPasswordForm.confirm_password" type="password" placeholder="请再次输入新密码" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showResetPasswordDialog = false">取消</el-button>
        <el-button type="primary" @click="handleResetPassword" :loading="isLoading">重置密码</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(to right, #f0f5ff, #f5f7fa);
  padding: 20px;
}

.login-wrapper {
  max-width: 400px;
  width: 100%;
}

.logo-section {
  text-align: center;
  margin-bottom: 30px;
  
  .logo {
    font-size: 32px;
    font-weight: 700;
    color: var(--apple-blue);
    margin-bottom: 8px;
  }
  
  .slogan {
    font-size: 16px;
    color: var(--apple-gray);
  }
}

.login-card {
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  
  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    h2 {
      margin: 0;
      font-size: 24px;
      color: var(--apple-dark-gray);
    }
    
    .form-switcher {
      display: flex;
      
      span {
        padding: 8px 12px;
        cursor: pointer;
        color: var(--apple-gray);
        border-radius: 4px;
        
        &.active {
          color: var(--apple-blue);
          font-weight: 600;
        }
        
        &:hover:not(.active) {
          background-color: #f5f5f5;
        }
      }
    }
  }
  
  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .forget-link {
      color: var(--apple-blue);
      text-decoration: none;
      font-size: 14px;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
  
  .submit-button {
    width: 100%;
    height: 44px;
    border-radius: 8px;
    font-size: 16px;
    margin-bottom: 20px;
  }
  
  .form-footer {
    text-align: center;
    font-size: 14px;
    color: var(--apple-gray);
    
    a {
      color: var(--apple-blue);
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}



.bottom-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}
</style> 