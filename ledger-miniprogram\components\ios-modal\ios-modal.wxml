<!-- iOS风格模态框组件 -->
<view wx:if="{{visible}}" class="ios-modal-overlay" bindtap="handleOverlayClick">
  <view class="ios-modal" catchtap="stopPropagation">
    <!-- 模态框头部 -->
    <view class="ios-modal-header">
      <button class="ios-header-btn cancel-btn" bindtap="handleCancel">
        {{cancelText || '取消'}}
      </button>
      <text class="ios-modal-title">{{title}}</text>
      <button 
        class="ios-header-btn confirm-btn" 
        bindtap="handleConfirm"
        disabled="{{confirmDisabled}}"
      >
        {{confirmLoading ? '保存中...' : (confirmText || '确定')}}
      </button>
    </view>

    <!-- 模态框内容 -->
    <view class="ios-modal-content">
      <slot></slot>
    </view>
  </view>
</view>
