from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional
import datetime
import re

# Base schema for User
class UserBase(BaseModel):
    username: str = Field(..., min_length=3, max_length=20, description="用户名，长度3-20个字符")
    email: EmailStr
    phone: Optional[str] = None
    
    @validator('username')
    def username_alphanumeric(cls, v):
        if not re.match(r'^[a-zA-Z0-9_\u4e00-\u9fa5]+$', v):
            raise ValueError('用户名只能包含字母、数字、下划线和中文')
        return v

# Schema for creating a new user
class UserCreate(UserBase):
    password: str = Field(..., min_length=6, description="密码，至少6个字符")
    email_verification_code: str = Field(..., min_length=6, max_length=6, description="邮箱验证码")

# Schema for updating an existing user
class UserUpdate(BaseModel):
    username: Optional[str] = Field(None, min_length=3, max_length=20, description="用户名，长度3-20个字符")
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    password: Optional[str] = Field(None, min_length=6, description="密码，至少6个字符")
    
    @validator('username')
    def username_alphanumeric(cls, v):
        if v is not None and not re.match(r'^[a-zA-Z0-9_\u4e00-\u9fa5]+$', v):
            raise ValueError('用户名只能包含字母、数字、下划线和中文')
        return v

# Schema for user data stored in DB (includes password hash)
class UserInDB(UserBase):
    password_hash: str

# Schema for user data returned from API
class User(BaseModel):
    id: int
    username: str  # 移除长度限制，允许短用户名
    email: EmailStr
    phone: Optional[str] = None
    created_at: datetime.datetime
    updated_at: datetime.datetime
    email_verified: bool = False  # 邮箱验证状态

    class Config:
        orm_mode = True

# Schema for token data
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None 

# Schema for user settings
class UserSettingsBase(BaseModel):
    language: str = "zh"
    currency: str = "CNY"
    dark_mode: bool = False
    notifications: bool = True
    auto_backup: bool = False

class UserSettingsCreate(UserSettingsBase):
    user_id: int

class UserSettingsUpdate(UserSettingsBase):
    pass

# 邮箱验证相关schema
class EmailVerificationRequest(BaseModel):
    email: EmailStr

class RegisterEmailVerificationRequest(BaseModel):
    email: EmailStr

class EmailVerificationConfirm(BaseModel):
    email: EmailStr
    verification_code: str = Field(..., min_length=6, max_length=6, description="6位验证码")

class PasswordResetRequest(BaseModel):
    email: EmailStr

class PasswordResetConfirm(BaseModel):
    email: EmailStr
    reset_code: str = Field(..., min_length=6, max_length=6, description="6位重置码")
    new_password: str = Field(..., min_length=6, description="新密码，至少6个字符")

class UserSettings(UserSettingsBase):
    id: int
    user_id: int
    last_backup: Optional[datetime.datetime] = None
    created_at: datetime.datetime
    updated_at: datetime.datetime
    
    class Config:
        orm_mode = True 