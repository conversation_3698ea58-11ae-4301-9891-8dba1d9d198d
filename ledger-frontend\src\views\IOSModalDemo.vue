<script setup lang="ts">
import { ref } from 'vue'
import IOSTransactionModal from '@/components/IOSTransactionModal.vue'

const showModal = ref(false)
const isEditing = ref(false)

const openAddModal = () => {
  isEditing.value = false
  showModal.value = true
}

const openEditModal = () => {
  isEditing.value = true
  showModal.value = true
}

const handleClose = () => {
  showModal.value = false
}

const handleSave = (data: any) => {
  console.log('保存数据:', data)
  alert('交易保存成功！')
}
</script>

<template>
  <div class="demo-container">
    <!-- 页面头部 -->
    <div class="demo-header">
      <h1 class="demo-title">iOS风格添加交易弹窗</h1>
      <p class="demo-subtitle">现代化移动端设计，简洁优雅</p>
    </div>

    <!-- 演示按钮 -->
    <div class="demo-buttons">
      <button class="demo-btn primary" @click="openAddModal">
        添加交易
      </button>
      <button class="demo-btn secondary" @click="openEditModal">
        编辑交易
      </button>
    </div>

    <!-- 设计特点说明 -->
    <div class="demo-features">
      <h2 class="features-title">设计特点</h2>
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">🎨</div>
          <h3>iOS原生风格</h3>
          <p>采用苹果设计语言，圆角、阴影、毛玻璃效果</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">📱</div>
          <h3>移动端优化</h3>
          <p>专为移动设备设计，触控友好，响应迅速</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">✨</div>
          <h3>现代化交互</h3>
          <p>流畅动画，直观操作，提升用户体验</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">🌙</div>
          <h3>深色模式</h3>
          <p>自动适配系统主题，护眼舒适</p>
        </div>
      </div>
    </div>

    <!-- 字段说明 -->
    <div class="demo-fields">
      <h2 class="fields-title">表单字段</h2>
      <div class="fields-list">
        <div class="field-item">
          <span class="field-name">日期</span>
          <span class="field-desc">支持时间选择，默认当前时间</span>
        </div>
        <div class="field-item">
          <span class="field-name">类型</span>
          <span class="field-desc">支出/收入/转账选择</span>
        </div>
        <div class="field-item">
          <span class="field-name">金额</span>
          <span class="field-desc">数字输入，支持小数</span>
        </div>
        <div class="field-item">
          <span class="field-name">分类</span>
          <span class="field-desc">交易分类下拉选择</span>
        </div>
        <div class="field-item">
          <span class="field-name">账户</span>
          <span class="field-desc">账户选择</span>
        </div>
        <div class="field-item">
          <span class="field-name">描述</span>
          <span class="field-desc">多行文本输入，可选</span>
        </div>
      </div>
    </div>

    <!-- iOS风格弹窗组件 -->
    <IOSTransactionModal
      :visible="showModal"
      :is-editing="isEditing"
      @close="handleClose"
      @save="handleSave"
    />
  </div>
</template>

<style lang="scss" scoped>
.demo-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
  color: white;

  .demo-title {
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 10px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .demo-subtitle {
    font-size: 18px;
    font-weight: 400;
    margin: 0;
    opacity: 0.9;
  }
}

.demo-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 50px;

  .demo-btn {
    padding: 16px 32px;
    border: none;
    border-radius: 12px;
    font-size: 17px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;

    &.primary {
      background: #007aff;
      color: white;
      box-shadow: 0 4px 15px rgba(0, 122, 255, 0.4);

      &:hover {
        background: #0056cc;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 122, 255, 0.5);
      }

      &:active {
        transform: translateY(0);
      }
    }

    &.secondary {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

.demo-features {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);

  .features-title {
    color: white;
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 25px 0;
    text-align: center;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;

    .feature-card {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 16px;
      padding: 25px;
      text-align: center;
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: transform 0.2s ease;

      &:hover {
        transform: translateY(-5px);
      }

      .feature-icon {
        font-size: 40px;
        margin-bottom: 15px;
      }

      h3 {
        color: white;
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 10px 0;
      }

      p {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
        line-height: 1.5;
        margin: 0;
      }
    }
  }
}

.demo-fields {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);

  .fields-title {
    color: white;
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 25px 0;
    text-align: center;
  }

  .fields-list {
    display: grid;
    gap: 15px;

    .field-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      border: 1px solid rgba(255, 255, 255, 0.2);

      .field-name {
        color: white;
        font-weight: 600;
        font-size: 16px;
        min-width: 80px;
      }

      .field-desc {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
        text-align: right;
        flex: 1;
        margin-left: 20px;
      }
    }
  }
}

@media (max-width: 768px) {
  .demo-container {
    padding: 15px;
  }

  .demo-header .demo-title {
    font-size: 28px;
  }

  .demo-buttons {
    flex-direction: column;
    align-items: center;

    .demo-btn {
      width: 100%;
      max-width: 300px;
    }
  }

  .demo-features .features-grid {
    grid-template-columns: 1fr;
  }

  .demo-fields .fields-list .field-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .field-desc {
      text-align: left;
      margin-left: 0;
    }
  }
}
</style>
