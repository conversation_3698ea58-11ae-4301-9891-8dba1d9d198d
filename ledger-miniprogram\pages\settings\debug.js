// pages/settings/debug.js
const api = require('../../utils/api.js')

Page({
  data: {
    debugInfo: {
      hasToken: false,
      hasUserInfo: false,
      username: '',
      email: '',
      rawUserInfo: ''
    }
  },

  onLoad() {
    console.log('调试页面加载')
    this.refreshDebugInfo()
  },

  // 刷新调试信息
  refreshDebugInfo() {
    try {
      const token = wx.getStorageSync('token')
      const userInfo = wx.getStorageSync('userInfo')
      
      let parsedUserInfo = null
      if (userInfo) {
        try {
          parsedUserInfo = JSON.parse(userInfo)
        } catch (error) {
          console.error('解析用户信息失败:', error)
        }
      }
      
      this.setData({
        debugInfo: {
          hasToken: !!token,
          hasUserInfo: !!userInfo,
          username: parsedUserInfo ? parsedUserInfo.username : '',
          email: parsedUserInfo ? parsedUserInfo.email : '',
          rawUserInfo: userInfo || '无数据'
        }
      })
      
      console.log('调试信息已刷新:', this.data.debugInfo)
    } catch (error) {
      console.error('刷新调试信息失败:', error)
    }
  },

  // 从服务器获取用户信息
  async testUserInfoFromServer() {
    wx.showLoading({
      title: '获取中...'
    })
    
    try {
      const response = await api.auth.getUserInfo()
      console.log('从服务器获取的用户信息:', response)
      
      // 更新本地存储
      wx.setStorageSync('userInfo', JSON.stringify(response))
      
      wx.hideLoading()
      wx.showToast({
        title: '获取成功',
        icon: 'success'
      })
      
      // 刷新调试信息
      this.refreshDebugInfo()
      
    } catch (error) {
      wx.hideLoading()
      console.error('从服务器获取用户信息失败:', error)
      wx.showToast({
        title: '获取失败: ' + error.message,
        icon: 'none'
      })
    }
  },

  // 清除用户信息
  clearUserInfo() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除用户信息吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('userInfo')
          this.refreshDebugInfo()
          wx.showToast({
            title: '已清除',
            icon: 'success'
          })
        }
      }
    })
  }
})
