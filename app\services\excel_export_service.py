import pandas as pd
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload
import logging
from typing import Dict, Any, List
import datetime

from app.modules.transactions.models import Transaction

logger = logging.getLogger(__name__)

class ExcelExportService:
    async def _get_formatted_transactions(self, db: AsyncSession, user_id: int) -> List[Dict[str, Any]]:
        """
        Fetches and formats transaction data into a user-friendly format.
        """
        # Eagerly load related account and category data to avoid multiple queries
        result = await db.execute(
            select(Transaction)
            .where(Transaction.user_id == user_id)
            .options(
                joinedload(Transaction.account),
                joinedload(Transaction.category),
                joinedload(Transaction.from_account),
                joinedload(Transaction.to_account)
            )
            .order_by(Transaction.transaction_date.desc())
        )
        transactions = result.scalars().all()

        formatted_data = []
        for t in transactions:
            row = {
                "时间": t.transaction_date.strftime('%Y/%m/%d %H:%M') if t.transaction_date else None,
                "分类": "",
                "二级分类": "",  # Placeholder as per request
                "类型": "",
                "金额": f"¥{t.amount:,.2f}" if t.amount is not None else "¥0.00",
                "账户1": "",
                "账户2": "",
                "备注": t.description
            }

            if t.transaction_type == 'expense':
                row["类型"] = "支出"
                row["分类"] = t.category.name if t.category else "支出"
                row["账户1"] = t.account.account_name if t.account else ""
            elif t.transaction_type == 'income':
                row["类型"] = "收入"
                row["分类"] = t.category.name if t.category else "收入"
                row["账户1"] = t.account.account_name if t.account else ""
            elif t.transaction_type == 'transfer':
                # Represent a single transfer as two user-friendly rows
                # Row 1: Expense from the source account
                expense_row = row.copy()
                expense_row["类型"] = "支出"
                expense_row["分类"] = "转账"
                expense_row["账户1"] = t.from_account.account_name if t.from_account else ""
                expense_row["账户2"] = t.to_account.account_name if t.to_account else ""
                formatted_data.append(expense_row)
                
                # Row 2: Income to the destination account
                income_row = row.copy()
                income_row["类型"] = "收入"
                income_row["分类"] = "转账"
                income_row["账户1"] = t.to_account.account_name if t.to_account else ""
                income_row["账户2"] = t.from_account.account_name if t.from_account else ""
                formatted_data.append(income_row)
                continue # Skip the default append
            
            formatted_data.append(row)
            
        return formatted_data

    async def export_user_data_to_excel(self, db: AsyncSession, user_id: int) -> bytes:
        try:
            logger.info(f"Starting Excel export for user_id: {user_id}")
            
            # Fetch formatted transactions for the main sheet
            formatted_transactions = await self._get_formatted_transactions(db, user_id)
            
            from io import BytesIO
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                # Write the user-friendly formatted data to the main sheet
                pd.DataFrame(formatted_transactions).to_excel(writer, sheet_name='交易流水', index=False)
            
            logger.info(f"Excel export successful for user_id: {user_id}")
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"Error exporting user data to Excel for user_id {user_id}: {e}", exc_info=True)
            raise 