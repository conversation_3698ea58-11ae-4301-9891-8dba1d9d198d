  /* 关键修复 - 处理最顶层白色区域问题 */

/* 确保整个页面有正确背景色 */
html.dark-mode,
body.dark-mode,
.dark-mode #app,
.dark-mode .app-layout,
.dark-mode .main-container,
.dark-mode .app-container {
  background-color: #1e1e2e !important;
  color: #e2e2e6 !important;
}

/* 确保卡片有正确背景色 */
.dark-mode .el-card,
.dark-mode .el-card__body,
.dark-mode .settings-section {
  background-color: #282838 !important;
  color: #e2e2e6 !important;
  border-color: #363646 !important;
}

/* 处理在白色区域中显示的文本 */
.dark-mode h1,
.dark-mode h2,
.dark-mode h3,
.dark-mode p,
.dark-mode span,
.dark-mode div,
.dark-mode label {
  color: #e2e2e6 !important;
}

/* 强制覆盖任何可能的白色背景元素 */
.dark-mode .el-card > div,
.dark-mode .el-form > div,
.dark-mode .el-form-item > div,
.dark-mode .el-card > *,
.dark-mode .settings-container > * {
  background-color: inherit !important;
}

/* 强制深色背景直达根元素 */
html.dark-mode:before,
body.dark-mode:before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #1e1e2e !important;
  z-index: -1;
}

/* 图表区域深色模式修复 */
.dark-mode .chart-container {
  background-color: #282838 !important;
}

/* 顶部区域修复 */
.dark-mode .header,
.dark-mode .el-header,
.dark-mode .header-container,
.dark-mode .nav-header {
  background-color: #282838 !important;
  color: #e2e2e6 !important;
  border-color: #363646 !important;
}

/* 确保顶部白色区域被覆盖 */
.dark-mode header,
.dark-mode .el-menu--horizontal,
.dark-mode .top-bar {
  background-color: #282838 !important;
  color: #e2e2e6 !important;
}

/* 修复顶部账户头像区域 */
.dark-mode .avatar-container,
.dark-mode .user-info,
.dark-mode .el-dropdown,
.dark-mode .el-avatar {
  background-color: transparent !important;
}

/* 特别处理图表容器内的DOM元素 */
.dark-mode .chart-container canvas,
.dark-mode .chart-container div,
.dark-mode .echarts-instance,
.dark-mode div[_echarts_instance_] {
  background-color: #282838 !important;
}

/* 修复按钮文本在深色模式下的可见性 */
.dark-mode .el-button--primary.is-plain,
.dark-mode .el-button--success.is-plain,
.dark-mode .el-button--warning.is-plain,
.dark-mode .el-button--danger.is-plain,
.dark-mode .el-button--info.is-plain {
  color: #e2e2e6 !important;
  background-color: rgba(64, 158, 255, 0.1) !important;
  border-color: #409eff !important;
}

/* 系统级别深色模式增强 - 表格和数据展示 */
.dark-mode .el-table {
  background-color: #282838 !important;
  color: #e2e2e6 !important;
}

.dark-mode .el-table th,
.dark-mode .el-table tr,
.dark-mode .el-table td,
.dark-mode .el-table .cell {
  background-color: #282838 !important;
  color: #e2e2e6 !important;
  border-color: #363646 !important;
}

.dark-mode .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #2b2b3b !important;
}

.dark-mode .el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: #313142 !important;
}

/* 交易记录页面样式增强 */
.dark-mode .transactions-view .filter-bar {
  background-color: #282838 !important;
  border-color: #363646 !important;
}

.dark-mode .el-date-editor {
  background-color: #313142 !important;
}

.dark-mode .el-input__inner,
.dark-mode .el-textarea__inner {
  background-color: #313142 !important;
  color: #e2e2e6 !important;
  border-color: #363646 !important;
}

.dark-mode .el-input-group__append,
.dark-mode .el-input-group__prepend {
  background-color: #313142 !important;
  color: #e2e2e6 !important;
  border-color: #363646 !important;
}

/* 日期选择器增强 */
.dark-mode .el-picker-panel {
  background-color: #282838 !important;
  color: #e2e2e6 !important;
  border-color: #363646 !important;
}

.dark-mode .el-date-table td,
.dark-mode .el-date-table th {
  color: #e2e2e6 !important;
}

.dark-mode .el-date-table td.current:not(.disabled) span {
  background-color: #409eff !important;
  color: #ffffff !important;
}

/* 财务分析页面增强 */
.dark-mode .analytics-view .chart-controls {
  background-color: #282838 !important;
  color: #e2e2e6 !important;
  border-color: #363646 !important;
}

.dark-mode .el-radio-button__inner {
  background-color: #282838 !important;
  color: #e2e2e6 !important;
  border-color: #363646 !important;
}

.dark-mode .el-radio-button__original-radio:checked + .el-radio-button__inner {
  background-color: #409eff !important;
  color: #ffffff !important;
}

/* 表格样式全局增强 */
.dark-mode .el-table th.el-table__cell.is-leaf {
  background-color: #23232e !important;
}

.dark-mode .el-table--border:after, 
.dark-mode .el-table--group:after,
.dark-mode .el-table:before {
  background-color: #363646 !important;
}

.dark-mode .el-table--border,
.dark-mode .el-table--group {
  border-color: #363646 !important;
}

.dark-mode .el-table--border .el-table__cell {
  border-right: 1px solid #363646 !important;
}

.dark-mode .el-table__body tr.hover-row > td.el-table__cell {
  background-color: #313142 !important;
}

/* 特殊组件修复 */
.dark-mode .el-empty__description {
  color: #a0a0b0 !important;
}

.dark-mode .el-loading-mask {
  background-color: rgba(40, 40, 56, 0.8) !important;
}

.dark-mode .page-title {
  color: #e2e2e6 !important;
}

.dark-mode .dialog-footer button {
  border-color: #363646 !important;
  color: #e2e2e6 !important;
}

.dark-mode .dialog-footer .el-button--primary {
  background-color: #409eff !important;
  color: #ffffff !important;
}

/* 修复表格内按钮显示 */
.dark-mode .el-table .el-button.el-button--text {
  color: #409eff !important; 
}

.dark-mode .el-table .cell .el-button.el-button--danger.is-text {
  color: #f56c6c !important;
}

/* 修复日期范围选择器 */
.dark-mode .el-range-editor.el-input__inner {
  background-color: #313142 !important;
}

.dark-mode .el-range-editor .el-range-input {
  color: #e2e2e6 !important;
  background-color: transparent !important;
}

.dark-mode .el-range-editor .el-range-separator {
  color: #909399 !important;
}

/* 修复下拉框箭头 */
.dark-mode .el-select .el-input .el-select__caret {
  color: #909399 !important;
}

/* 修复Dialog对话框 */
.dark-mode .el-dialog,
.dark-mode .el-dialog__header,
.dark-mode .el-dialog__body,
.dark-mode .el-dialog__footer {
  background-color: #282838 !important;
  color: #e2e2e6 !important;
}

.dark-mode .el-dialog__footer {
  border-top-color: #404040 !important;
  background-color: #2a2a3a !important;
}

.dark-mode .el-dialog__title {
  color: #e2e2e6 !important;
}

.dark-mode .el-dialog__headerbtn .el-dialog__close {
  color: #909399 !important;
}

/* 确保ECharts实例在深色模式下显示正确 */
.dark-mode [_echarts_instance_] {
  background-color: #282838 !important;
}

.dark-mode .chart-empty-text {
  color: #a0a0b0 !important;
}

/* 修复表单项标签 */
.dark-mode .el-form-item__label {
  color: #e2e2e6 !important;
} 