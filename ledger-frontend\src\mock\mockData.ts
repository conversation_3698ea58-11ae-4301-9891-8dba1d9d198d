// 用户相关模拟数据
export const mockUserData = {
  userInfo: {
    id: 1,
    username: '张三',
    email: '<PERSON><PERSON><PERSON>@example.com',
    phone: '***********',
    avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    createdAt: '2025-01-01T00:00:00.000Z',
    updatedAt: '2025-01-01T00:00:00.000Z'
  }
}

// 账户相关模拟数据
export const mockAccountData = {
  accounts: [
    {
      id: 1,
      name: '工商银行储蓄卡',
      balance: 15420.50,
      type: 'bank',
      icon: '🏦',
      userId: 1,
      createdAt: '2025-01-01T00:00:00.000Z',
      updatedAt: '2025-01-01T00:00:00.000Z'
    },
    {
      id: 2,
      name: '微信零钱',
      balance: 523.65,
      type: 'digital',
      icon: '💰',
      userId: 1,
      createdAt: '2025-01-01T00:00:00.000Z',
      updatedAt: '2025-01-01T00:00:00.000Z'
    },
    {
      id: 3,
      name: '支付宝',
      balance: 2341.28,
      type: 'digital',
      icon: '💰',
      userId: 1,
      createdAt: '2025-01-01T00:00:00.000Z',
      updatedAt: '2025-01-01T00:00:00.000Z'
    },
    {
      id: 4,
      name: '招商银行信用卡',
      balance: -4356.40,
      type: 'credit',
      icon: '💳',
      userId: 1,
      createdAt: '2025-01-01T00:00:00.000Z',
      updatedAt: '2025-01-01T00:00:00.000Z'
    },
    {
      id: 5,
      name: '股票账户',
      balance: 35210.00,
      type: 'investment',
      icon: '📈',
      userId: 1,
      createdAt: '2025-01-01T00:00:00.000Z',
      updatedAt: '2025-01-01T00:00:00.000Z'
    }
  ]
}

// 交易相关模拟数据
export const mockTransactionData = {
  transactions: [
    {
      id: 1,
      date: '2025-07-15',
      type: 'expense',
      amount: 125.5,
      category: 'food',
      account_id: 2,
      description: '午餐和晚餐',
      userId: 1,
      createdAt: '2025-07-15T12:30:00.000Z',
      updatedAt: '2025-07-15T12:30:00.000Z'
    },
    {
      id: 2,
      date: '2025-07-14',
      type: 'expense',
      amount: 35.5,
      category: 'transport',
      account_id: 3,
      description: '打车费用',
      userId: 1,
      createdAt: '2025-07-14T18:20:00.000Z',
      updatedAt: '2025-07-14T18:20:00.000Z'
    },
    {
      id: 3,
      date: '2025-07-10',
      type: 'income',
      amount: 10000,
      category: 'salary',
      account_id: 1,
      description: '七月工资',
      userId: 1,
      createdAt: '2025-07-10T09:00:00.000Z',
      updatedAt: '2025-07-10T09:00:00.000Z'
    },
    {
      id: 4,
      date: '2025-07-05',
      type: 'transfer',
      amount: 2000,
      from_account_id: 1,
      to_account_id: 2,
      description: '转账到微信',
      userId: 1,
      createdAt: '2025-07-05T10:15:00.000Z',
      updatedAt: '2025-07-05T10:15:00.000Z'
    },
    {
      id: 5,
      date: '2025-07-01',
      type: 'expense',
      amount: 1500,
      category: 'housing',
      account_id: 1,
      description: '房租',
      userId: 1,
      createdAt: '2025-07-01T09:30:00.000Z',
      updatedAt: '2025-07-01T09:30:00.000Z'
    }
  ]
}

// 预算相关模拟数据
export const mockBudgetData = {
  budgets: [
    {
      id: 1,
      category: 'food',
      amount: 2000,
      used: 1350,
      month: '2025-07',
      userId: 1,
      createdAt: '2025-07-01T00:00:00.000Z',
      updatedAt: '2025-07-20T00:00:00.000Z'
    },
    {
      id: 2,
      category: 'transport',
      amount: 800,
      used: 620,
      month: '2025-07',
      userId: 1,
      createdAt: '2025-07-01T00:00:00.000Z',
      updatedAt: '2025-07-20T00:00:00.000Z'
    },
    {
      id: 3,
      category: 'shopping',
      amount: 1500,
      used: 1800,
      month: '2025-07',
      userId: 1,
      createdAt: '2025-07-01T00:00:00.000Z',
      updatedAt: '2025-07-20T00:00:00.000Z'
    },
    {
      id: 4,
      category: 'entertainment',
      amount: 500,
      used: 300,
      month: '2025-07',
      userId: 1,
      createdAt: '2025-07-01T00:00:00.000Z',
      updatedAt: '2025-07-20T00:00:00.000Z'
    },
    {
      id: 5,
      category: 'housing',
      amount: 3000,
      used: 3000,
      month: '2025-07',
      userId: 1,
      createdAt: '2025-07-01T00:00:00.000Z',
      updatedAt: '2025-07-20T00:00:00.000Z'
    },
    {
      id: 6,
      category: 'utilities',
      amount: 600,
      used: 580,
      month: '2025-07',
      userId: 1,
      createdAt: '2025-07-01T00:00:00.000Z',
      updatedAt: '2025-07-20T00:00:00.000Z'
    }
  ]
}

// 分析相关模拟数据
export const mockAnalyticsData = {
  incomeExpenseSummary: [
    { month: '一月', income: 4500, expense: 3500 },
    { month: '二月', income: 5000, expense: 4200 },
    { month: '三月', income: 4800, expense: 3800 },
    { month: '四月', income: 5200, expense: 4000 },
    { month: '五月', income: 5800, expense: 4500 },
    { month: '六月', income: 6000, expense: 5000 },
    { month: '七月', income: 5500, expense: 4200 }
  ],
  expenseCategorySummary: [
    { category: '餐饮', amount: 1350, percentage: 35 },
    { category: '购物', amount: 1000, percentage: 25 },
    { category: '交通', amount: 800, percentage: 20 },
    { category: '其他', amount: 800, percentage: 20 }
  ],
  incomeCategorySummary: [
    { category: '工资', amount: 5000, percentage: 90 },
    { category: '其他', amount: 500, percentage: 10 }
  ],
  accountSummary: [
    { account_id: 1, account_name: '工商银行储蓄卡', balance: 15420.50 },
    { account_id: 2, account_name: '微信零钱', balance: 523.65 },
    { account_id: 3, account_name: '支付宝', balance: 2341.28 },
    { account_id: 4, account_name: '招商银行信用卡', balance: -4356.40 },
    { account_id: 5, account_name: '股票账户', balance: 35210.00 }
  ],
  assetTrend: [
    { date: '2025-01-01', assets: 60000 },
    { date: '2025-02-01', assets: 62000 },
    { date: '2025-03-01', assets: 64000 },
    { date: '2025-04-01', assets: 67000 },
    { date: '2025-05-01', assets: 70000 },
    { date: '2025-06-01', assets: 72000 },
    { date: '2025-07-01', assets: 76000 }
  ],
  totalIncome: 5500,
  totalExpense: 4200,
  totalBalance: 1300,
  totalAssets: 76890
} 