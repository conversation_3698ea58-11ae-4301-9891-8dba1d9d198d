<!-- 首页 -->
<view class="index-page">
  <!-- 余额卡片 -->
  <view class="balance-card">
    <view class="balance-header">
      <text class="greeting">你好，{{userInfo.username || '用户'}}</text>
      <text class="date">{{currentDate}}</text>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 余额显示 -->
    <view wx:else class="balance-content" bindtap="viewAccountDetails">
      <view class="balance-amount">
        <text class="currency">{{hideAmounts ? '' : '¥'}}</text>
        <text class="amount">{{hideAmounts ? '****' : totalBalance}}</text>
      </view>
      <view class="balance-label">
        <view class="balance-label-row">
          <text class="balance-text">净资产</text>
          <text class="eye-toggle" catchtap="toggleAmountVisibility">{{hideAmounts ? '👁️' : '🙈'}}</text>
        </view>
        <text class="tap-hint">点击查看详情</text>
        <text class="refresh-btn" catchtap="forceRefresh">🔄</text>
      </view>

      <view class="balance-stats">
        <view class="stat-item" bindtap="viewMonthlyIncome">
          <text class="stat-value income">{{hideAmounts ? '****' : '+¥' + monthlyIncome}}</text>
          <text class="stat-label">本月收入</text>
        </view>
        <view class="stat-item" bindtap="viewMonthlyExpense">
          <text class="stat-value expense">{{hideAmounts ? '****' : '-¥' + monthlyExpense}}</text>
          <text class="stat-label">本月支出</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 快速操作 -->
  <view class="quick-actions">
    <view class="section-title">快速操作</view>
    <view class="actions-grid">
      <view class="action-item" bindtap="addIncome">
        <view class="action-icon income-icon">
          <text class="icon-text">💰</text>
        </view>
        <text class="action-title">收入</text>
      </view>
      <view class="action-item" bindtap="addExpense">
        <view class="action-icon expense-icon">
          <text class="icon-text">💸</text>
        </view>
        <text class="action-title">支出</text>
      </view>
      <view class="action-item" bindtap="addTransfer">
        <view class="action-icon transfer-icon">
          <text class="icon-text">🔄</text>
        </view>
        <text class="action-title">转账</text>
      </view>
      <view class="action-item" bindtap="viewAnalytics">
        <view class="action-icon analytics-icon">
          <text class="icon-text">📊</text>
        </view>
        <text class="action-title">统计</text>
      </view>
    </view>
  </view>
  
  <!-- 最近交易 -->
  <view class="recent-transactions">
    <view class="section-header">
      <text class="section-title">最近交易</text>
      <text class="more-btn" bindtap="viewMoreTransactions">查看更多</text>
    </view>

    <!-- 交易列表 -->
    <view wx:if="{{!loading && recentTransactions.length > 0}}" class="transactions-list">
      <view class="transaction-item" wx:for="{{recentTransactions}}" wx:key="id">
        <view class="transaction-icon">
          <text class="icon-text">{{item.icon}}</text>
        </view>
        <view class="transaction-info">
          <text class="transaction-desc">{{item.description}}</text>
          <text class="transaction-category">{{item.category}}</text>
        </view>
        <view class="transaction-right">
          <text class="transaction-amount {{item.type}}">{{hideAmounts ? '****' : item.amountText}}</text>
          <text class="transaction-date">{{item.date}}</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:elif="{{!loading && recentTransactions.length === 0}}" class="empty-state">
      <text class="empty-icon">📝</text>
      <text class="empty-text">暂无交易记录</text>
      <text class="empty-hint">点击下方快速操作开始记账</text>
    </view>

    <!-- 加载状态 -->
    <view wx:elif="{{loading}}" class="loading-transactions">
      <view class="loading-item" wx:for="{{[1,2,3]}}" wx:key="*this">
        <view class="loading-icon"></view>
        <view class="loading-content">
          <view class="loading-line short"></view>
          <view class="loading-line long"></view>
        </view>
      </view>
    </view>
  </view>
</view>
