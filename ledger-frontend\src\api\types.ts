// 基础响应接口
export interface BaseResponse<T> {
  code: number;
  message: string;
  data: T;
}

// 分页请求参数
export interface PaginationQuery {
  page: number;
  pageSize: number;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
}

// 分页响应数据
export interface PaginationResponse<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}

// 用户相关接口
export interface LoginParams {
  username: string;
  password: string;
}

// OAuth2 Token响应
export interface TokenResponse {
  access_token: string;
  token_type: string;
}

export interface RegisterParams {
  username: string;
  email: string;
  password: string;
}

export interface UserInfo {
  id: number;
  username: string;
  email: string;
  phone?: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

// 账户相关接口
export interface Account {
  id: number;
  account_name: string;
  account_type: string;
  institution: string;
  account_number: string;
  currency: string;
  initial_balance: number;
  current_balance: number;
  user_id: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface AccountCreateParams {
  account_name: string;
  account_type: string;
  initial_balance: number;
}

export interface AccountUpdateParams extends Partial<AccountCreateParams> {
  id: number;
}

// 交易类型定义
export interface Transaction {
  id: number;
  date?: string;
  transaction_date?: string;
  type?: string;
  transaction_type?: string;
  amount: number;
  category?: string;
  category_id?: number | null;
  account_id?: number;
  description?: string;
  from_account_id?: number | null;
  to_account_id?: number | null;
  user_id?: number;
  created_at?: string;
  updated_at?: string;
}

// 创建交易参数
export interface TransactionCreateParams {
  date?: string;
  type: string;
  transaction_type?: string;  // 添加明确的transaction_type字段
  amount: number;
  category?: string;
  account_id?: number;
  from_account_id?: number;
  to_account_id?: number;
  description?: string;
}

export interface TransactionUpdateParams {
  id: number;
  date?: string;
  type?: 'income' | 'expense' | 'transfer';
  amount?: number;
  category?: string;
  account_id?: number;
  from_account_id?: number;
  to_account_id?: number;
  description?: string;
}

export interface TransactionQuery extends PaginationQuery {
  dateRange?: [string, string];
  type?: string;
  category?: string;
  account_id?: number;
  keyword?: string;
}

// 预算相关接口
export interface BudgetWithCategory {
  id: number;
  category_id: number;
  category_name: string;
  category_color: string;
  amount: number;
  spent: number;
  year: number;
  month: number;
  user_id: number;
}

export interface Budget {
  id: number;
  category_id: number;
  user_id: number;
  amount: number;
  spent: number;
  year: number;
  month: number;
}

export interface BudgetSummary {
  total_budget: number;
  total_spent: number;
  remaining: number;
  usage_percentage: number;
}

export interface MonthlyBudgetResponse {
  summary: BudgetSummary;
  budgets: BudgetWithCategory[];
}

export interface BudgetCreateParams {
  category_id: number;
  amount: number;
  year: number;
  month: number;
}

export interface BudgetUpdateParams {
  id: number;
  amount: number;
}

// 财务分析相关接口
export interface IncomeExpenseSummary {
  month: string;
  income: number;
  expense: number;
}

export interface CategorySummary {
  category: string;
  amount: number;
  percentage: number;
}

export interface AccountSummary {
  account_id: number;
  account_name: string;
  balance: number;
}

export interface AssetTrend {
  date: string;
  assets: number;
}

export interface AnalyticsData {
  incomeExpenseSummary: IncomeExpenseSummary[];
  expenseCategorySummary: CategorySummary[];
  incomeCategorySummary: CategorySummary[];
  accountSummary: AccountSummary[];
  assetTrend: AssetTrend[];
  totalIncome: number;
  totalExpense: number;
  totalBalance: number;
  totalAssets: number;
} 

// 新增财务分析相关类型

// 月度收入支出数据
export interface MonthlyIncome {
  month: number;
  income: number;
  expense: number;
}

// 支出分布数据
export interface ExpenseDistributionItem {
  category_name: string;
  amount: number;
  percentage: number;
}

export interface ExpenseDistribution {
  total_expense: number;
  expense_distribution: ExpenseDistributionItem[];
}

// 净资产趋势数据
export interface NetWorthTrend {
  date: string;
  net_worth: number;
}

// 账户余额数据
export interface AccountBalance {
  id: number;
  name: string;
  type: string;
  institution: string;
  balance: number;
  currency: string;
}

// 月度财务汇总数据
export interface MonthlySummary {
  monthly_income: number;
  income_growth: number;
  monthly_expense: number;
  expense_growth: number;
  monthly_balance: number;
  balance_growth: number;
  net_worth: number;
  net_worth_growth: number;
} 

// 用户设置接口
export interface UserSettings {
  id: number;
  user_id: number;
  language: string;
  currency: string;
  dark_mode: boolean;
  notifications: boolean;
  auto_backup: boolean;
  last_backup?: string;
  created_at: string;
  updated_at: string;
} 