from typing import Generic, List, Optional, Type, TypeVar
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.base_class import Base

ModelType = TypeVar("ModelType", bound=Base)

class BaseService(Generic[ModelType]):
    def __init__(self, model: Type[ModelType]):
        self.model = model

    async def get_multi(
        self, db: AsyncSession, *, skip: int = 0, limit: Optional[int] = None
    ) -> List[ModelType]:
        query = select(self.model).order_by(self.model.id).offset(skip)
        if limit is not None:
            query = query.limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all() 