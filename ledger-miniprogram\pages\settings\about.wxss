/* pages/settings/about.wxss */
.about-page {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding: 32rpx;
}

.app-info-section {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  text-align: center;
}

.app-icon {
  width: 160rpx;
  height: 160rpx;
  border-radius: 32rpx;
  background: linear-gradient(135deg, #007AFF, #AF52DE);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 32rpx;
}

.icon-text {
  font-size: 80rpx;
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #000000;
  margin-bottom: 16rpx;
}

.app-version {
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 24rpx;
}

.app-description {
  font-size: 30rpx;
  color: #666666;
  line-height: 1.5;
}

.details-section,
.features-section,
.contact-section,
.legal-section {
  margin-bottom: 40rpx;
}

.section-header {
  padding: 0 32rpx 16rpx;
  font-size: 26rpx;
  color: #8E8E93;
  text-transform: uppercase;
  letter-spacing: 1rpx;
}

.section-content {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
}

.detail-item,
.contact-item,
.legal-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.detail-item:last-child,
.contact-item:last-child,
.legal-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 32rpx;
  color: #000000;
  width: 200rpx;
}

.detail-value {
  flex: 1;
  font-size: 32rpx;
  color: #8E8E93;
  text-align: right;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-icon {
  font-size: 40rpx;
  margin-right: 32rpx;
  margin-top: 8rpx;
}

.feature-info {
  flex: 1;
}

.feature-title {
  display: block;
  font-size: 34rpx;
  color: #000000;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 28rpx;
  color: #8E8E93;
  line-height: 1.4;
}

.contact-icon {
  font-size: 40rpx;
  margin-right: 32rpx;
}

.contact-info {
  flex: 1;
}

.contact-title {
  display: block;
  font-size: 34rpx;
  color: #000000;
  margin-bottom: 8rpx;
}

.contact-value {
  font-size: 28rpx;
  color: #8E8E93;
}

.contact-action {
  font-size: 28rpx;
  color: #007AFF;
}

.legal-title {
  flex: 1;
  font-size: 34rpx;
  color: #000000;
}

.legal-arrow {
  font-size: 32rpx;
  color: #C7C7CC;
  font-weight: 500;
}

.actions-section {
  margin-bottom: 40rpx;
}

.action-btn {
  background-color: #FFFFFF;
  color: #007AFF;
  border-radius: 20rpx;
  padding: 24rpx 32rpx;
  font-size: 34rpx;
  border: none;
  width: 100%;
  margin-bottom: 20rpx;
}

.action-btn:last-child {
  margin-bottom: 0;
}

.action-btn:active {
  background-color: #F0F8FF;
}

.copyright-section {
  text-align: center;
  padding: 40rpx 20rpx;
}

.copyright-text {
  display: block;
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.copyright-text:last-child {
  margin-bottom: 0;
}
