# 个人金融账本系统后端产品文档

## 1. 产品概述

个人金融账本系统是一个基于Web的应用程序，旨在帮助用户管理和跟踪其各类金融资产与负债。本后端系统提供稳定、高效的RESTful API服务，支持前端Web应用和移动端应用。

## 2. 开发策略

系统采用**后端先行**的开发策略：

1. **设计与实现**：完成模块化后端架构、数据库设计和API接口开发
2. **测试与优化**：进行全面的接口测试和性能优化
3. **交付**：提供稳定可靠的API服务供前端调用

## 3. 功能需求（API视角）

- **账户管理**：提供账户的增删改查API
- **交易记录**：提供交易记录的增删改查和筛选API
- **资产负债表**：提供实时计算资产负债数据的API
- **财务分析**：提供资产配置、收益率等分析数据的API
- **预算管理**：提供预算的增删改查和跟踪API
- **用户管理**：提供用户认证、资料管理API

## 4. 后端技术架构

### 4.1 整体架构
采用模块化设计的后端架构：
- Web框架：Python FastAPI RESTful API
- 数据库：MySQL 5.7.36（远程服务器）
- 缓存系统：Redis
- 认证：JWT (JSON Web Token)
- API文档：Swagger/OpenAPI

系统划分为以下功能模块：
- 用户模块
- 账户模块
- 交易模块
- 分析模块
- 预算模块

### 4.2 后端技术栈
- 编程语言：Python 3.8+
- Web框架：FastAPI
- ORM层：SQLAlchemy
- API文档：Swagger/OpenAPI
- 数据库：MySQL 5.7.36
- 缓存系统：Redis
- 模块化设计：基于领域驱动设计(DDD)

## 5. 数据库设计

### 5.1 模块化数据库设计原则
- 软删除机制
- 适当的外键约束
- 模块内聚性
- 适当的索引设计
- 模块间通信
- 审计追踪

### 5.2 主要数据表

#### 用户表 (users)
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    phone VARCHAR(20),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login DATETIME,
    device_info VARCHAR(255),
    deleted BOOLEAN DEFAULT FALSE
);
```

#### 账户表 (accounts)
```sql
CREATE TABLE accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    account_name VARCHAR(100) NOT NULL,
    account_type ENUM('bank', 'fund', 'stock', 'debt') NOT NULL,
    institution VARCHAR(100),
    account_number VARCHAR(50),
    currency VARCHAR(10) DEFAULT 'CNY',
    initial_balance DECIMAL(15,2) DEFAULT 0,
    current_balance DECIMAL(15,2) DEFAULT 0,
    interest_rate DECIMAL(8,4),
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE,
    INDEX idx_user_id (user_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT
);
```

#### 银行账户详情表 (bank_accounts)
```sql
CREATE TABLE bank_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '银行账户详情ID',
    account_id INT NOT NULL COMMENT '关联的账户ID',
    account_subtype ENUM('checking', 'savings', 'time_deposit') NOT NULL COMMENT '账户子类型：活期、储蓄、定期',
    maturity_date DATE COMMENT '到期日期（针对定期存款）',
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE
);
```

#### 投资账户详情表 (investment_accounts)
```sql
CREATE TABLE investment_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '投资账户详情ID',
    account_id INT NOT NULL COMMENT '关联的账户ID',
    account_subtype ENUM('fund', 'stock', 'bond', 'other') NOT NULL COMMENT '投资子类型：基金、股票、债券、其他',
    risk_level INT COMMENT '风险等级（1-5，数字越大风险越高）',
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE
);
```

#### 负债账户详情表 (debt_accounts)
```sql
CREATE TABLE debt_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '负债账户详情ID',
    account_id INT NOT NULL COMMENT '关联的账户ID',
    debt_type ENUM('credit_card', 'loan', 'mortgage', 'other') NOT NULL COMMENT '负债类型：信用卡、贷款、抵押贷款、其他',
    due_date DATE COMMENT '还款日期',
    minimum_payment DECIMAL(15,2) COMMENT '最低还款金额',
    total_debt DECIMAL(15,2) COMMENT '总负债金额',
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE
);
```

#### 交易记录表 (transactions)
```sql
CREATE TABLE transactions (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '交易ID',
    account_id INT NOT NULL COMMENT '关联的账户ID',
    user_id INT NOT NULL COMMENT '关联的用户ID',
    transaction_date DATETIME NOT NULL COMMENT '交易日期时间',
    amount DECIMAL(15,2) NOT NULL COMMENT '交易金额',
    transaction_type ENUM('income', 'expense', 'transfer', 'investment', 'interest', 'dividend', 'fee') NOT NULL COMMENT '交易类型：收入、支出、转账、投资、利息、分红、手续费',
    category VARCHAR(50) COMMENT '交易分类',
    description TEXT COMMENT '交易描述',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### 预算表 (budgets)
```sql
CREATE TABLE budgets (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '预算ID',
    user_id INT NOT NULL COMMENT '关联的用户ID',
    category VARCHAR(50) NOT NULL COMMENT '预算分类',
    amount DECIMAL(15,2) NOT NULL COMMENT '预算金额',
    start_date DATE NOT NULL COMMENT '预算开始日期',
    end_date DATE NOT NULL COMMENT '预算结束日期',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### 系统日志表 (system_logs)
```sql
CREATE TABLE system_logs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    log_type ENUM('info', 'warning', 'error', 'critical') NOT NULL COMMENT '日志类型',
    module VARCHAR(50) NOT NULL COMMENT '相关模块',
    message TEXT NOT NULL COMMENT '日志消息',
    user_id INT COMMENT '关联用户ID（如适用）',
    ip_address VARCHAR(50) COMMENT '操作IP地址',
    device_type VARCHAR(50) COMMENT '设备类型（桌面/移动）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_log_type (log_type),
    INDEX idx_module (module),
    INDEX idx_created_at (created_at)
);
```

#### 用户设备表 (user_devices)
```sql
CREATE TABLE user_devices (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '设备ID',
    user_id INT NOT NULL COMMENT '关联的用户ID',
    device_type VARCHAR(50) NOT NULL COMMENT '设备类型（desktop/mobile/tablet）',
    device_name VARCHAR(100) COMMENT '设备名称',
    os_name VARCHAR(50) COMMENT '操作系统名称',
    os_version VARCHAR(50) COMMENT '操作系统版本',
    browser_name VARCHAR(50) COMMENT '浏览器名称',
    browser_version VARCHAR(50) COMMENT '浏览器版本',
    last_login_time DATETIME COMMENT '最后登录时间',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 5.3 数据库操作流程

所有数据库变更通过SQL迁移脚本进行管理，并由DBA在远程服务器上手动执行。

## 6. API设计

### 6.1 API设计原则
- 统一接口规范
- 版本控制
- 模块前缀
- 完整CRUD操作
- 查询参数标准化
- 错误处理一致性
- 文档自动生成

### 6.2 核心API端点

#### 6.2.1 用户API
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - 刷新令牌
- `POST /api/v1/auth/logout` - 用户登出
- `GET /api/v1/users/me` - 获取当前用户信息
- `PUT /api/v1/users/me` - 更新用户信息

#### 6.2.2 账户API
- `GET /api/v1/accounts` - 获取用户所有账户列表
- `GET /api/v1/accounts/{id}` - 获取特定账户详情
- `POST /api/v1/accounts` - 创建新账户
- `PUT /api/v1/accounts/{id}` - 更新账户信息
- `DELETE /api/v1/accounts/{id}` - 删除账户

#### 6.2.3 交易API
- `GET /api/v1/transactions` - 获取交易列表(支持筛选)
- `GET /api/v1/transactions/{id}` - 获取特定交易详情
- `POST /api/v1/transactions` - 创建新交易
- `PUT /api/v1/transactions/{id}` - 更新交易信息
- `DELETE /api/v1/transactions/{id}` - 删除交易

#### 6.2.4 分析API
- `GET /api/v1/analytics/asset-allocation` - 获取资产配置分析
- `GET /api/v1/analytics/income-expense` - 获取收支分析
- `GET /api/v1/analytics/trends` - 获取财务趋势分析
- `GET /api/v1/analytics/net-worth` - 获取净资产分析

#### 6.2.5 预算API
- `GET /api/v1/budgets` - 获取预算列表
- `GET /api/v1/budgets/{id}` - 获取特定预算详情
- `POST /api/v1/budgets` - 创建新预算
- `PUT /api/v1/budgets/{id}` - 更新预算信息
- `DELETE /api/v1/budgets/{id}` - 删除预算

#### 6.2.6 系统管理API
- `GET /api/v1/system/health` - 获取系统健康状态
- `GET /api/v1/system/logs` - 获取系统日志（仅管理员）
- `GET /api/v1/system/stats` - 获取系统统计数据（仅管理员）
- `POST /api/v1/system/backup` - 触发数据库备份（仅管理员）

#### 6.2.7 移动端特定API
- `GET /api/v1/mobile/config` - 获取移动端特定配置
- `POST /api/v1/mobile/feedback` - 提交移动端用户反馈
- `GET /api/v1/mobile/optimized-dashboard` - 获取针对移动端优化的仪表板数据
- `GET /api/v1/mobile/sync-status` - 检查数据同步状态

### 6.3 API响应格式标准
所有API响应遵循统一的JSON格式。

### 6.4 API文档系统（Swagger/OpenAPI）
系统采用FastAPI内置的Swagger/OpenAPI自动生成API文档。

## 7. 后端模块设计

### 7.1 用户模块
#### 7.1.1 核心功能
- 用户注册与登录
- JWT令牌管理
- 用户信息管理
- 权限控制

#### 7.1.2 目录结构
```
/app
  /modules
    /users
      __init__.py
      models.py        # 数据模型
      schemas.py       # Pydantic模式
      repository.py    # 数据访问层
      service.py       # 业务逻辑层
      router.py        # API路由
      exceptions.py    # 自定义异常
      dependencies.py  # 依赖注入
```

#### 7.1.3 主要服务接口
```python
class UserService:
    async def register_user(self, user_data: UserCreate) -> User:
        """注册新用户"""
        
    async def authenticate_user(self, username: str, password: str) -> User:
        """验证用户凭据"""
        
    async def get_user_by_id(self, user_id: int) -> User:
        """通过ID获取用户"""
        
    async def update_user(self, user_id: int, user_data: UserUpdate) -> User:
        """更新用户信息"""
```

### 7.2 账户模块
#### 7.2.1 核心功能
- 账户管理（创建、读取、更新、删除）
- 不同类型账户的特定处理
- 账户余额计算

#### 7.2.2 目录结构
```
/app
  /modules
    /accounts
      __init__.py
      models.py
      schemas.py
      repository.py
      service.py
      router.py
      exceptions.py
```

#### 7.2.3 主要服务接口
```python
class AccountService:
    async def create_account(self, user_id: int, account_data: AccountCreate) -> Account:
        """创建新账户"""
        
    async def get_accounts_by_user(self, user_id: int, filters: AccountFilters) -> List[Account]:
        """获取用户所有账户"""
        
    async def update_account(self, account_id: int, account_data: AccountUpdate) -> Account:
        """更新账户信息"""
        
    async def delete_account(self, account_id: int) -> bool:
        """删除账户（软删除）"""
        
    async def calculate_balance(self, account_id: int) -> Decimal:
        """重新计算账户余额"""
```

### 7.3 交易模块
#### 7.3.1 核心功能
- 交易记录管理
- 交易分类
- 交易搜索和筛选

#### 7.3.2 目录结构
```
/app
  /modules
    /transactions
      __init__.py
      models.py
      schemas.py
      repository.py
      service.py
      router.py
      exceptions.py
```

#### 7.3.3 主要服务接口
```python
class TransactionService:
    async def create_transaction(self, transaction_data: TransactionCreate) -> Transaction:
        """创建新交易记录"""
        
    async def get_transactions(self, user_id: int, filters: TransactionFilters) -> List[Transaction]:
        """获取交易记录列表，支持筛选"""
        
    async def update_transaction(self, transaction_id: int, transaction_data: TransactionUpdate) -> Transaction:
        """更新交易记录"""
        
    async def delete_transaction(self, transaction_id: int) -> bool:
        """删除交易记录"""
```

### 7.4 分析模块
#### 7.4.1 核心功能
- 财务数据分析
- 资产配置统计
- 趋势分析

#### 7.4.2 目录结构
```
/app
  /modules
    /analytics
      __init__.py
      schemas.py
      service.py
      router.py
      utils/
        __init__.py
        calculators.py
        formatters.py
```

#### 7.4.3 主要服务接口
```python
class AnalyticsService:
    async def get_asset_allocation(self, user_id: int) -> AssetAllocation:
        """获取资产配置分析"""
        
    async def get_income_expense(self, user_id: int, period: str) -> IncomeExpense:
        """获取收支分析"""
        
    async def get_net_worth_history(self, user_id: int, period: str) -> List[NetWorthPoint]:
        """获取净资产历史数据"""
```

### 7.5 预算模块
#### 7.5.1 核心功能
- 预算设置和管理
- 预算执行跟踪
- 预算分析和提醒

#### 7.5.2 目录结构
```
/app
  /modules
    /budgets
      __init__.py
      models.py
      schemas.py
      repository.py
      service.py
      router.py
      exceptions.py
```

#### 7.5.3 主要服务接口
```python
class BudgetService:
    async def create_budget(self, user_id: int, budget_data: BudgetCreate) -> Budget:
        """创建新预算"""
        
    async def get_budgets(self, user_id: int) -> List[Budget]:
        """获取用户所有预算"""
        
    async def update_budget(self, budget_id: int, budget_data: BudgetUpdate) -> Budget:
        """更新预算信息"""
        
    async def calculate_budget_progress(self, budget_id: int) -> BudgetProgress:
        """计算预算执行进度"""
        
    async def check_budget_alerts(self, user_id: int) -> List[BudgetAlert]:
        """检查预算提醒"""
```

## 8. Redis缓存设计

### 8.1 缓存策略
- 缓存内容：用户会话、常用数据、计算密集型结果
- 缓存键命名约定
- 缓存过期策略

### 8.2 缓存实现
#### 8.2.1 Redis配置
```python
# Redis客户端配置
redis_client = aioredis.from_url(
    settings.REDIS_URL,
    encoding="utf-8",
    decode_responses=True,
    socket_timeout=5,
    socket_keepalive=True
)

# 依赖注入
def get_redis() -> Redis:
    return redis_client
```

#### 8.2.2 服务层缓存封装
```python
class CachedAccountService:
    def __init__(self, repository: AccountRepository, redis: Redis):
        self.repository = repository
        self.redis = redis
        
    async def get_accounts_by_user(self, user_id: int) -> List[Account]:
        # 尝试从缓存获取
        cache_key = f"finance-app:user:{user_id}:accounts"
        cached_data = await self.redis.get(cache_key)
        
        if cached_data:
            return json.loads(cached_data)
            
        # 缓存未命中，从数据库获取
        accounts = await self.repository.get_by_user_id(user_id)
        
        # 存入缓存
        await self.redis.set(
            cache_key,
            json.dumps([account.dict() for account in accounts]),
            ex=300  # 5分钟过期
        )
        
        return accounts
        
    async def create_account(self, user_id: int, data: AccountCreate) -> Account:
        account = await self.repository.create(user_id, data)
        
        # 使相关缓存失效
        await self.redis.delete(f"finance-app:user:{user_id}:accounts")
        
        return account
```

### 8.3 缓存监控与管理
- 缓存命中率
- 内存使用情况
- 慢查询日志
- 连接数状态

## 9. 安全性设计

### 9.1 认证与授权
- 使用JWT进行用户认证
- 基于角色的访问控制(RBAC)
- 密码加盐哈希存储(bcrypt)
- 令牌自动续期机制
- IP地址和设备验证（可选）

### 9.2 数据安全
- HTTPS加密传输
- 敏感数据加密存储
- SQL注入防护
- 输入数据验证和清洗
- 定期数据备份策略

### 9.3 API安全
- 请求限流
- CORS策略
- API密钥管理（第三方API集成）
- 异常请求监控与告警
- API版本控制，确保向后兼容

## 10. 后端开发环境与部署

### 10.1 开发环境
- Python 3.8+
- Docker容器（MySQL, Redis）
- 本地开发服务器

### 10.2 测试环境
- Gunicorn + Uvicorn
- 远程MySQL和Redis服务器
- 自动化测试工具（pytest）

### 10.3 系统配置管理
所有配置集中在单一配置文件中管理，支持分层和环境变量覆盖。

## 11. 模块化设计与扩展性

### 11.1 领域驱动设计(DDD)
- 根据业务领域划分清晰的模块边界
- 每个模块拥有自己的模型、仓储和服务
- 定义明确的模块间通信接口和契约
- 减少模块间直接依赖，使用事件驱动模式

### 11.2 模块间通信机制
- 采用发布-订阅模式实现模块间松耦合通信
- 使用事件总线传递领域事件
- 关键业务变更记录为事件流
- 支持事件回放和状态重建

### 11.3 依赖管理策略
- 高层模块不依赖低层模块，二者都依赖抽象
- 使用接口和抽象类定义模块间交互契约
- 使用依赖注入实现运行时组件装配
- 避免模块间循环依赖

## 12. 后端测试策略

### 12.1 测试级别
- 单元测试
- 集成测试
- API测试
- 性能测试

### 12.2 测试工具
- pytest, pytest-fastapi
- Locust

### 12.3 测试自动化
- CI/CD集成
- 预提交钩子
- 定期全面测试 