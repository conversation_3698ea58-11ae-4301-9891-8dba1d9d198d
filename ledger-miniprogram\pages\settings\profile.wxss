/* pages/settings/profile.wxss */
.profile-page {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding: 32rpx;
}

.avatar-section {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  text-align: center;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.user-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  background: linear-gradient(135deg, #007AFF, #AF52DE);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
}

.avatar-text {
  font-size: 64rpx;
  font-weight: bold;
  color: white;
}

.change-avatar-btn {
  color: #007AFF;
  font-size: 28rpx;
  padding: 16rpx 32rpx;
  border: 1rpx solid #007AFF;
  border-radius: 40rpx;
  background-color: transparent;
}

.form-section {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 40rpx;
  overflow: hidden;
}

.form-group {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.form-group:last-child {
  border-bottom: none;
}

.form-label {
  width: 160rpx;
  font-size: 32rpx;
  color: #000000;
  font-weight: 500;
}

.form-input {
  flex: 1;
  font-size: 32rpx;
  color: #000000;
  padding: 16rpx 0;
}

.form-input[disabled] {
  color: #8E8E93;
}

.form-value {
  flex: 1;
  font-size: 32rpx;
  color: #8E8E93;
  padding: 16rpx 0;
}

.action-section {
  margin-bottom: 40rpx;
}

.btn-primary {
  background-color: #007AFF;
  color: white;
  border-radius: 20rpx;
  padding: 24rpx 32rpx;
  font-size: 34rpx;
  border: none;
  width: 100%;
}

.btn-primary:active {
  background-color: #0056CC;
}

.btn-secondary {
  background-color: #F2F2F7;
  color: #007AFF;
  border-radius: 20rpx;
  padding: 24rpx 32rpx;
  font-size: 34rpx;
  border: none;
  flex: 1;
  margin-right: 20rpx;
}

.btn-secondary:active {
  background-color: #E5E5EA;
}

.edit-actions {
  display: flex;
  align-items: center;
}

.save-btn {
  flex: 1;
}

.password-section {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
}

.section-title {
  padding: 32rpx 32rpx 16rpx;
  font-size: 28rpx;
  color: #8E8E93;
  font-weight: 500;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.list-item:last-child {
  border-bottom: none;
}

.cell-icon {
  font-size: 40rpx;
  margin-right: 32rpx;
}

.cell-title {
  flex: 1;
  font-size: 34rpx;
  color: #000000;
}

.cell-arrow {
  font-size: 32rpx;
  color: #C7C7CC;
  font-weight: 500;
}
