/* pages/settings/index.wxss */
.settings-page {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding: 32rpx;
}

.user-card {
  display: flex;
  align-items: center;
  padding: 40rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 40rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: linear-gradient(135deg, #007AFF, #AF52DE);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
}

.avatar-text {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 8rpx;
}

.user-email {
  font-size: 26rpx;
  color: #8E8E93;
}

.edit-btn {
  background: none;
  border: none;
  color: #007AFF;
  font-size: 34rpx;
  padding: 16rpx 24rpx;
}

.settings-list {
  
}

.list-section {
  margin-bottom: 40rpx;
}

.section-header {
  padding: 0 32rpx 16rpx;
  font-size: 26rpx;
  color: #8E8E93;
  text-transform: uppercase;
  letter-spacing: 1rpx;
}

.section-content {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.list-item:last-child {
  border-bottom: none;
}

.cell-icon {
  font-size: 40rpx;
  margin-right: 32rpx;
}

.cell-title {
  flex: 1;
  font-size: 34rpx;
  color: #000000;
}

.cell-value {
  font-size: 26rpx;
  color: #8E8E93;
  margin-right: 16rpx;
}

.cell-arrow {
  font-size: 32rpx;
  color: #C7C7CC;
  font-weight: 500;
}

.logout-section {
  margin-top: 40rpx;
}

.btn-danger {
  background-color: #FF3B30;
  color: white;
  border-radius: 20rpx;
  padding: 24rpx 32rpx;
  font-size: 34rpx;
  border: none;
  width: 100%;
}

.btn-danger:active {
  background-color: #D70015;
}

.placeholder {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #8E8E93;
  font-size: 30rpx;
}
