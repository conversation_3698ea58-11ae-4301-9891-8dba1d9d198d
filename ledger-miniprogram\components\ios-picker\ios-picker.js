// components/ios-picker/ios-picker.js
Component({
  properties: {
    // 是否显示选择器
    visible: {
      type: Boolean,
      value: false
    },
    // 标题
    title: {
      type: String,
      value: '请选择'
    },
    // 选项数组
    options: {
      type: Array,
      value: []
    },
    // 当前选中的值
    selectedValue: {
      type: String,
      value: ''
    },
    // 值字段名
    valueKey: {
      type: String,
      value: 'value'
    },
    // 标签字段名
    labelKey: {
      type: String,
      value: 'label'
    },
    // 图标字段名
    iconKey: {
      type: String,
      value: 'icon'
    },
    // 点击遮罩是否关闭
    closeOnOverlay: {
      type: Boolean,
      value: true
    }
  },

  methods: {
    // 处理遮罩点击
    handleOverlayClick() {
      if (this.properties.closeOnOverlay) {
        this.triggerEvent('cancel')
      }
    },

    // 阻止事件冒泡
    stopPropagation() {
      // 阻止点击选择器内容时关闭
    },

    // 处理取消
    handleCancel() {
      this.triggerEvent('cancel')
    },

    // 处理确认
    handleConfirm() {
      this.triggerEvent('confirm')
    },

    // 选择项目
    selectItem(e) {
      const value = e.currentTarget.dataset.value
      const index = e.currentTarget.dataset.index
      const item = this.properties.options[index]
      
      this.triggerEvent('select', {
        value: value,
        item: item,
        index: index
      })
    }
  }
})
