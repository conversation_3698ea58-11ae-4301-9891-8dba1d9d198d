// pages/settings/about.js
Page({
  data: {
    appInfo: {
      version: '1.0.0',
      buildNumber: '2024.01.15.001',
      releaseDate: '2024-01-15',
      developer: '个人记账团队'
    },
    contactInfo: {
      email: '<EMAIL>',
      website: 'https://ledger-app.com',
      wechatGroup: 'ledger_users'
    },
    features: [
      {
        icon: '📊',
        title: '智能记账',
        description: '快速记录收支，自动分类整理'
      },
      {
        icon: '📈',
        title: '数据分析',
        description: '多维度统计分析，洞察消费习惯'
      },
      {
        icon: '💳',
        title: '多账户管理',
        description: '支持多个账户，资产一目了然'
      },
      {
        icon: '🔒',
        title: '数据安全',
        description: '本地加密存储，云端同步备份'
      },
      {
        icon: '📱',
        title: '简洁界面',
        description: '精美设计，操作简单直观'
      },
      {
        icon: '🌙',
        title: '深色模式',
        description: '护眼深色主题，夜间使用更舒适'
      }
    ]
  },

  onLoad() {
    console.log('关于应用页面加载')
    this.loadAppInfo()
  },

  // 加载应用信息
  loadAppInfo() {
    // 这里可以从服务器获取最新的应用信息
    // 或者从本地配置文件读取
    try {
      const appInfo = wx.getAccountInfoSync()
      if (appInfo && appInfo.miniProgram) {
        this.setData({
          'appInfo.version': appInfo.miniProgram.version || '1.0.0'
        })
      }
    } catch (error) {
      console.error('获取应用信息失败:', error)
    }
  },

  // 复制邮箱
  copyEmail() {
    wx.setClipboardData({
      data: this.data.contactInfo.email,
      success: () => {
        wx.showToast({
          title: '邮箱已复制',
          icon: 'success'
        })
      }
    })
  },

  // 打开官网
  openWebsite() {
    wx.showModal({
      title: '打开网站',
      content: `即将打开 ${this.data.contactInfo.website}`,
      success: (res) => {
        if (res.confirm) {
          // 小程序中无法直接打开外部链接
          // 可以复制链接到剪贴板
          wx.setClipboardData({
            data: this.data.contactInfo.website,
            success: () => {
              wx.showToast({
                title: '链接已复制，请在浏览器中打开',
                icon: 'none',
                duration: 3000
              })
            }
          })
        }
      }
    })
  },

  // 加入用户群
  joinGroup() {
    wx.showModal({
      title: '加入用户群',
      content: '请添加客服微信号获取群邀请',
      confirmText: '复制微信号',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: this.data.contactInfo.wechatGroup,
            success: () => {
              wx.showToast({
                title: '微信号已复制',
                icon: 'success'
              })
            }
          })
        }
      }
    })
  },

  // 显示隐私政策
  showPrivacyPolicy() {
    wx.showModal({
      title: '隐私政策',
      content: '我们重视您的隐私保护。所有数据仅用于应用功能，不会泄露给第三方。详细内容请访问官网查看。',
      showCancel: false
    })
  },

  // 显示服务条款
  showTermsOfService() {
    wx.showModal({
      title: '服务条款',
      content: '使用本应用即表示您同意我们的服务条款。详细内容请访问官网查看。',
      showCancel: false
    })
  },

  // 显示开源许可
  showLicense() {
    wx.showModal({
      title: '开源许可',
      content: '本应用使用了多个开源项目，感谢开源社区的贡献。详细许可信息请访问官网查看。',
      showCancel: false
    })
  },

  // 检查更新
  checkUpdate() {
    wx.showLoading({
      title: '检查中...'
    })

    try {
      // 检查小程序更新
      const updateManager = wx.getUpdateManager()

      updateManager.onCheckForUpdate((res) => {
        wx.hideLoading()
        if (res.hasUpdate) {
          wx.showModal({
            title: '发现新版本',
            content: '发现新版本，是否立即更新？',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.showLoading({
                  title: '下载中...'
                })
              }
            }
          })
        } else {
          wx.showToast({
            title: '已是最新版本',
            icon: 'success'
          })
        }
      })

      updateManager.onUpdateReady(() => {
        wx.hideLoading()
        wx.showModal({
          title: '更新完成',
          content: '新版本已下载完成，是否立即重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })

      updateManager.onUpdateFailed(() => {
        wx.hideLoading()
        wx.showToast({
          title: '更新失败，请稍后重试',
          icon: 'none'
        })
      })

      // 如果没有更新管理器（开发工具中），显示默认消息
      if (!updateManager) {
        setTimeout(() => {
          wx.hideLoading()
          wx.showToast({
            title: '已是最新版本',
            icon: 'success'
          })
        }, 1500)
      }
    } catch (error) {
      wx.hideLoading()
      console.error('检查更新失败:', error)
      wx.showToast({
        title: '检查更新失败',
        icon: 'none'
      })
    }
  },

  // 给应用评分
  rateApp() {
    wx.showModal({
      title: '应用评分',
      content: '如果您觉得应用不错，请在应用商店给我们好评，这对我们很重要！',
      confirmText: '去评分',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '感谢您的支持！',
            icon: 'success'
          })
        }
      }
    })
  },

  // 分享应用
  shareApp() {
    wx.showShareMenu({
      withShareTicket: true,
      success: () => {
        wx.showToast({
          title: '请点击右上角分享',
          icon: 'none'
        })
      }
    })
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '个人记账 - 简单易用的财务管理工具',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.jpg' // 需要添加分享图片
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '个人记账 - 让理财变得简单',
      imageUrl: '/images/share-cover.jpg'
    }
  }
})
