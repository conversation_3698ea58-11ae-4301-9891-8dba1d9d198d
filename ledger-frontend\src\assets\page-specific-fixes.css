/**
 * 页面特定修复样式
 * 针对特定页面在深色模式下的样式问题
 */

/* 交易记录页面特定修复 */
.dark-mode .transactions-view {
  color: #e2e2e6 !important;
}

.dark-mode .transactions-view .el-table {
  background-color: #282838 !important;
}

.dark-mode .transactions-view .page-title {
  color: #e2e2e6 !important;
}

.dark-mode .transactions-view .filter-bar {
  background-color: #282838 !important;
  border-color: #363646 !important;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.dark-mode .transactions-view .el-table__header th {
  background-color: #23232e !important;
  color: #e2e2e6 !important;
  border-bottom-color: #363646 !important;
}

.dark-mode .transactions-view .el-table__body td {
  color: #e2e2e6 !important;
  border-bottom-color: #363646 !important;
}

.dark-mode .transactions-view .el-input__inner,
.dark-mode .transactions-view .el-range-editor .el-range-input {
  background-color: #313142 !important;
  color: #e2e2e6 !important;
  border-color: #363646 !important;
}

.dark-mode .transactions-view .el-button.el-button--text {
  color: #409eff !important;
}

.dark-mode .transactions-view .el-button.el-button--danger.is-text {
  color: #f56c6c !important;
}

.dark-mode .transactions-view .el-button--primary {
  background-color: #409eff !important;
  color: #ffffff !important;
}

.dark-mode .transactions-view .transaction-list .transaction-item {
  background-color: #282838 !important;
  border-color: #363646 !important;
}

.dark-mode .transactions-view .transaction-list .transaction-item:hover {
  background-color: #313142 !important;
}

/* 财务分析页面特定修复 */
.dark-mode .analytics-view {
  color: #e2e2e6 !important;
}

.dark-mode .analytics-view .page-title {
  color: #e2e2e6 !important;
}

.dark-mode .analytics-view .chart-card {
  background-color: #282838 !important;
  border-color: #363646 !important;
}

.dark-mode .analytics-view .chart-container {
  background-color: #282838 !important;
}

/* 优化单选按钮组样式 */
.dark-mode .analytics-view .el-radio-group {
  background-color: #282838 !important;
  border-radius: 4px;
  overflow: hidden;
}

.dark-mode .analytics-view .el-radio-button__inner {
  background-color: #282838 !important;
  color: #a0a0b0 !important;
  border-color: #363646 !important;
}

.dark-mode .analytics-view .el-radio-button__original-radio:checked + .el-radio-button__inner {
  background-color: #409eff !important;
  color: #ffffff !important;
  border-color: #409eff !important;
  box-shadow: -1px 0 0 0 #409eff !important;
}

/* 优化日期选择器样式 */
.dark-mode .analytics-view .filter-item .filter-label {
  color: #e2e2e6 !important;
}

.dark-mode .analytics-view .el-select .el-input__inner {
  background-color: #313142 !important;
  color: #e2e2e6 !important;
  border-color: #363646 !important;
  width: 120px !important;
}

.dark-mode .analytics-view .date-select {
  background-color: #313142 !important;
}

/* 图表文字颜色修复 */
.dark-mode .analytics-view .chart-title {
  color: #e2e2e6 !important;
}

.dark-mode .analytics-view .chart-subtitle {
  color: #a0a0b0 !important;
}

.dark-mode .analytics-view .data-summary {
  color: #e2e2e6 !important;
}

.dark-mode .analytics-view .data-summary .value {
  color: #e2e2e6 !important;
}

.dark-mode .analytics-view .data-summary .label {
  color: #a0a0b0 !important;
}

/* 交易表格行特殊处理 */
.dark-mode .transactions-view .el-table__row.income-row td {
  color: #67c23a !important;
}

.dark-mode .transactions-view .el-table__row.expense-row td {
  color: #f56c6c !important;
}

.dark-mode .transactions-view .el-table__row.transfer-row td {
  color: #409eff !important;
}

/* 解决第二行文字颜色 */
.dark-mode .transactions-view .el-table__row td.el-table__cell .secondary-text {
  color: #a0a0b0 !important;
}

/* 修复排序箭头 */
.dark-mode .transactions-view .el-table .sort-caret.ascending,
.dark-mode .transactions-view .el-table .sort-caret.descending {
  border-bottom-color: #909399 !important;
}

.dark-mode .transactions-view .cell .caret-wrapper {
  color: #909399 !important;
}

/* 修复日期选择器 */
.dark-mode .el-date-picker {
  background-color: #282838 !important;
  color: #e2e2e6 !important;
  border-color: #363646 !important;
}

.dark-mode .el-date-picker .el-picker-panel__content,
.dark-mode .el-date-picker .el-date-picker__header {
  background-color: #282838 !important;
  color: #e2e2e6 !important;
}

.dark-mode .el-date-picker .el-picker-panel__icon-btn {
  color: #a0a0b0 !important;
}

.dark-mode .el-date-picker .el-date-table th {
  color: #a0a0b0 !important;
  border-bottom-color: #363646 !important;
}

.dark-mode .el-date-picker .el-date-table td.available:not(.current):not(.today):hover {
  color: #409eff !important;
}

.dark-mode .el-date-picker .el-date-table td.today span {
  color: #409eff !important;
}

/* 修复ECharts工具提示 */
.dark-mode .echarts-tooltip {
  background-color: #282838 !important;
  color: #e2e2e6 !important;
  border-color: #363646 !important;
}

/* 增强ECharts图例文字可见性 */
.dark-mode .analytics-view .chart-container text {
  fill: #e2e2e6 !important;
}

.dark-mode .analytics-view .chart-container .ec-legend text {
  fill: #e2e2e6 !important;
}

.dark-mode .analytics-view .chart-container .ec-legend-item {
  color: #e2e2e6 !important;
}

/* 确保图表内所有文字元素可见 */
.dark-mode .analytics-view .chart-container .ec-axis-line,
.dark-mode .analytics-view .chart-container .ec-axis-label,
.dark-mode .analytics-view .chart-container .ec-title,
.dark-mode .analytics-view .chart-container .ec-legend,
.dark-mode .analytics-view .chart-container [text-anchor] {
  fill: #e2e2e6 !important;
  stroke: none !important;
  color: #e2e2e6 !important;
}

/* 确保图表各种标题和标签可见 */
.dark-mode .analytics-view .chart-container .chart-title,
.dark-mode .analytics-view .chart-container .chart-label {
  color: #e2e2e6 !important;
} 