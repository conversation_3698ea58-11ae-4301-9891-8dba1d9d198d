// pages/accounts/detail.js
const api = require('../../utils/api.js')

Page({
  data: {
    accountId: '',
    account: {},
    recentTransactions: [],
    loading: false,
    hideAmounts: false
  },

  onLoad(options) {
    console.log('账户详情页面加载', options)
    this.loadHideAmountsState()
    if (options.id) {
      this.setData({ accountId: options.id })
      this.loadAccountDetail()
    } else {
      wx.showToast({
        title: '账户ID缺失',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  onShow() {
    // 从编辑页面返回时刷新数据
    if (this.data.accountId) {
      this.loadAccountDetail()
    }
  },

  onPullDownRefresh() {
    this.loadAccountDetail()
  },

  // 加载账户详情
  async loadAccountDetail() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      // 并行加载账户详情和相关交易
      const [accountDetail, transactions] = await Promise.all([
        api.accounts.getDetail(this.data.accountId),
        api.transactions.getList({ account_id: this.data.accountId, limit: 5 })
      ])

      console.log('账户详情:', accountDetail)
      console.log('相关交易:', transactions)

      // 处理账户数据
      const processedAccount = {
        ...accountDetail,
        icon: this.getAccountIcon(accountDetail.account_type),
        balanceText: this.formatAmount(accountDetail.current_balance),
        type_display: this.getAccountTypeDisplay(accountDetail.account_type),
        created_at_text: this.formatDate(accountDetail.created_at),
        updated_at_text: this.formatDate(accountDetail.updated_at)
      }

      // 处理交易数据
      const processedTransactions = (transactions.items || transactions || []).map(transaction => ({
        id: transaction.id,
        type: transaction.transaction_type,
        icon: this.getTransactionIcon(transaction.category_name, transaction.transaction_type),
        description: transaction.description,
        category: transaction.category_name,
        amountText: this.formatTransactionAmount(transaction.amount, transaction.transaction_type),
        date: this.formatTransactionDate(transaction.transaction_date)
      }))

      this.setData({
        account: processedAccount,
        recentTransactions: processedTransactions
      })

    } catch (error) {
      console.error('加载账户详情失败:', error)
      
      if (!error.message.includes('登录已过期')) {
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    } finally {
      this.setData({ loading: false })
      wx.stopPullDownRefresh()
    }
  },

  // 格式化金额
  formatAmount(amount) {
    return parseFloat(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  },

  // 格式化交易金额
  formatTransactionAmount(amount, type) {
    const formattedAmount = this.formatAmount(Math.abs(amount))
    return type === 'income' ? `+¥${formattedAmount}` : `-¥${formattedAmount}`
  },

  // 格式化日期
  formatDate(dateString) {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  },

  // 格式化交易日期
  formatTransactionDate(dateString) {
    const date = new Date(dateString)
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
    const transactionDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

    const timeStr = date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })

    if (transactionDate.getTime() === today.getTime()) {
      return `今天 ${timeStr}`
    } else if (transactionDate.getTime() === yesterday.getTime()) {
      return `昨天 ${timeStr}`
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit'
      }) + ` ${timeStr}`
    }
  },

  // 获取账户图标
  getAccountIcon(type) {
    const iconMap = {
      'bank': '🏦',
      'fund': '📊',
      'stock': '📈',
      'debt': '💳'
    }
    return iconMap[type] || '💰'
  },

  // 获取账户类型显示名称
  getAccountTypeDisplay(type) {
    const typeMap = {
      'bank': '银行账户',
      'fund': '基金账户',
      'stock': '股票账户',
      'debt': '负债账户'
    }
    return typeMap[type] || '其他账户'
  },

  // 获取交易图标
  getTransactionIcon(categoryName, type) {
    const iconMap = {
      '餐饮美食': '🍽️',
      '交通出行': '🚗',
      '购物消费': '🛍️',
      '生活服务': '🏠',
      '医疗健康': '🏥',
      '教育培训': '📚',
      '娱乐休闲': '🎮',
      '工资收入': '💰',
      '投资收益': '📈',
      '其他收入': '💵',
      '转账': '🔄'
    }

    return iconMap[categoryName] || (type === 'income' ? '💰' : '💸')
  },

  // 编辑账户
  editAccount() {
    wx.navigateTo({
      url: `/pages/accounts/edit?id=${this.data.accountId}`
    })
  },

  // 添加交易
  addTransaction() {
    wx.navigateTo({
      url: `/pages/transactions/add?account_id=${this.data.accountId}`
    })
  },

  // 查看交易记录
  viewTransactions() {
    wx.navigateTo({
      url: `/pages/transactions/list?account_id=${this.data.accountId}`
    })
  },

  // 查看所有交易
  viewAllTransactions() {
    this.viewTransactions()
  },

  // 删除账户
  deleteAccount() {
    wx.showModal({
      title: '确认删除',
      content: '删除账户将同时删除该账户下的所有交易记录，此操作不可恢复。确定要删除吗？',
      confirmText: '删除',
      confirmColor: '#FF3B30',
      success: async (res) => {
        if (res.confirm) {
          await this.performDeleteAccount()
        }
      }
    })
  },

  // 执行删除账户
  async performDeleteAccount() {
    try {
      wx.showLoading({
        title: '删除中...'
      })

      await api.accounts.delete(this.data.accountId)

      wx.hideLoading()
      wx.showToast({
        title: '账户已删除',
        icon: 'success'
      })

      // 返回账户列表
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)

    } catch (error) {
      wx.hideLoading()
      console.error('删除账户失败:', error)
      wx.showToast({
        title: error.message || '删除失败',
        icon: 'none'
      })
    }
  },

  // 返回
  goBack() {
    wx.navigateBack()
  },

  // 加载隐藏金额状态
  loadHideAmountsState() {
    try {
      const hideAmounts = wx.getStorageSync('hideAmounts')
      this.setData({
        hideAmounts: hideAmounts === 'true' || hideAmounts === true
      })
    } catch (error) {
      console.error('加载隐藏金额状态失败:', error)
    }
  },

  // 切换金额显示/隐藏
  toggleAmountVisibility() {
    const newHideState = !this.data.hideAmounts
    this.setData({
      hideAmounts: newHideState
    })

    // 保存状态到本地存储
    try {
      wx.setStorageSync('hideAmounts', newHideState)
    } catch (error) {
      console.error('保存隐藏金额状态失败:', error)
    }
  }
})
