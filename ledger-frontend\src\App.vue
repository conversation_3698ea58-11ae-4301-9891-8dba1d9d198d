<script setup lang="ts">
import { onMounted, ref } from 'vue'

// 深色模式状态
const darkMode = ref(false)

// 检查并应用深色模式设置
const checkDarkMode = () => {
  // 检查localStorage或用户设置
  darkMode.value = localStorage.getItem('darkMode') === 'true'
  if (darkMode.value) {
    document.documentElement.classList.add('dark-mode')
    document.body.classList.add('dark-mode')
  }
}

// 组件挂载时检查深色模式
onMounted(() => {
  checkDarkMode()
})
</script>

<template>
  <div class="app-container" :class="{ 'dark-theme': darkMode }">
    <router-view />
  </div>
</template>

<style lang="scss">
:root {
  /* Apple风格颜色变量 */
  --apple-blue: #007AFF;
  --apple-green: #34C759;
  --apple-red: #FF3B30;
  --apple-yellow: #FFCC00;
  --apple-gray: #8E8E93;
  --apple-light-gray: #F2F2F7;
  --apple-dark-gray: #1C1C1E;
  
  /* 基础尺寸 */
  --border-radius: 10px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  
  /* 响应式断点 */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 768px;
  --breakpoint-md: 992px;
  --breakpoint-lg: 1200px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'San Francisco', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

body {
  background-color: var(--apple-light-gray);
  color: var(--apple-dark-gray);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.app-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  transition: background-color 0.3s ease, color 0.3s ease;
  
  /* 响应式调整 */
  @media (max-width: 768px) {
    padding: 1rem;
  }
  
  @media (max-width: 480px) {
    padding: 0.5rem;
  }
}

/* Element Plus主题覆盖 */
.el-button {
  border-radius: var(--border-radius);
  font-weight: 500;
  }

.el-button--primary {
  background-color: var(--apple-blue);
  border-color: var(--apple-blue);
}

.el-card {
  border-radius: var(--border-radius);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease, transform 0.3s ease, background-color 0.3s ease, color 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
  
  /* 移动端卡片样式调整 */
  @media (max-width: 768px) {
    margin-bottom: 1rem;
    
    &:hover {
      transform: none; /* 移动端禁用悬浮效果 */
    }
  }
}

/* 深色模式覆盖 - 基础元素 */
.dark-mode {
  & body {
    background-color: #1e1e2e !important;
    color: #e2e2e6 !important;
  }
  
  & .app-container {
    background-color: #1e1e2e !important;
    color: #e2e2e6 !important;
  }

  & .app-layout,
  & .main-container,
  & .content,
  & .el-main,
  & .el-container,
  & .el-aside {
    background-color: #1e1e2e !important;
  }

  & .el-card,
  & .settings-section,
  & .el-card__body {
    background-color: #282838 !important;
    color: #e2e2e6 !important;
    border-color: #363646 !important;
  }
}

/* 特别添加：确保白色区域问题解决 */
html, body, #app {
  min-height: 100vh;
  width: 100%;
}

html.dark-mode {
  background-color: #1e1e2e !important;
  color: #e2e2e6 !important;
}

body.dark-mode {
  background-color: #1e1e2e !important;
  color: #e2e2e6 !important;
}

.dark-mode #app {
  background-color: #1e1e2e !important;
  color: #e2e2e6 !important;
}

.dark-mode .app-container {
  background-color: #1e1e2e !important;
  color: #e2e2e6 !important;
}

/* 全局响应式样式 */
@media (max-width: 768px) {
  .el-form-item {
    margin-bottom: 12px;
  }
  
  .el-row {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  
  .el-col {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  
  /* 字体调整 */
  h1 {
    font-size: 1.5rem !important;
  }
  
  h2 {
    font-size: 1.25rem !important;
  }
  
  h3 {
    font-size: 1.1rem !important;
  }
  
  /* 确保表格能横向滚动 */
  .el-table {
    width: 100%;
    overflow-x: auto;
    display: block;
  }
}

/* 额外的小屏幕调整 */
@media (max-width: 480px) {
  .el-button + .el-button {
    margin-left: 0;
    margin-top: 8px;
  }
  
  .el-dialog {
    width: 90% !important;
    max-width: none !important;
  }
  
  /* 移动端表单布局调整 */
  .el-form--inline .el-form-item {
    display: block;
    margin-right: 0;
    width: 100%;
  }
  
  /* 修复底部导航条问题 */
  .app-container {
    padding-bottom: 0 !important;
  }
  
  /* 隐藏底部固定元素 */
  .fixed-bottom-nav,
  .mobile-tab-bar,
  .bottom-actions,
  [class*="bottom-nav"],
  [class*="bottom-bar"],
  [class*="mobile-bar"],
  [class*="floating"],
  [class*="fab-"],
  [class*="footer"],
  div[style*="position:fixed"][style*="bottom:"],
  div[style*="position: fixed"][style*="bottom:"] {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    z-index: -9999 !important;
    height: 0 !important;
    transform: translateY(100%) !important;
  }
  
  /* 避免内容被截断 */
  body {
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
    padding-bottom: 0 !important;
    margin-bottom: 0 !important;
  }
  
  /* 更精确地针对截图中看到的元素 */
  [class*="cursor"] {
    display: none !important;
  }
  
  /* 修复日期选择器问题 */
  .el-picker__popper {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    max-height: 80vh !important;
    overflow-y: auto !important;
    margin: 0 !important;
  }
}

/* 通过ID选择器强力清除 */
#bottom-nav, 
#mobile-actions, 
#footer-bar, 
#fab-container,
#mobile-tab-bar,
#nav-bottom,
#fixed-bottom,
#bottom-toolbar {
  display: none !important;
}

/* 修复日期选择器在桌面端的问题 */
.el-picker-panel, .el-date-picker, .el-date-range-picker {
  z-index: 3000 !important; 
}

/* 确保日期选择器的关闭按钮可点击 */
.el-picker-panel__footer .el-button {
  z-index: 3001 !important;
  position: relative !important;
  pointer-events: auto !important;
}

/* 确保移动端触摸友好 */
@media (hover: none) and (pointer: coarse) {
  /* 增大触摸目标区域 */
  .el-button,
  .el-input,
  .el-date-editor, /* 将 date-editor 也包含进来，确保高度一致 */
  .el-select,
  .el-checkbox,
  .el-radio,
  .el-switch {
    min-height: 44px; /* 苹果推荐的最小触摸目标大小 */
  }
  
  /* 增大间距 */
  .el-menu-item, 
  .el-submenu__title {
    height: 56px;
    line-height: 56px;
  }
}

/* 关键修复：确保日期选择器和按钮在移动端保持一致宽度 */
.el-date-editor.el-input, 
.el-date-editor.el-input__wrapper {
  width: 100% !important;
}

/* 关键修复：确保日期选择器和按钮在移动端保持一致宽度 */
@media (max-width: 768px) {
  .el-date-editor.el-input, 
  .el-date-editor.el-input__wrapper {
    width: 100% !important;
  }
  
  /* 深度修改日期选择器内部元素 */
  .el-date-editor .el-input__wrapper {
    width: 100% !important;
  }
  
  .el-date-editor .el-input__inner {
    width: 100% !important;
  }
  
  /* 强制应用宽度 */
  .budget-date-picker.el-date-editor {
    display: block !important;
    width: 100% !important;
  }
}
</style>
