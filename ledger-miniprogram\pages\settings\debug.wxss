/* pages/settings/debug.wxss */
.debug-page {
  padding: 32rpx;
  background-color: #F2F2F7;
  min-height: 100vh;
}

.debug-section {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.debug-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #000000;
  margin-bottom: 32rpx;
  display: block;
}

.debug-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #E5E5EA;
}

.debug-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.debug-label {
  width: 200rpx;
  font-size: 28rpx;
  color: #666666;
  font-weight: 500;
}

.debug-value {
  flex: 1;
  font-size: 28rpx;
  color: #000000;
  word-break: break-all;
  line-height: 1.4;
}

.debug-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.debug-btn {
  background-color: #007AFF;
  color: white;
  border-radius: 20rpx;
  padding: 24rpx 32rpx;
  font-size: 32rpx;
  border: none;
}

.debug-btn:active {
  background-color: #0056CC;
}
