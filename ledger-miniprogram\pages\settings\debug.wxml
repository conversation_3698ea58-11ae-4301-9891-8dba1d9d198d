<!-- 调试页面 -->
<view class="debug-page">
  <view class="debug-section">
    <text class="debug-title">用户信息调试</text>
    
    <view class="debug-item">
      <text class="debug-label">Token存在:</text>
      <text class="debug-value">{{debugInfo.hasToken ? '是' : '否'}}</text>
    </view>
    
    <view class="debug-item">
      <text class="debug-label">UserInfo存在:</text>
      <text class="debug-value">{{debugInfo.hasUserInfo ? '是' : '否'}}</text>
    </view>
    
    <view class="debug-item">
      <text class="debug-label">用户名:</text>
      <text class="debug-value">{{debugInfo.username || '无'}}</text>
    </view>
    
    <view class="debug-item">
      <text class="debug-label">邮箱:</text>
      <text class="debug-value">{{debugInfo.email || '无'}}</text>
    </view>
    
    <view class="debug-item">
      <text class="debug-label">原始数据:</text>
      <text class="debug-value">{{debugInfo.rawUserInfo}}</text>
    </view>
  </view>
  
  <view class="debug-actions">
    <button class="debug-btn" bindtap="refreshDebugInfo">刷新信息</button>
    <button class="debug-btn" bindtap="testUserInfoFromServer">从服务器获取</button>
    <button class="debug-btn" bindtap="clearUserInfo">清除用户信息</button>
  </view>
</view>
