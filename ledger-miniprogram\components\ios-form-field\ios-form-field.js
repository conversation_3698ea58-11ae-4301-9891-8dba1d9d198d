// components/ios-form-field/ios-form-field.js
Component({
  properties: {
    // 字段类型: input, picker, switch, text
    type: {
      type: String,
      value: 'input'
    },
    // 标签
    label: {
      type: String,
      value: ''
    },
    // 值
    value: {
      type: String,
      value: ''
    },
    // 占位符
    placeholder: {
      type: String,
      value: ''
    },
    // 显示文本（用于picker和text类型）
    displayText: {
      type: String,
      value: ''
    },
    // 输入框类型
    inputType: {
      type: String,
      value: 'text'
    },
    // 最大长度
    maxlength: {
      type: Number,
      value: -1
    },
    // 后缀文本
    suffix: {
      type: String,
      value: ''
    },
    // 输入框样式类
    inputClass: {
      type: String,
      value: ''
    },
    // 开关状态（用于switch类型）
    checked: {
      type: Boolean,
      value: false
    },
    // 是否显示箭头（用于text类型）
    showArrow: {
      type: Boolean,
      value: false
    }
  },

  methods: {
    // 处理输入
    handleInput(e) {
      this.triggerEvent('input', {
        value: e.detail.value
      })
    },

    // 处理焦点
    handleFocus(e) {
      this.triggerEvent('focus', e.detail)
    },

    // 处理失焦
    handleBlur(e) {
      this.triggerEvent('blur', e.detail)
    },

    // 处理选择器点击
    handlePickerTap() {
      this.triggerEvent('pickertap')
    },

    // 处理开关变化
    handleSwitchChange(e) {
      this.triggerEvent('switchchange', {
        checked: e.detail.value
      })
    },

    // 处理文本点击
    handleTextTap() {
      this.triggerEvent('texttap')
    }
  }
})
