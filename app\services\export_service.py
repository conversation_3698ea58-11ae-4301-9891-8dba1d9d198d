from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import text
import logging
import datetime
from typing import Dict, List, Any, Optional
from decimal import Decimal

from app.modules.users.models import User, UserSettings
from app.modules.users.service import UserService
from app.db.base_class import Base

logger = logging.getLogger(__name__)

class ExportService:
    """
    服务类，用于导出和恢复用户数据
    """
    
    async def export_user_data(self, db: AsyncSession, user_id: int) -> Dict[str, Any]:
        """
        导出用户数据，包括用户信息、账户、交易、预算和分类
        """
        try:
            logger.info(f"开始导出用户数据: User ID={user_id}")
            
            # 获取用户信息
            result = await db.execute(select(User).filter(User.id == user_id))
            user = result.scalars().first()
            if not user:
                logger.warning(f"未找到用户: User ID={user_id}")
                return {}
                
            # 获取用户设置
            settings_result = await db.execute(select(UserSettings).filter(UserSettings.user_id == user_id))
            settings = settings_result.scalars().first()
            
            # 获取账户信息
            from app.modules.accounts.models import Account
            accounts_result = await db.execute(select(Account).filter(Account.user_id == user_id))
            accounts = accounts_result.scalars().all()
            
            # 获取分类信息
            from app.modules.categories.models import Category
            categories_result = await db.execute(select(Category).filter(Category.user_id == user_id))
            categories = categories_result.scalars().all()
            
            # 获取预算信息
            from app.modules.budgets.models import Budget
            budgets_result = await db.execute(select(Budget).filter(Budget.user_id == user_id))
            budgets = budgets_result.scalars().all()
            
            # 获取交易信息
            from app.modules.transactions.models import Transaction
            transactions_result = await db.execute(select(Transaction).filter(Transaction.user_id == user_id))
            transactions = transactions_result.scalars().all()
            
            # 构建导出数据
            export_data = {
                "export_date": datetime.datetime.now().isoformat(),
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "phone": user.phone
                },
                "settings": self._model_to_dict(settings) if settings else None,
                "accounts": [self._model_to_dict(account) for account in accounts],
                "categories": [self._model_to_dict(category) for category in categories],
                "budgets": [self._model_to_dict(budget) for budget in budgets],
                "transactions": [self._model_to_dict(transaction) for transaction in transactions]
            }
            
            logger.info(f"用户数据导出成功: User ID={user_id}")
            return export_data
            
        except Exception as e:
            logger.error(f"导出用户数据时出错: {str(e)}")
            raise
    
    async def restore_user_data(self, db: AsyncSession, user_id: int, restore_data: Dict[str, Any]) -> bool:
        """
        从导出的数据中恢复用户数据
        """
        try:
            logger.info(f"开始恢复用户数据: User ID={user_id}")
            
            # 验证数据格式
            if not all(k in restore_data for k in ["accounts", "categories", "budgets", "transactions"]):
                logger.warning("恢复数据格式不正确")
                return False
                
            # 获取用户信息
            result = await db.execute(select(User).filter(User.id == user_id))
            user = result.scalars().first()
            if not user:
                logger.warning(f"未找到用户: User ID={user_id}")
                return False
            
            # 恢复用户设置
            if "settings" in restore_data and restore_data["settings"]:
                await self._restore_settings(db, user_id, restore_data["settings"])
            
            # 恢复账户 - 记录原始ID到新ID的映射
            account_id_map = await self._restore_accounts(db, user_id, restore_data["accounts"])
            
            # 恢复分类 - 记录原始ID到新ID的映射
            category_id_map = await self._restore_categories(db, user_id, restore_data["categories"])
            
            # 恢复预算 - 使用新的分类ID
            await self._restore_budgets(db, user_id, restore_data["budgets"], category_id_map)
            
            # 恢复交易 - 使用新的账户ID和分类ID
            await self._restore_transactions(db, user_id, restore_data["transactions"], 
                                        account_id_map, category_id_map)
            
            logger.info(f"用户数据恢复成功: User ID={user_id}")
            return True
            
        except Exception as e:
            logger.error(f"恢复用户数据时出错: {str(e)}", exc_info=True)
            raise
    
    async def _restore_settings(self, db: AsyncSession, user_id: int, settings_data: Dict[str, Any]) -> None:
        """恢复用户设置"""
        try:
            user_service = UserService()
            
            # 获取现有设置或创建新设置
            settings = await user_service.get_user_settings(db, user_id)
            
            # 更新设置字段
            allowed_fields = ["language", "currency", "dark_mode", "notifications", "auto_backup"]
            for field in allowed_fields:
                if field in settings_data:
                    setattr(settings, field, settings_data[field])
            
            logger.info(f"用户设置恢复成功: User ID={user_id}")
        except Exception as e:
            logger.error(f"恢复用户设置时出错: {str(e)}", exc_info=True)
            raise
    
    async def _restore_accounts(self, db: AsyncSession, user_id: int, accounts_data: List[Dict[str, Any]]) -> Dict[int, int]:
        """
        恢复账户数据
        返回: Dict[原始ID: 新ID]
        """
        try:
            from app.modules.accounts.models import Account
            
            # 创建一个映射表，用于存储原始ID到新ID的映射
            id_mapping = {}
            
            # 删除用户现有账户
            await db.execute(text("DELETE FROM accounts WHERE user_id = :user_id"), {"user_id": user_id})
            
            # 导入账户数据
            for account_data in accounts_data:
                original_id = account_data.get("id")
                
                # 创建新账户对象
                account = Account(
                    account_name=account_data.get("account_name"),
                    account_type=account_data.get("account_type"),
                    current_balance=account_data.get("current_balance", 0),
                    initial_balance=account_data.get("initial_balance", 0),
                    currency=account_data.get("currency", "CNY"),
                    user_id=user_id,
                    is_active=account_data.get("is_active", True)
                )
                db.add(account)
                # 需要立即提交以获取生成的ID
                await db.flush()
                
                # 记录新生成的ID和原始ID的映射关系
                if original_id:
                    id_mapping[original_id] = account.id
            
            logger.info(f"账户数据恢复成功: User ID={user_id}")
            return id_mapping
        except Exception as e:
            logger.error(f"恢复账户数据时出错: {str(e)}", exc_info=True)
            raise
    
    async def _restore_categories(self, db: AsyncSession, user_id: int, categories_data: List[Dict[str, Any]]) -> Dict[int, int]:
        """
        恢复分类数据
        返回: Dict[原始ID: 新ID]
        """
        try:
            from app.modules.categories.models import Category
            
            # 创建一个映射表，用于存储原始ID到新ID的映射
            id_mapping = {}
            
            # 删除用户现有分类
            await db.execute(text("DELETE FROM categories WHERE user_id = :user_id"), {"user_id": user_id})
            
            # 导入分类数据
            for category_data in categories_data:
                original_id = category_data.get("id")
                
                # 创建新分类对象
                category = Category(
                    name=category_data.get("name"),
                    type=category_data.get("type"),
                    color=category_data.get("color", "#1E88E5"),
                    user_id=user_id
                )
                db.add(category)
                # 需要立即提交以获取生成的ID
                await db.flush()
                
                # 记录新生成的ID和原始ID的映射关系
                if original_id:
                    id_mapping[original_id] = category.id
            
            logger.info(f"分类数据恢复成功: User ID={user_id}")
            return id_mapping
        except Exception as e:
            logger.error(f"恢复分类数据时出错: {str(e)}", exc_info=True)
            raise
    
    async def _restore_budgets(self, db: AsyncSession, user_id: int, budgets_data: List[Dict[str, Any]], 
                         category_id_map: Dict[int, int]) -> None:
        """恢复预算数据"""
        try:
            from app.modules.budgets.models import Budget
            
            # 删除用户现有预算
            await db.execute(text("DELETE FROM budgets WHERE user_id = :user_id"), {"user_id": user_id})
            
            # 导入预算数据
            for budget_data in budgets_data:
                # 获取原始分类ID
                original_category_id = budget_data.get("category_id")
                # 使用映射获取新的分类ID
                new_category_id = category_id_map.get(original_category_id) if original_category_id else None
                
                # 创建新预算对象
                if new_category_id:  # 只在能找到映射的分类ID时创建预算
                    budget = Budget(
                        category_id=new_category_id,  # 使用新的分类ID
                        amount=budget_data.get("amount", 0),
                        spent=budget_data.get("spent", 0),
                        year=budget_data.get("year"),
                        month=budget_data.get("month"),
                        user_id=user_id
                    )
                    db.add(budget)
            
            logger.info(f"预算数据恢复成功: User ID={user_id}")
        except Exception as e:
            logger.error(f"恢复预算数据时出错: {str(e)}", exc_info=True)
            raise
    
    async def _restore_transactions(self, db: AsyncSession, user_id: int, transactions_data: List[Dict[str, Any]],
                              account_id_map: Dict[int, int], category_id_map: Dict[int, int]) -> None:
        """恢复交易数据"""
        try:
            from app.modules.transactions.models import Transaction
            
            # 删除用户现有交易
            await db.execute(text("DELETE FROM transactions WHERE user_id = :user_id"), {"user_id": user_id})
            
            # 为了调试目的，记录要恢复的交易数量
            logger.info(f"准备恢复 {len(transactions_data)} 条交易记录")
            
            # 添加显式事务提交确认
            # 导入交易数据
            restored_count = 0
            skipped_count = 0
            for transaction_data in transactions_data:
                try:
                    # 获取原始ID并映射到新ID
                    original_account_id = transaction_data.get("account_id")
                    new_account_id = account_id_map.get(original_account_id) if original_account_id else None
                    
                    original_category_id = transaction_data.get("category_id")
                    new_category_id = category_id_map.get(original_category_id) if original_category_id else None
                    
                    original_from_account_id = transaction_data.get("from_account_id")
                    new_from_account_id = account_id_map.get(original_from_account_id) if original_from_account_id else None
                    
                    original_to_account_id = transaction_data.get("to_account_id")
                    new_to_account_id = account_id_map.get(original_to_account_id) if original_to_account_id else None
                    
                    # 记录交易数据的详细信息，用于调试
                    logger.info(f"交易数据: ID={transaction_data.get('id')}, 类型={transaction_data.get('transaction_type')}, 金额={transaction_data.get('amount')}")
                    logger.info(f"账户映射: 原始account_id={original_account_id} -> 新account_id={new_account_id}")
                    
                    # 对于转账类型的交易，验证来源账户和目标账户
                    if transaction_data.get("transaction_type") == "transfer":
                        logger.info(f"转账映射: 原始from_id={original_from_account_id}->新from_id={new_from_account_id}, 原始to_id={original_to_account_id}->新to_id={new_to_account_id}")
                    
                    # 跳过无法找到对应账户的交易
                    if original_account_id and not new_account_id:
                        logger.warning(f"跳过无法找到对应账户的交易: 原始account_id={original_account_id}, 交易ID={transaction_data.get('id')}")
                        skipped_count += 1
                        continue
                    
                    # 使用直接SQL插入，确保数据正确写入
                    try:
                        transaction_date_str = transaction_data.get("transaction_date")
                        # 确保transaction_date是正确的格式
                        if isinstance(transaction_date_str, str):
                            # 如果是ISO格式的字符串，尝试解析
                            try:
                                from datetime import datetime
                                transaction_date = datetime.fromisoformat(transaction_date_str.replace('Z', '+00:00'))
                            except:
                                # 如果解析失败，使用当前时间
                                transaction_date = datetime.utcnow()
                        else:
                            transaction_date = datetime.utcnow()
                        
                        # 使用参数化查询避免SQL注入
                        insert_stmt = text("""
                            INSERT INTO transactions 
                            (account_id, user_id, category_id, transaction_date, amount, 
                             transaction_type, description, from_account_id, to_account_id, 
                             created_at, updated_at) 
                            VALUES 
                            (:account_id, :user_id, :category_id, :transaction_date, :amount, 
                             :transaction_type, :description, :from_account_id, :to_account_id, 
                             NOW(), NOW())
                        """)
                        
                        await db.execute(
                            insert_stmt, 
                            {
                                "account_id": new_account_id,
                                "user_id": user_id,
                                "category_id": new_category_id,
                                "transaction_date": transaction_date,
                                "amount": transaction_data.get("amount", 0),
                                "transaction_type": transaction_data.get("transaction_type"),
                                "description": transaction_data.get("description", ""),
                                "from_account_id": new_from_account_id,
                                "to_account_id": new_to_account_id
                            }
                        )
                        
                        restored_count += 1
                        logger.info(f"交易记录恢复成功: ID={transaction_data.get('id')}")
                    except Exception as e:
                        logger.error(f"SQL插入交易记录失败: {str(e)}, 交易数据: {transaction_data}")
                        skipped_count += 1
                except Exception as e:
                    logger.error(f"处理交易记录时出错: {str(e)}, 交易数据: {transaction_data}")
                    skipped_count += 1
                    continue
                    
            # 确保显式提交到数据库
            await db.commit()
            
            # 执行验证查询，确认数据已保存
            verify_query = text("SELECT COUNT(*) FROM transactions WHERE user_id = :user_id")
            result = await db.execute(verify_query, {"user_id": user_id})
            actual_count = result.scalar()
            
            logger.info(f"交易数据恢复验证: 预期恢复{restored_count}条记录, 实际数据库中有{actual_count}条记录")
            logger.info(f"交易数据恢复完成: User ID={user_id}, 恢复数量={restored_count}, 跳过数量={skipped_count}")
        except Exception as e:
            logger.error(f"恢复交易数据时出错: {str(e)}", exc_info=True)
            await db.rollback()  # 显式回滚以防止事务悬挂
            raise
    
    def _model_to_dict(self, model: Optional[Base]) -> Dict[str, Any]:
        """将SQLAlchemy模型转换为字典"""
        if model is None:
            return {}

        data = {}
        for column in model.__table__.columns:
            column_name = column.name
            value = getattr(model, column_name)

            # 特殊处理日期时间类型
            if isinstance(value, (datetime.datetime, datetime.date)):
                value = value.isoformat()
            # 特殊处理Decimal类型
            elif isinstance(value, Decimal):
                value = float(value)

            data[column_name] = value

        return data