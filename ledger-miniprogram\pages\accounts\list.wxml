<!-- 账户管理页面 -->
<view class="accounts-page">
  <!-- 净资产卡片 -->
  <view class="total-assets-card">
    <view class="assets-header">
      <view class="assets-title-row">
        <text class="assets-title">净资产</text>
        <text class="eye-toggle" bindtap="toggleAmountVisibility">{{hideAmounts ? '👁️' : '🙈'}}</text>
      </view>
      <text class="assets-amount">{{hideAmounts ? '****' : '¥' + totalAssets}}</text>
    </view>
    <view class="assets-breakdown">
      <view class="breakdown-item">
        <text class="breakdown-label">银行</text>
        <text class="breakdown-value">{{hideAmounts ? '****' : '¥' + totalBank}}</text>
      </view>
      <view class="breakdown-item">
        <text class="breakdown-label">投资</text>
        <text class="breakdown-value">{{hideAmounts ? '****' : '¥' + totalInvestment}}</text>
      </view>
      <view class="breakdown-item">
        <text class="breakdown-label">负债</text>
        <text class="breakdown-value debt">{{hideAmounts ? '****' : '-¥' + totalDebt}}</text>
      </view>
    </view>
  </view>

  <!-- 账户列表 -->
  <view class="accounts-list">
    <view class="list-header">
      <text class="list-title">我的账户</text>
      <text class="account-count">{{accounts.length}}个账户</text>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <view class="loading-accounts">
        <view class="loading-account-item" wx:for="{{[1,2,3]}}" wx:key="*this">
          <view class="loading-icon"></view>
          <view class="loading-content">
            <view class="loading-line short"></view>
            <view class="loading-line long"></view>
          </view>
          <view class="loading-balance"></view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:elif="{{accounts.length === 0}}" class="empty-state">
      <text class="empty-icon">💳</text>
      <text class="empty-text">暂无账户</text>
      <text class="empty-hint">添加您的第一个账户开始记账</text>
      <button class="btn-primary" bindtap="addAccount">添加账户</button>
    </view>

    <!-- 账户列表 -->
    <view wx:else class="accounts-container">
      <view class="account-item" wx:for="{{accounts}}" wx:key="id" bindtap="viewAccountDetail" data-id="{{item.id}}">
        <view class="account-icon">
          <text class="icon-text">{{item.icon}}</text>
        </view>
        <view class="account-info">
          <text class="account-name">{{item.account_name}}</text>
          <text class="account-type">{{item.type_display}}</text>
          <text wx:if="{{item.institution}}" class="account-institution">{{item.institution}}</text>
        </view>
        <view class="account-balance">
          <text class="balance-amount">{{hideAmounts ? '****' : '¥' + item.balanceText}}</text>
          <text class="balance-arrow">></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 添加按钮 -->
  <view class="fab" bindtap="addAccount">
    <text class="fab-icon">+</text>
  </view>
</view>
