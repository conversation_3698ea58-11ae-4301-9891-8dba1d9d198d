<!-- iOS风格选择器组件 -->
<view wx:if="{{visible}}" class="ios-picker-overlay" bindtap="handleOverlayClick">
  <view class="ios-picker-modal" catchtap="stopPropagation">
    <!-- 选择器头部 -->
    <view class="ios-picker-header">
      <button class="ios-picker-btn" bindtap="handleCancel">取消</button>
      <text class="ios-picker-title">{{title}}</text>
      <button class="ios-picker-btn confirm" bindtap="handleConfirm">完成</button>
    </view>
    
    <!-- 选择器内容 -->
    <view class="ios-picker-content">
      <view 
        class="ios-picker-item {{item[valueKey] === selectedValue ? 'active' : ''}}"
        wx:for="{{options}}" 
        wx:key="{{valueKey}}"
        bindtap="selectItem"
        data-value="{{item[valueKey]}}"
        data-index="{{index}}"
      >
        <!-- 图标 -->
        <text wx:if="{{item[iconKey]}}" class="picker-icon">{{item[iconKey]}}</text>
        
        <!-- 标签 -->
        <text class="picker-label">{{item[labelKey]}}</text>
        
        <!-- 选中标记 -->
        <text wx:if="{{item[valueKey] === selectedValue}}" class="picker-check">✓</text>
      </view>
    </view>
  </view>
</view>
