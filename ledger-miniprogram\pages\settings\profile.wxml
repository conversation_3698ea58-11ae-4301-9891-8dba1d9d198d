<!-- 个人信息页面 -->
<view class="profile-page">
  <!-- 头像区域 -->
  <view class="avatar-section">
    <view class="avatar-container">
      <view class="user-avatar">
        <text class="avatar-text">{{userInfo.username ? userInfo.username.charAt(0).toUpperCase() : 'U'}}</text>
      </view>
      <text class="change-avatar-btn" bindtap="changeAvatar">更换头像</text>
    </view>
  </view>

  <!-- 个人信息表单 -->
  <view class="form-section">
    <view class="form-group">
      <view class="form-label">用户名</view>
      <input 
        class="form-input" 
        value="{{userInfo.username}}" 
        placeholder="请输入用户名"
        bindinput="onUsernameInput"
        disabled="{{!editMode}}"
      />
    </view>

    <view class="form-group">
      <view class="form-label">邮箱</view>
      <input 
        class="form-input" 
        value="{{userInfo.email}}" 
        placeholder="请输入邮箱"
        bindinput="onEmailInput"
        disabled="{{!editMode}}"
      />
    </view>

    <view class="form-group">
      <view class="form-label">手机号</view>
      <input 
        class="form-input" 
        value="{{userInfo.phone || ''}}" 
        placeholder="请输入手机号"
        bindinput="onPhoneInput"
        disabled="{{!editMode}}"
      />
    </view>

    <view class="form-group">
      <view class="form-label">注册时间</view>
      <view class="form-value">{{formatDate(userInfo.created_at)}}</view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button 
      wx:if="{{!editMode}}" 
      class="btn-primary edit-btn" 
      bindtap="enterEditMode"
    >
      编辑资料
    </button>
    
    <view wx:else class="edit-actions">
      <button class="btn-secondary cancel-btn" bindtap="cancelEdit">取消</button>
      <button class="btn-primary save-btn" bindtap="saveProfile" loading="{{saving}}">
        {{saving ? '保存中...' : '保存'}}
      </button>
    </view>
  </view>

  <!-- 修改密码 -->
  <view class="password-section">
    <view class="section-title">安全设置</view>
    <view class="list-item" bindtap="changePassword">
      <text class="cell-icon">🔒</text>
      <text class="cell-title">修改密码</text>
      <text class="cell-arrow">></text>
    </view>
  </view>
</view>
