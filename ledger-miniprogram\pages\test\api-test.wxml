<!-- API测试页面 -->
<view class="test-page">
  <view class="test-header">
    <text class="test-title">API测试页面</text>
  </view>

  <view class="test-section">
    <text class="section-title">账户API测试</text>
    
    <button class="test-btn" bindtap="testCreateAccount">测试创建账户</button>
    <button class="test-btn" bindtap="testGetAccounts">测试获取账户列表</button>
    <button class="test-btn" bindtap="testGetDashboard">测试获取仪表盘</button>
  </view>

  <view class="test-logs">
    <text class="logs-title">测试日志：</text>
    <view class="log-item" wx:for="{{logs}}" wx:key="index">
      <text class="log-text">{{item}}</text>
    </view>
  </view>
</view>
