from typing import List, Optional, Dict
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_

from app.modules.categories.models import Category

async def get_category(
    db: AsyncSession, category_id: int, user_id: int
) -> Optional[Category]:
    """获取单个分类"""
    # 查询是用户自己的分类，或是系统默认分类
    query = select(Category).where(
        and_(
            Category.id == category_id,
            or_(
                Category.user_id == user_id,
                Category.user_id.is_(None)
            )
        )
    )
    result = await db.execute(query)
    return result.scalar_one_or_none()

async def get_categories_by_user(
    db: AsyncSession, user_id: int
) -> List[Category]:
    """获取用户的所有分类（包括系统默认分类）"""
    query = select(Category).where(
        or_(
            Category.user_id == user_id,
            Category.user_id.is_(None)
        )
    )
    result = await db.execute(query)
    return result.scalars().all()

async def create_category(
    db: AsyncSession, name: str, category_type: str, color: str, icon: str, user_id: int
) -> Category:
    """创建新分类"""
    category = Category(
        name=name, type=category_type, color=color, icon=icon, user_id=user_id
    )
    db.add(category)
    await db.commit()

    await db.refresh(category)
    return category

async def update_category(
    db: AsyncSession, category: Category, name: Optional[str] = None, 
    color: Optional[str] = None, icon: Optional[str] = None
) -> Category:
    """更新分类"""
    if name is not None:
        category.name = name
    if color is not None:
        category.color = color
    if icon is not None:
        category.icon = icon
        
    await db.commit()
    await db.refresh(category)
    return category

async def delete_category(
    db: AsyncSession, category: Category
) -> None:
    """删除分类"""
    await db.delete(category)
    await db.commit() 