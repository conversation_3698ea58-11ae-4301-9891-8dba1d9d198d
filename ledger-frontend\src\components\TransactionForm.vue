<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import {
  ElMessage,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElDatePicker,
  ElIcon
} from 'element-plus'
import { createTransaction } from '@/api/transaction'
import { getAccounts } from '@/api/account'
import type { Account, TransactionCreateParams } from '@/api/types'
import { useResponsive } from '@/plugins/useResponsive'
import MobileDatetimePicker from '@/components/MobileDatetimePicker.vue'
import {
  Money,
  Calendar,
  User,
  Document,
  ArrowRight,
  ArrowLeft,
  Plus,
  Minus,
  Switch
} from '@element-plus/icons-vue'

// 组件属性
interface Props {
  visible: boolean
  accountId?: number
}

const props = defineProps<Props>()

// 事件
const emit = defineEmits(['close', 'success'])

// 响应式状态
const formRef = ref()
const loading = ref(false)
const submitting = ref(false)
const accounts = ref<Account[]>([])

// 响应式工具
const { isMobile, isTablet, isDesktop } = useResponsive()

  // 格式化当前时间为标准格式
const formatCurrentTime = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 表单数据
const form = reactive({
  date: formatCurrentTime(), // 默认为当前时间的标准格式
  type: 'expense',
  amount: 0,
  category: '',
  description: '',
  account_id: props.accountId || null as number | null,
})

// 表单验证规则
const rules = {
  type: [{ required: true, message: '请选择交易类型', trigger: 'change' }],
  amount: [
    { required: true, message: '请输入金额', trigger: 'blur' },
    { type: 'number' as const, min: 0.01, message: '金额必须大于0', trigger: 'blur' }
  ],
  date: [{ required: true, message: '请选择日期', trigger: 'change' }],
  account_id: [{ required: true, message: '请选择账户', trigger: 'change' }],
}

// 交易类型选项
const transactionTypes = [
  { value: 'expense', label: '支出', icon: 'minus', color: '#f56565' },
  { value: 'income', label: '收入', icon: 'plus', color: '#48bb78' },
  { value: 'transfer', label: '转账', icon: 'switch', color: '#4299e1' }
]

// 获取交易类型图标
const getTypeIcon = (type: string) => {
  switch (type) {
    case 'expense': return 'el-icon-minus'
    case 'income': return 'el-icon-plus'
    case 'transfer': return 'el-icon-sort'
    default: return 'el-icon-money'
  }
}

// 获取账户类型标签
const getAccountTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'checking': '储蓄',
    'savings': '储蓄',
    'credit': '信用卡',
    'investment': '投资',
    'cash': '现金',
    'debt': '负债'
  }
  return typeMap[type] || type
}

// 验证金额输入
const validateAmount = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = parseFloat(target.value)
  if (value < 0.01) {
    target.value = '0.01'
    form.amount = 0.01
  } else {
    form.amount = value
  }
}

// 分类选项
const categories = {
  expense: [
    '饮食', '购物', '交通', '住宿', '娱乐', '医疗', '教育', '其他支出'
  ],
  income: [
    '工资', '奖金', '投资收益', '其他收入'
  ],
  transfer: [
    '账户转账'
  ]
}

// 根据交易类型获取分类选项
const categoryOptions = computed(() => {
  return categories[form.type as keyof typeof categories] || []
})

// 获取账户列表
const fetchAccounts = async () => {
  try {
    loading.value = true
    accounts.value = await getAccounts()
  } catch (error) {
    console.error('获取账户列表失败', error)
    ElMessage.error('获取账户列表失败')
  } finally {
    loading.value = false
  }
}

// 获取账户信息
const getSelectedAccount = computed(() => {
  if (!form.account_id) return null
  return accounts.value.find(acc => acc.id === form.account_id)
})

// 判断是否为负债账户
const isDebtAccount = computed(() => {
  const account = getSelectedAccount.value
  return account && account.account_type === 'debt'
})

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        submitting.value = true
        
        let finalType = form.type
        let finalAmount = form.amount
        
        // 移除前端的负债账户特殊处理，让后端统一处理
        // 所有账户类型都使用相同的金额处理逻辑
        
        // 构造交易数据
        const transactionData: TransactionCreateParams = {
          date: typeof form.date === 'string' ? form.date.split(' ')[0] : form.date,
          type: finalType,
          amount: finalAmount,
          category: form.category,
          account_id: form.account_id!,
          description: form.description,
          // 添加明确的transaction_type字段以避免后端混淆
          transaction_type: finalType
        }
        
        console.log('提交交易数据:', transactionData)
        
        // 发送API请求
        await createTransaction(transactionData)
        
        ElMessage.success('交易记录添加成功')
        
        // 重置表单
        resetForm()
        
        // 发送成功事件
        emit('success')
      } catch (error: any) {
        console.error('添加交易记录失败', error)
        // 显示更详细的错误信息
        if (error.response && error.response.data) {
          ElMessage.error(`添加交易记录失败: ${error.response.data.detail || error.message || '未知错误'}`)
        } else {
          ElMessage.error('添加交易记录失败，请重试')
        }
      } finally {
        submitting.value = false
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  form.date = formatCurrentTime() // 使用格式化的当前时间
  form.type = 'expense'
  form.amount = 0
  form.category = ''
  form.description = ''
}

// 关闭对话框
const handleClose = () => {
  resetForm()
  emit('close')
}

// 获取账户列表
fetchAccounts()
</script>

<template>
  <div class="transaction-form-container" :class="{ 'mobile': isMobile }">
    <!-- 移动端现代化表单 -->
    <div v-if="isMobile" class="mobile-form">
      <!-- 交易类型选择 - 卡片式 -->
      <div class="form-section">
        <div class="section-title">
          <el-icon class="section-icon"><Switch /></el-icon>
          交易类型
        </div>
        <div class="type-selector">
          <div
            v-for="type in transactionTypes"
            :key="type.value"
            class="type-card"
            :class="{ 'active': form.type === type.value }"
            @click="form.type = type.value"
          >
            <div class="type-icon" :style="{ backgroundColor: type.color }">
              <el-icon>
                <Plus v-if="type.value === 'income'" />
                <Minus v-else-if="type.value === 'expense'" />
                <ArrowRight v-else />
              </el-icon>
            </div>
            <span class="type-label">{{ type.label }}</span>
          </div>
        </div>
      </div>

      <!-- 金额输入 - 大号显示 -->
      <div class="form-section">
        <div class="section-title">
          <el-icon class="section-icon"><Money /></el-icon>
          金额
        </div>
        <div class="amount-input-container">
          <span class="currency-symbol">¥</span>
          <input
            v-model="form.amount"
            type="number"
            step="0.01"
            min="0.01"
            placeholder="0.00"
            class="amount-input"
            @input="validateAmount"
          />
        </div>
        <div class="amount-tips">
          <span class="tip-text">{{ form.type === 'expense' ? '支出金额' : form.type === 'income' ? '收入金额' : '转账金额' }}</span>
        </div>
      </div>

      <!-- 账户和分类选择 -->
      <div class="form-section">
        <div class="form-row">
          <div class="form-col">
            <div class="field-label">
              <el-icon><User /></el-icon>
              账户
            </div>
            <el-select
              v-model="form.account_id"
              placeholder="选择账户"
              class="mobile-select"
              :disabled="props.accountId !== undefined"
            >
              <el-option
                v-for="account in accounts"
                :key="account.id"
                :label="account.account_name"
                :value="account.id"
              >
                <div class="account-option">
                  <span class="account-name">{{ account.account_name }}</span>
                  <span class="account-type">{{ getAccountTypeLabel(account.account_type) }}</span>
                </div>
              </el-option>
            </el-select>
          </div>

          <div class="form-col">
            <div class="field-label">
              <el-icon><Document /></el-icon>
              分类
            </div>
            <el-select
              v-model="form.category"
              placeholder="选择分类"
              class="mobile-select"
            >
              <el-option
                v-for="category in categoryOptions"
                :key="category"
                :label="category"
                :value="category"
              />
            </el-select>
          </div>
        </div>
      </div>

      <!-- 日期时间选择 -->
      <div class="form-section">
        <div class="field-label">
          <el-icon><Calendar /></el-icon>
          日期时间
        </div>
        <MobileDatetimePicker
          v-model="form.date"
          placeholder="选择交易日期和时间"
          class="mobile-datetime-picker"
        />
      </div>

      <!-- 备注输入 -->
      <div class="form-section">
        <div class="field-label">
          <el-icon><Document /></el-icon>
          备注 <span class="optional">(可选)</span>
        </div>
        <textarea
          v-model="form.description"
          placeholder="添加备注信息..."
          class="mobile-textarea"
          maxlength="255"
          rows="3"
        ></textarea>
        <div class="char-count">{{ form.description.length }}/255</div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="mobile-actions">
        <button class="action-btn cancel-btn" @click="handleClose">
          <span>取消</span>
        </button>
        <button
          class="action-btn confirm-btn"
          @click="submitForm"
          :disabled="submitting"
          :class="{ 'loading': submitting }"
        >
          <el-icon v-if="submitting" class="loading-icon"><ArrowRight /></el-icon>
          <span>{{ submitting ? '保存中...' : '保存交易' }}</span>
        </button>
      </div>
    </div>

    <!-- 桌面端表单 -->
    <el-form
      v-else
      ref="formRef"
      :model="form"
      :rules="rules"
      label-position="top"
      v-loading="loading"
      class="desktop-form"
    >
      <el-form-item label="交易类型" prop="type">
        <el-select v-model="form.type" style="width: 100%">
          <el-option
            v-for="item in transactionTypes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="交易日期" prop="date">
        <el-date-picker
          v-model="form.date"
          type="datetime"
          placeholder="选择日期和时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="金额" prop="amount">
        <el-input
          v-model.number="form.amount"
          type="number"
          placeholder="请输入金额"
          :step="0.01"
        />
      </el-form-item>

      <el-form-item label="分类" prop="category">
        <el-select v-model="form.category" placeholder="选择分类" style="width: 100%">
          <el-option
            v-for="category in categoryOptions"
            :key="category"
            :label="category"
            :value="category"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="账户" prop="account_id">
        <el-select v-model="form.account_id" placeholder="选择账户" style="width: 100%" :disabled="props.accountId !== undefined">
          <el-option
            v-for="account in accounts"
            :key="account.id"
            :label="account.account_name"
            :value="account.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="2"
          placeholder="请输入交易备注(选填)"
        />
      </el-form-item>

      <div class="form-actions">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="submitForm"
          :loading="submitting"
        >
          保存
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.transaction-form-container {
  width: 100%;

  &.mobile {
    padding: 0;
  }
}

/* 移动端现代化样式 */
.mobile-form {
  padding: 0;

  .form-section {
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #1a1a1a;
      margin-bottom: 12px;

      .section-icon {
        margin-right: 8px;
        color: #007AFF;
      }
    }

    .field-label {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 8px;

      .el-icon {
        margin-right: 6px;
        color: #666;
        font-size: 16px;
      }

      .optional {
        color: #999;
        font-weight: 400;
        margin-left: 4px;
      }
    }
  }

  /* 交易类型选择器 */
  .type-selector {
    display: flex;
    gap: 12px;

    .type-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px 12px;
      background: #f8f9fa;
      border: 2px solid transparent;
      border-radius: 16px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
      }

      &.active {
        background: #e3f2fd;
        border-color: #007AFF;

        .type-icon {
          transform: scale(1.1);
        }

        .type-label {
          color: #007AFF;
          font-weight: 600;
        }
      }

      .type-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        transition: all 0.3s ease;

        .el-icon {
          font-size: 20px;
          color: white;
        }
      }

      .type-label {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        transition: all 0.3s ease;
      }
    }
  }

  /* 金额输入 */
  .amount-input-container {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 16px;
    padding: 16px 20px;
    transition: all 0.3s ease;

    &:focus-within {
      border-color: #007AFF;
      background: #fff;
      box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
    }

    .currency-symbol {
      font-size: 24px;
      font-weight: 600;
      color: #007AFF;
      margin-right: 8px;
    }

    .amount-input {
      flex: 1;
      border: none;
      background: transparent;
      font-size: 28px;
      font-weight: 600;
      color: #1a1a1a;
      outline: none;

      &::placeholder {
        color: #999;
      }

      /* 移除数字输入框的箭头 */
      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      &[type=number] {
        -moz-appearance: textfield;
      }
    }
  }

  .amount-tips {
    margin-top: 8px;
    text-align: center;

    .tip-text {
      font-size: 12px;
      color: #666;
    }
  }

  /* 表单行布局 */
  .form-row {
    display: flex;
    gap: 12px;

    .form-col {
      flex: 1;
    }
  }

  /* 移动端选择器 */
  .mobile-select {
    width: 100%;

    :deep(.el-input__wrapper) {
      background: #f8f9fa;
      border: 2px solid #e9ecef;
      border-radius: 12px;
      padding: 12px 16px;
      transition: all 0.3s ease;
      box-shadow: none;

      &:hover {
        border-color: #007AFF;
      }

      &.is-focus {
        border-color: #007AFF;
        background: #fff;
        box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
      }
    }

    :deep(.el-input__inner) {
      font-size: 15px;
      color: #1a1a1a;
      height: auto;
      line-height: 1.4;
    }
  }

  /* 账户选项样式 */
  .account-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .account-name {
      font-weight: 500;
      color: #1a1a1a;
    }

    .account-type {
      font-size: 12px;
      color: #666;
      background: #f0f0f0;
      padding: 2px 8px;
      border-radius: 8px;
    }
  }

  /* 移动端日期时间选择器 */
  .mobile-datetime-picker {
    :deep(.picker-input) {
      background: #f8f9fa;
      border: 2px solid #e9ecef;
      border-radius: 12px;
      padding: 12px 16px;
      height: auto;
      transition: all 0.3s ease;

      &:active {
        border-color: #007AFF;
        background: #fff;
      }

      .picker-value {
        font-size: 15px;
        color: #1a1a1a;

        &.placeholder {
          color: #999;
        }
      }
    }
  }

  /* 移动端文本域 */
  .mobile-textarea {
    width: 100%;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 15px;
    color: #1a1a1a;
    resize: none;
    outline: none;
    transition: all 0.3s ease;
    font-family: inherit;

    &:focus {
      border-color: #007AFF;
      background: #fff;
      box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
    }

    &::placeholder {
      color: #999;
    }
  }

  .char-count {
    text-align: right;
    font-size: 12px;
    color: #999;
    margin-top: 4px;
  }

  /* 移动端操作按钮 */
  .mobile-actions {
    display: flex;
    gap: 12px;
    padding: 20px 0 8px;

    .action-btn {
      flex: 1;
      height: 52px;
      border: none;
      border-radius: 16px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;

      &:active {
        transform: scale(0.98);
      }

      &.cancel-btn {
        background: #f8f9fa;
        color: #666;

        &:hover {
          background: #e9ecef;
        }
      }

      &.confirm-btn {
        background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);

        &:hover {
          box-shadow: 0 6px 16px rgba(0, 122, 255, 0.4);
        }

        &:disabled {
          background: #ccc;
          color: #999;
          box-shadow: none;
          cursor: not-allowed;
          transform: none;
        }

        &.loading {
          .loading-icon {
            animation: spin 1s linear infinite;
          }
        }
      }
    }
  }
}

/* 桌面端样式 */
.desktop-form {
  .form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    gap: 12px;
    padding: 8px 0;

    .el-button {
      padding: 12px 24px;
      border-radius: 8px;
    }
  }
}

/* 动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 深色模式适配 */
:global(.dark-mode) {
  .mobile-form {
    .section-title {
      color: #e2e2e6;
    }

    .field-label {
      color: #e2e2e6;

      .el-icon {
        color: #a0a0a0;
      }
    }

    .type-card {
      background: #2c2c2e;

      &.active {
        background: #1c3a5e;
      }

      .type-label {
        color: #e2e2e6;
      }
    }

    .amount-input-container {
      background: #2c2c2e;
      border-color: #3a3a3c;

      &:focus-within {
        background: #1c1c1e;
      }

      .amount-input {
        color: #e2e2e6;

        &::placeholder {
          color: #8e8e93;
        }
      }
    }

    .mobile-select {
      :deep(.el-input__wrapper) {
        background: #2c2c2e;
        border-color: #3a3a3c;

        &.is-focus {
          background: #1c1c1e;
        }
      }

      :deep(.el-input__inner) {
        color: #e2e2e6;
      }
    }

    .mobile-datetime-picker {
      :deep(.picker-input) {
        background: #2c2c2e;
        border-color: #3a3a3c;

        &:active {
          background: #1c1c1e;
        }

        .picker-value {
          color: #e2e2e6;
        }
      }
    }

    .mobile-textarea {
      background: #2c2c2e;
      border-color: #3a3a3c;
      color: #e2e2e6;

      &:focus {
        background: #1c1c1e;
      }
    }

    .action-btn {
      &.cancel-btn {
        background: #2c2c2e;
        color: #e2e2e6;

        &:hover {
          background: #3a3a3c;
        }
      }
    }
  }
}
</style>