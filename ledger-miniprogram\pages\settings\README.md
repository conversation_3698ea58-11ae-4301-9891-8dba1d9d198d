# 设置页面功能说明

## 页面结构

### 主设置页面 (`index`)
- **用户信息卡片**: 显示用户头像、用户名、邮箱
- **账户设置**: 个人信息、偏好设置
- **应用设置**: 清除缓存、使用帮助、关于应用
- **退出登录**: 安全退出当前账户

### 个人信息页面 (`profile`)
- **头像管理**: 更换用户头像（功能开发中）
- **基本信息编辑**: 用户名、邮箱、手机号
- **安全设置**: 修改密码（功能开发中）
- **表单验证**: 完整的输入验证和错误提示

### 偏好设置页面 (`preferences`)
- **显示设置**: 深色模式、默认货币、金额精度
- **交易设置**: 快速记账、默认交易类型、记账提醒
- **数据设置**: 自动备份、数据导入导出
- **隐私设置**: 生物识别解锁、隐藏金额
- **云端同步**: 设置数据与服务器同步

### 关于应用页面 (`about`)
- **应用信息**: 版本号、构建信息、开发者信息
- **功能特性**: 主要功能介绍
- **联系方式**: 邮箱、官网、用户群
- **法律信息**: 隐私政策、服务条款、开源许可
- **应用操作**: 检查更新、评分、分享

## 主要功能

### 1. 用户信息管理
- ✅ 显示当前用户信息
- ✅ 编辑用户基本信息
- ✅ 表单验证和错误处理
- ✅ 与后端API同步
- 🚧 头像上传功能
- 🚧 密码修改功能

### 2. 偏好设置
- ✅ 本地设置存储
- ✅ 服务器设置同步
- ✅ 货币选择
- ✅ 深色模式切换
- ✅ 通知设置
- 🚧 生物识别解锁
- 🚧 数据导入导出

### 3. 缓存管理
- ✅ 动态计算缓存大小
- ✅ 智能清除缓存（保留重要数据）
- ✅ 清除确认和进度提示

### 4. 应用信息
- ✅ 版本信息显示
- ✅ 功能特性介绍
- ✅ 联系方式管理
- ✅ 更新检查
- ✅ 应用分享

## 技术特点

### 数据同步
- 本地存储 + 服务器同步
- 离线优先，在线同步
- 错误处理和重试机制

### 用户体验
- 加载状态提示
- 错误信息友好显示
- 操作确认和反馈
- 响应式设计

### 安全性
- 表单输入验证
- 敏感操作确认
- 登录状态检查
- 数据加密存储

## API 接口

### 用户相关
- `GET /users/me` - 获取用户信息
- `PUT /users/me` - 更新用户信息
- `GET /users/settings` - 获取用户设置
- `PUT /users/settings` - 更新用户设置
- `POST /users/logout` - 退出登录

### 数据格式
```javascript
// 用户信息
{
  id: number,
  username: string,
  email: string,
  phone: string,
  created_at: string,
  updated_at: string,
  email_verified: boolean
}

// 用户设置
{
  language: string,
  currency: string,
  dark_mode: boolean,
  notifications: boolean,
  auto_backup: boolean
}
```

## 使用说明

1. **个人信息管理**
   - 点击用户卡片的"编辑"按钮进入个人信息页面
   - 点击"编辑资料"开始编辑
   - 修改完成后点击"保存"同步到服务器

2. **偏好设置**
   - 在设置列表中选择"偏好设置"
   - 调整各项设置选项
   - 点击"保存设置"应用更改

3. **缓存管理**
   - 点击"清除缓存"查看当前缓存大小
   - 确认清除后会保留登录信息和重要设置

4. **应用信息**
   - 查看应用版本和功能介绍
   - 检查更新和联系客服
   - 分享应用给朋友

## 开发状态

- ✅ 已完成
- 🚧 开发中
- ❌ 未开始

当前版本：v1.0.0
最后更新：2024-01-15
