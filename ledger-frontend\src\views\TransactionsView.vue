<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import AppLayout from '../layouts/AppLayout.vue'
import { ElTable, ElTableColumn, ElButton, ElInput, ElSelect, ElOption, ElDatePicker, ElDialog, ElForm, ElFormItem, ElCard, ElMessage, ElMessageBox, ElDivider, ElIcon } from 'element-plus'
import { Food, ShoppingCart, VideoPlay, House, Van, Basketball, Suitcase,
  School, OfficeBuilding, Present, Money, Coin, DataAnalysis, Watch,
  Lightning, Coffee, Cellphone, CreditCard, Bell, Umbrella, Wallet, Headset, Medal, Promotion, Edit, Delete, Plus, Filter, Search, Refresh, Close,
  Switch, User, Document, Calendar, ArrowLeft, ArrowRight, ArrowDown, Minus, Loading, Check } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { getTransactions, createTransaction, updateTransaction, deleteTransaction } from '@/api/transaction'
import { getAccounts } from '@/api/account'
import { getCategories } from '@/api/category'
import type { Category } from '@/api/category'

import type { Transaction, Account } from '@/api/types'
import axios from 'axios'; // 导入axios
import MobileDatetimePicker from '@/components/MobileDatetimePicker.vue'
import MobileDateRangePicker from '@/components/MobileDateRangePicker.vue'
import MobileMonthPicker from '@/components/MobileMonthPicker.vue'
import { useResponsive } from '@/plugins/useResponsive'

// 使用响应式工具检测设备类型
const { isMobile, isTablet } = useResponsive()

// 移动端筛选面板控制
const showMobileFilters = ref(false)

// 滑动手势相关数据
const swipeOffsets = ref<{ [key: number]: number }>({})
const touchStartX = ref(0)
const touchStartTime = ref(0)
const isSwipping = ref(false)
const activeSwipeId = ref<number | null>(null)

// 判断账户是否为负债账户
const isDebtAccount = (accountType?: string): boolean => {
  return accountType === 'debt' || accountType === 'debt_account'
}

// 格式化当前时间为标准格式
const formatCurrentTime = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 交易类型
const transactionTypes = [
  { value: 'income', label: '收入' },
  { value: 'expense', label: '支出' },
  { value: 'transfer', label: '转账' }
]

// 响应式数据
const loading = ref(true);
const accounts = ref<Account[]>([]);
const transactions = ref<Transaction[]>([]);
const accountMap = ref<{ [key: number]: string }>({});
const categories = ref<Category[]>([]);
const categoryMap = ref<{ [key: string]: Category }>({});

// 收入分类 - 现在从API获取
const incomeCategories = computed(() => {
  if (!categories.value || categories.value.length === 0) {
    return [];
  }
  return categories.value.filter(cat => cat.type === 'income').map(cat => ({
    value: cat.name.toLowerCase(),
    label: cat.name,
    icon: cat.icon || 'Money'
  }));
});

// 支出分类 - 现在从API获取
const expenseCategories = computed(() => {
  if (!categories.value || categories.value.length === 0) {
    return [];
  }
  return categories.value.filter(cat => cat.type === 'expense').map(cat => ({
    value: cat.name.toLowerCase(),
    label: cat.name,
    icon: cat.icon || 'ShoppingCart'
  }));
});

// 筛选条件
const filters = reactive({
  dateRange: [] as string[],
  type: '',
  category: '',
  account_id: undefined as number | undefined,  // 不用 null，使用 undefined
  keyword: ''
})

// 新交易对话框
const transactionDialogVisible = ref(false)
const isEditing = ref(false)  // 添加编辑状态标志
const currentTransactionId = ref<number | null>(null)  // 当前编辑的交易ID
const submitting = ref(false)  // 添加提交状态

// 移动端选择器状态
const showAccountPicker = ref(false)
const showCategoryPicker = ref(false)
const showFromAccountPicker = ref(false)
const showToAccountPicker = ref(false)
const showDatePicker = ref(false)
const showTypePicker = ref(false)

// 初始化时就使用格式化的当前时间
const transactionForm = reactive({
  date: formatCurrentTime(), // 使用格式化的当前时间字符串
  type: 'expense',
  amount: 0,
  category: '',
  account_id: undefined as number | undefined, // 使用 undefined 代替 null
  from_account_id: undefined as number | undefined,
  to_account_id: undefined as number | undefined,
  description: ''
})

// 根据交易类型获取可用的分类
const availableCategories = computed(() => {
  let result: Array<{value: string, label: string, icon: string}> = []
  if (transactionForm.type === 'income') {
    result = incomeCategories.value || []
  } else if (transactionForm.type === 'expense') {
    result = expenseCategories.value || []
  }

  // 确保返回的每个项都有必要的属性
  return result.filter(item => item && (item.value || item.label))
})

// 检查选择的账户是否是负债账户
const selectedAccount = computed(() => {
  if (!transactionForm.account_id) return null
  return accounts.value.find(acc => acc.id === transactionForm.account_id)
})

// 是否显示负债账户专用表单
// const showDebtForm = computed(() => {
//   return selectedAccount.value && 
//          (isDebtAccount(selectedAccount.value.account_type) || 
//           selectedAccount.value.account_name === 'abc')
// })

// 在setup函数中添加
const loadData = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const params: any = {};
    
    // 添加日期范围参数
    if (filters.dateRange && Array.isArray(filters.dateRange) && filters.dateRange.length === 2) {
      const startDate = filters.dateRange[0];
      const endDate = filters.dateRange[1];
      
      // 确保日期字符串格式正确
      if (startDate && endDate) {
        params.start_date = `${startDate}T00:00:00`;
        params.end_date = `${endDate}T23:59:59`;
        console.log('筛选日期范围:', params.start_date, '至', params.end_date);
      }
    }
    
    // 添加其他筛选条件
    if (filters.type) {
      params.transaction_type = filters.type;
    }
    
    if (filters.account_id) {
      params.account_id = filters.account_id;
    }
    
    if (filters.keyword) {
      params.keyword = filters.keyword;
    }
    
    console.log('查询参数:', params);
    
    // 并行获取数据
    const [transactionData, accountsData, categoriesData] = await Promise.all([
      getTransactions(params),
      getAccounts(),
      getCategories()
    ]);
    
    console.log('筛选后交易记录数据:', transactionData ? transactionData.length : 0, '条记录');
    transactions.value = transactionData || [];
    
    // 处理账户数据
    accounts.value = accountsData || [];
    accountMap.value = {};
    accounts.value.forEach(account => {
      accountMap.value[account.id] = account.account_name;
    });
    
    // 处理分类数据
    categories.value = categoriesData || [];
    categoryMap.value = {};
    categories.value.forEach(category => {
      categoryMap.value[category.name.toLowerCase()] = category;
      // 也使用原始名称作为键，以备不同的情况
      categoryMap.value[category.name] = category;
    });
    
    console.log('获取到分类数据:', categories.value.length, '个分类');
  } catch (error) {
    console.error('加载数据失败:', error);
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// 获取完整的交易记录，尝试获取包含实际时间的详细数据
const fetchCompleteTransactions = async () => {
  try {
    // 使用原始axios实例，尝试获取完整的交易记录
    const response = await axios.get('/api/v1/transactions', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });
    
    console.log('API返回的交易记录原始数据:', response.data);
    
    // 检查API返回的日期格式字段
    if (response.data && Array.isArray(response.data)) {
      // 处理日期格式，确保使用包含时间的字段
      const processedData = response.data.map((item: any) => {
        // 检查是否有transaction_date字段（可能包含完整时间）
        if (item.transaction_date) {
          console.log(`交易ID ${item.id}: transaction_date = ${item.transaction_date}`);
          // 将transaction_date复制到date字段，确保显示包含时间的日期
          return { ...item, date: item.transaction_date };
        }
        return item;
      });
      
      transactions.value = processedData;
    }
  } catch (error) {
    console.error('获取完整交易记录失败:', error);
  }
};

// 在onMounted中调用loadData
onMounted(() => {
  loadData();
});

// 在表格上方添加刷新按钮的处理函数
const refreshData = () => {
  loadData();
};

// 重置交易表单
const resetTransactionForm = () => {
  // 使用格式化的当前时间字符串，而不是Date对象
  transactionForm.date = formatCurrentTime()
  transactionForm.type = 'expense'
  transactionForm.amount = 0
  transactionForm.category = ''
  transactionForm.account_id = undefined
  transactionForm.from_account_id = undefined
  transactionForm.to_account_id = undefined
  transactionForm.description = ''
}

// 打开添加交易对话框
const openAddTransactionDialog = () => {
  isEditing.value = false
  currentTransactionId.value = null

  // 重置表单并设置默认值 - 使用当前时间的格式化字符串
  resetTransactionForm()
  transactionForm.date = formatCurrentTime() // 确保使用当前时间的格式化字符串作为默认值

  transactionDialogVisible.value = true
}

// 处理遮罩层点击
const handleOverlayClick = () => {
  transactionDialogVisible.value = false
}



// 获取账户类型名称
const getAccountTypeName = (type: string): string => {
  const typeMap: Record<string, string> = {
    'bank': '银行账户',
    'fund': '基金账户',
    'stock': '股票账户',
    'debt': '负债账户',
    'debt_account': '负债账户'
  }
  return typeMap[type] || type
}

// 获取账户类型标签（移动端用）
const getAccountTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'checking': '储蓄',
    'savings': '储蓄',
    'credit': '信用卡',
    'investment': '投资',
    'cash': '现金',
    'debt': '负债',
    'bank': '银行',
    'fund': '基金',
    'stock': '股票'
  }
  return typeMap[type] || type
}

// 获取交易类型颜色
const getTypeColor = (type: string) => {
  switch (type) {
    case 'expense': return '#f56565'
    case 'income': return '#48bb78'
    case 'transfer': return '#4299e1'
    default: return '#666'
  }
}

// 获取金额提示文字
const getAmountTip = () => {
  switch (transactionForm.type) {
    case 'expense': return '支出金额'
    case 'income': return '收入金额'
    case 'transfer': return '转账金额'
    default: return '请输入金额'
  }
}

// 验证金额输入
const validateAmount = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = parseFloat(target.value)
  if (value < 0.01) {
    target.value = '0.01'
    transactionForm.amount = 0.01
  } else {
    transactionForm.amount = value
  }
}

// 自动调整文本框高度
const descriptionTextarea = ref<HTMLTextAreaElement>()

const autoResizeTextarea = () => {
  const textarea = descriptionTextarea.value
  if (textarea) {
    textarea.style.height = 'auto'
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px' // 最大高度120px
  }
}

// 图标映射
const iconComponents = {
  'Food': Food,
  'ShoppingCart': ShoppingCart,
  'VideoPlay': VideoPlay,
  'House': House,
  'Van': Van,
  'Basketball': Basketball,
  'Suitcase': Suitcase,
  'School': School,
  'OfficeBuilding': OfficeBuilding,
  'Present': Present,
  'Money': Money,
  'Coin': Coin,
  'DataAnalysis': DataAnalysis,
  'Lightning': Lightning,
  'Coffee': Coffee,
  'Cellphone': Cellphone,
  'CreditCard': CreditCard,
  'Watch': Watch,
  'Promotion': Promotion,
  'Bell': Bell,
  'Umbrella': Umbrella,
  'Wallet': Wallet,
  'Headset': Headset,
  'Medal': Medal
}

// 获取图标组件
const getIconComponent = (iconName: string) => {
  return iconComponents[iconName as keyof typeof iconComponents] || null
}

// 计算属性：获取选中的显示名称
const selectedAccountName = computed(() => {
  if (!transactionForm.account_id) return ''
  const account = accounts.value.find(acc => acc.id === transactionForm.account_id)
  return account ? account.account_name : ''
})

const selectedCategoryName = computed(() => {
  if (!transactionForm.category) return ''
  const category = availableCategories.value.find(cat => cat.value === transactionForm.category)
  return category ? category.label : transactionForm.category
})

const selectedFromAccountName = computed(() => {
  if (!transactionForm.from_account_id) return ''
  const account = accounts.value.find(acc => acc.id === transactionForm.from_account_id)
  return account ? account.account_name : ''
})

const selectedToAccountName = computed(() => {
  if (!transactionForm.to_account_id) return ''
  const account = accounts.value.find(acc => acc.id === transactionForm.to_account_id)
  return account ? account.account_name : ''
})

// 格式化显示日期
const formatDisplayDate = (dateStr: string) => {
  if (!dateStr) return '选择日期时间'
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return dateStr
  }
}



// 选择器方法
const selectAccount = (accountId: number) => {
  transactionForm.account_id = accountId
  showAccountPicker.value = false
}

const selectCategory = (categoryValue: string) => {
  transactionForm.category = categoryValue
  showCategoryPicker.value = false
}

const selectFromAccount = (accountId: number) => {
  transactionForm.from_account_id = accountId
  showFromAccountPicker.value = false
}

const selectToAccount = (accountId: number) => {
  transactionForm.to_account_id = accountId
  showToAccountPicker.value = false
}

const selectType = (type: string) => {
  transactionForm.type = type
  transactionForm.category = '' // 重置分类
  showTypePicker.value = false
}

// 获取类型标签
const getTypeLabel = (type: string | undefined) => {
  if (!type) return '请选择类型'
  const typeMap: Record<string, string> = {
    'income': '收入',
    'expense': '支出',
    'transfer': '转账'
  }
  return typeMap[type] || type
}

// 格式化日期时间
const formatDateTime = (dateStr: string | undefined) => {
  if (!dateStr) return '未知日期';
  try {
    const date = new Date(dateStr);
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '日期格式错误';
    }
    return date.toLocaleDateString();
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '日期格式错误';
  }
}

// 获取交易类型标签
const getTransactionTypeLabel = (type: string | undefined): string => {
  if (!type) return '未知';
  const typeMap: Record<string, string> = {
    'income': '收入',
    'expense': '支出',
    'transfer': '转账'
  }
  return typeMap[type] || type
}



// 获取交易分类名称
const getCategoryName = (category: string | undefined, type: string | undefined): string => {
  // 处理转账类型
  if (type === 'transfer') {
    return '转账'
  }
  
  if (!category || category === 'undefined' || category === 'null') {
    // 根据类型返回默认分类名称
    if (type === 'expense') return '支出'
    if (type === 'income') return '收入'
    return '未分类'
  }
  
  // 检查是否是负债账户的特殊分类
  if (category === '还款' || category === '借款' || 
      category === '利息还款' || category === '其他还款' || 
      category === '信用卡消费' || category === '其他借款') {
    return category
  }
  
  // 首先尝试从API获取的分类数据中找到对应的名称
  const categoryData = categoryMap.value[category.toLowerCase()] || categoryMap.value[category];
  if (categoryData) {
    return categoryData.name;
  }
  
  // 如果没有找到，使用备用的分类映射
  const fallbackCategoryMap: Record<string, string> = {
    'food': '餐饮',
    'transport': '交通',
    'shopping': '购物',
    'entertainment': '娱乐',
    'housing': '住房',
    'medical': '医疗',
    'education': '教育',
    'salary': '工资',
    'bonus': '奖金',
    'investment': '投资收益',
    'refund': '退款'
  }
  
  return fallbackCategoryMap[category] || category
}

// 打开编辑交易对话框
const openEditTransactionDialog = (transaction: Transaction) => {
  isEditing.value = true
  currentTransactionId.value = transaction.id
  
  // 填充表单数据
  try {
    // 安全地处理日期
    if (transaction.date) {
      // 直接使用字符串格式，而不是Date对象
      transactionForm.date = transaction.date
    } else {
      transactionForm.date = formatCurrentTime()
    }
  } catch (e) {
    console.error('设置日期时出错:', e)
    transactionForm.date = formatCurrentTime()
  }
  
  transactionForm.type = transaction.type || 'expense'
  transactionForm.amount = Number(transaction.amount)
  transactionForm.category = transaction.category || ''
  
  if (transaction.type === 'transfer') {
    transactionForm.from_account_id = transaction.from_account_id || undefined
    transactionForm.to_account_id = transaction.to_account_id || undefined
  } else {
    transactionForm.account_id = transaction.account_id || undefined
  }
  
  transactionForm.description = transaction.description || ''
  
  // 显示对话框
  transactionDialogVisible.value = true
}

// 保存交易记录（添加或更新）
const saveTransaction = async () => {
  // 基本验证
  if (!transactionForm.type || !transactionForm.amount) {
    ElMessage.warning('请填写必要的交易信息');
    return;
  }
  
  // 确保日期值有效
  if (!transactionForm.date) {
    transactionForm.date = formatCurrentTime();
  }

  try {
    submitting.value = true;
    console.log('保存交易的日期:', transactionForm.date);
    
    // 准备提交数据
    if (transactionForm.type === 'transfer') {
      if (!transactionForm.from_account_id || !transactionForm.to_account_id) {
        ElMessage.warning('请选择转出和转入账户');
        return;
      }

      const transferData = {
        date: typeof transactionForm.date === 'string' ? 
              transactionForm.date : 
              formatCurrentTime(), // 发送完整的日期时间 YYYY-MM-DD HH:MM:SS
        type: transactionForm.type,
        transaction_type: transactionForm.type,
        amount: Number(transactionForm.amount),
        from_account_id: transactionForm.from_account_id,
        account_id: transactionForm.from_account_id,
        description: transactionForm.description || '账户转账'
      };
      
      console.log('提交的交易数据:', transferData);
      
      if (isEditing.value && currentTransactionId.value) {
        // 更新现有交易
        await updateTransaction({ ...transferData, id: currentTransactionId.value });
        ElMessage.success('交易更新成功');
      } else {
        // 创建新交易
        await createTransaction(transferData);
        ElMessage.success('交易添加成功');
      }
    } else {
      // 收入或支出交易
      if (!transactionForm.account_id) {
        ElMessage.warning('请选择账户');
        return;
      }

      // 计算实际提交的金额
      let finalAmount = Number(transactionForm.amount);
      
      // 移除前端的负债账户特殊处理，让后端统一处理
      // 所有账户类型都使用相同的金额处理逻辑

      const transactionData = {
        date: typeof transactionForm.date === 'string' ? 
              transactionForm.date : 
              formatCurrentTime(), // 发送完整的日期时间 YYYY-MM-DD HH:MM:SS
        type: transactionForm.type,
        transaction_type: transactionForm.type,
        amount: finalAmount,
        category: transactionForm.category,
        account_id: transactionForm.account_id,
        description: transactionForm.description || ''
      };
      
      console.log('提交的交易数据:', transactionData);
      
      if (isEditing.value && currentTransactionId.value) {
        // 更新现有交易
        await updateTransaction({ ...transactionData, id: currentTransactionId.value });
        ElMessage.success('交易更新成功');
      } else {
        // 创建新交易
        await createTransaction(transactionData);
        ElMessage.success('交易添加成功');
      }
    }
    
    // 关闭对话框并重新加载数据
    transactionDialogVisible.value = false;
    await loadData();
  } catch (error) {
    console.error('保存交易失败:', error);
    ElMessage.error('保存交易失败，请检查输入并重试');
  } finally {
    submitting.value = false;
  }
}

// 删除交易
const confirmDeleteTransaction = (transaction: Transaction) => {
  // 检查交易类型，确保负债账户交易能正确删除
  const isDebtTransaction = transaction.account_id && 
    accounts.value.find(a => a.id === transaction.account_id && 
    (a.account_type === 'debt' || a.account_name === 'abc'));
  
  let warningMessage = '确定要删除这条交易记录吗？此操作不可恢复。';
  
  if (isDebtTransaction) {
    warningMessage += '\n注意：这是负债账户的交易记录，删除可能会影响账户余额。';
  }
  
  ElMessageBox.confirm(
    warningMessage,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true
    }
  )
    .then(async () => {
      try {
        console.log('准备删除交易记录:', transaction);
        
        // 尝试删除交易
        await deleteTransaction(transaction.id);
        ElMessage.success('交易删除成功');
        
        // 先执行一次页面刷新，确保其他打开的账户页面能更新数据
        window.dispatchEvent(new CustomEvent('refresh-accounts'));
        
        try {
          // 导入账户API方法
          const { getAccounts } = await import('@/api/account');
          // 直接获取最新账户数据（不经过其他组件）
          await getAccounts();
          console.log('已获取最新账户数据');
        } catch (err) {
          console.error('获取最新账户数据失败', err);
        }
        
        // 刷新交易列表
        await loadData();
      } catch (error: any) {
        console.error('删除交易失败:', error);
        
        // 提供更详细的错误信息
        let errorMessage = '删除交易失败';
        
        if (error.response) {
          if (error.response.status === 500) {
            if (error.response.data && typeof error.response.data === 'string' && 
                error.response.data.includes('decimal')) {
              errorMessage = '删除交易失败：数据类型不兼容。此问题可能是由于账户余额计算错误引起的。';
            } else {
              errorMessage = `删除交易失败：服务器内部错误 (${error.response.status})`;
            }
          } else {
            errorMessage = `删除交易失败：${error.response.data?.detail || error.message || '未知错误'}`;
          }
        }
        
        ElMessage({
          message: errorMessage,
          type: 'error',
          duration: 5000
        });
      }
    })
    .catch(() => {
      // 用户取消删除
    });
}

// 格式化日期 - 增加对created_at字段的处理，使用简化格式
const formatDate = (dateStr: string | Date): string => {
  if (!dateStr) return '';
  
  // 如果已经是字符串格式的日期时间，直接返回
  if (typeof dateStr === 'string' && dateStr.includes(':')) {
    return dateStr;
  }
  
  // 否则格式化日期时间
  const date = typeof dateStr === 'string' ? new Date(dateStr) : dateStr;
  
  // 返回完整的日期时间格式 YYYY-MM-DD HH:MM:SS
  return date.toISOString().slice(0, 19).replace('T', ' ');
}

// 新增：格式化日期为 MM.DD 格式
const formatDateShort = (dateStr: string | Date): string => {
  if (!dateStr) return '';
  
  const date = typeof dateStr === 'string' ? new Date(dateStr) : dateStr;
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${month}.${day}`;
}

// 新增：获取交易日期分组标签
const getTransactionDateLabel = (dateStr: string | Date): string => {
  if (!dateStr) return '未知日期';

  const date = typeof dateStr === 'string' ? new Date(dateStr) : dateStr;
  const dateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const today = new Date();
  const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());

  const diffTime = todayOnly.getTime() - dateOnly.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  
  const mmdd = formatDateShort(date);

  if (diffDays === 0) {
    return `${mmdd} 今天`;
  } else if (diffDays === 1) {
    return `${mmdd} 昨天`;
  } else if (diffDays === 2) {
    return `${mmdd} 前天`;
  } else if (diffDays > 2 && diffDays <= 7) {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    return `${mmdd} ${weekdays[date.getDay()]}`;
  } else {
    // 超过7天只显示日期
    return mmdd;
  }
};



// 获取分类图标
const getCategoryIcon = (category: string | undefined, type: string | undefined): string => {
  if (!category) {
    // 根据交易类型返回默认图标
    if (type === 'income') return 'Money'
    if (type === 'expense') return 'ShoppingCart'
    if (type === 'transfer') return 'DataAnalysis'
    return 'Money'
  }
  
  // 从API获取的分类数据中查找图标
  const categoryData = categoryMap.value[category.toLowerCase()] || categoryMap.value[category];
  if (categoryData && categoryData.icon) {
    return categoryData.icon;
  }
  
  // 备用图标映射
  const iconMap: Record<string, string> = {
    'food': 'Food',
    'transport': 'Van',
    'shopping': 'ShoppingCart',
    'entertainment': 'VideoPlay',
    'housing': 'House',
    'medical': 'Bell',
    'education': 'School',
    'salary': 'Money',
    'bonus': 'Present',
    'investment': 'DataAnalysis',
    'refund': 'Coin',
    '餐饮': 'Food',
    '交通': 'Van',
    '购物': 'ShoppingCart',
    '娱乐': 'VideoPlay',
    '住房': 'House',
    '医疗': 'Bell',
    '教育': 'School',
    '工资': 'Money',
    '奖金': 'Present',
    '投资收益': 'DataAnalysis',
    '退款': 'Coin',
    '还款': 'CreditCard',
    '借款': 'Wallet',
    '利息还款': 'CreditCard',
    '其他还款': 'CreditCard',
    '信用卡消费': 'CreditCard',
    '其他借款': 'Wallet'
  }
  
  return iconMap[category] || iconMap[category.toLowerCase()] || 'Money'
}

// 触摸事件处理
const handleTouchStart = (event: TouchEvent, transactionId: number) => {
  if (!isMobile.value) return;
  
  touchStartX.value = event.touches[0].clientX;
  touchStartTime.value = Date.now();
  isSwipping.value = true;
  
  console.log('Touch start:', transactionId, touchStartX.value);
}

const handleTouchMove = (event: TouchEvent, transactionId: number) => {
  if (!isMobile.value || !isSwipping.value) return;
  
  event.preventDefault(); // 防止页面滚动
  
  const currentX = event.touches[0].clientX;
  const deltaX = touchStartX.value - currentX;
  
  // 移动端按钮宽度为140px
  const maxSwipeDistance = 140;
  
  // 只允许向左滑动，但在滑动过程中允许超出最大距离，提供阻尼效果
  if (deltaX > 0) {
    if (deltaX <= maxSwipeDistance) {
      // 在正常范围内，直接使用实际距离
      swipeOffsets.value[transactionId] = deltaX;
    } else {
      // 超出范围时，添加阻尼效果，让滑动变慢但不完全停止
      const excess = deltaX - maxSwipeDistance;
      const dampingFactor = 0.3; // 阻尼系数，越小阻力越大
      swipeOffsets.value[transactionId] = maxSwipeDistance + (excess * dampingFactor);
    }
  } else {
    swipeOffsets.value[transactionId] = 0;
  }
  
  console.log('Touch move:', transactionId, deltaX, swipeOffsets.value[transactionId]);
}

const handleTouchEnd = (event: TouchEvent, transactionId: number) => {
  if (!isMobile.value || !isSwipping.value) return;
  
  const touchEndTime = Date.now();
  const touchDuration = touchEndTime - touchStartTime.value;
  const currentOffset = swipeOffsets.value[transactionId] || 0;
  
  // 移动端按钮宽度为140px
  const maxSwipeDistance = 140;
  
  // 计算实际的滑动距离（不包括阻尼效果）
  const actualDeltaX = currentOffset > maxSwipeDistance ? 
    maxSwipeDistance + (currentOffset - maxSwipeDistance) / 0.3 : currentOffset;
  
  console.log('Touch end:', transactionId, actualDeltaX, touchDuration, 'currentOffset:', currentOffset);
  
  // 如果滑动距离超过70px或者快速滑动，则完全展开
  if (actualDeltaX > 70 || (actualDeltaX > 20 && touchDuration < 300)) {
    swipeOffsets.value[transactionId] = maxSwipeDistance;
    activeSwipeId.value = transactionId;
    
    console.log('Swipe activated:', transactionId);
    
    // 关闭其他已打开的滑动项
    Object.keys(swipeOffsets.value).forEach(key => {
      const id = parseInt(key);
      if (id !== transactionId) {
        swipeOffsets.value[id] = 0;
      }
    });
  } else {
    swipeOffsets.value[transactionId] = 0;
    if (activeSwipeId.value === transactionId) {
      activeSwipeId.value = null;
    }
    console.log('Swipe cancelled:', transactionId);
  }
  
  isSwipping.value = false;
}

// 点击其他地方关闭滑动
const handleTransactionClick = (transaction: Transaction) => {
  // 如果有打开的滑动项，先关闭它
  if (activeSwipeId.value !== null) {
    swipeOffsets.value[activeSwipeId.value] = 0;
    activeSwipeId.value = null;
    return;
  }
}

// 全局点击处理，关闭所有滑动状态
const handleGlobalClick = () => {
  if (activeSwipeId.value !== null) {
    swipeOffsets.value[activeSwipeId.value] = 0;
    activeSwipeId.value = null;
  }
}

// 判断交易项是否处于滑动状态
const isTransactionSwiped = (transactionId: number): boolean => {
  return (swipeOffsets.value[transactionId] || 0) >= 70;
}

// 应用筛选
const filteredTransactions = computed(() => {
  return transactions.value.filter(transaction => {
    // 日期范围筛选
    if (filters.dateRange && filters.dateRange.length === 2) {
      const transactionDate = new Date(transaction.date || '')
      const startDate = new Date(filters.dateRange[0])
      const endDate = new Date(filters.dateRange[1])
      
      if (transactionDate < startDate || transactionDate > endDate) {
        return false
      }
    }
    
    // 交易类型筛选
    if (filters.type && transaction.type !== filters.type) {
      return false
    }
    
    // 分类筛选
    if (filters.category && transaction.category !== filters.category) {
      return false
    }
    
    // 账户筛选
    if (filters.account_id) {
      if (transaction.type === 'transfer') {
        if (transaction.from_account_id !== filters.account_id && transaction.to_account_id !== filters.account_id) {
          return false
        }
      } else {
        if (transaction.account_id !== filters.account_id) {
          return false
        }
      }
    }
    
    // 关键词筛选
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase()
      const desc = (transaction.description || '').toLowerCase()
      if (!desc.includes(keyword)) {
        return false
      }
    }
    
    return true
  })
})

// 新增：按日期分组的计算属性
const groupedTransactions = computed(() => {
  if (!filteredTransactions.value || filteredTransactions.value.length === 0) {
    return [];
  }

  const groups: { [key: string]: { dateLabel: string; dailyTotal: { income: number; expense: number }; transactions: Transaction[] } } = {};

  const sortedTransactions = [...filteredTransactions.value].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

  for (const transaction of sortedTransactions) {
    const dateLabel = getTransactionDateLabel(transaction.date);
    if (!groups[dateLabel]) {
      groups[dateLabel] = {
        dateLabel: dateLabel,
        dailyTotal: { income: 0, expense: 0 },
        transactions: [],
      };
    }
    
    groups[dateLabel].transactions.push(transaction);
    if (transaction.type === 'income') {
        groups[dateLabel].dailyTotal.income += Number(transaction.amount);
    } else if (transaction.type === 'expense') {
        groups[dateLabel].dailyTotal.expense += Number(transaction.amount);
    }
  }
  
  return Object.values(groups);
});


// 重置筛选条件
const resetFilters = () => {
  filters.dateRange = []
  filters.type = ''
  filters.category = ''
  filters.account_id = undefined  // 使用 undefined 代替 null
  filters.keyword = ''
}

// 添加筛选函数
const applyFilter = () => {
  console.log('应用筛选条件:', filters);
  loadData();
}

// 重置筛选
const resetFilter = () => {
  filters.dateRange = [];
  filters.type = '';
  filters.account_id = undefined;  // 使用 undefined 代替 null
  filters.keyword = '';
  loadData();
}

// 获取账户名称
const getAccountName = (id: number): string => {
  const account = accounts.value.find(a => a.id === id)
  return account ? (account.account_name || '未知账户') : '未知账户'
}

// 获取交易类型名称
const getTypeName = (type: string): string => {
  const found = transactionTypes.find(t => t.value === type)
  return found ? found.label : '未知'
}

// 格式化金额 - iOS风格，统一显示格式
const formatAmount = (type: string | undefined, amount: number): string => {
  const formattedAmount = Number(amount).toFixed(2)
  if (type === 'income') {
    return `+${formattedAmount}`
  } else if (type === 'expense') {
    return `${formattedAmount}` // 移除负号，因为支出在CSS中已经用红色表示
  } else {
    return formattedAmount
  }
}
</script>

<template>
  <AppLayout>
    <div class="ios-transactions-view" @click="handleGlobalClick">
      <!-- iOS风格页面头部 -->
      <div class="ios-page-header">
        <h1 class="ios-page-title">交易记录</h1>
        <button 
          class="ios-add-btn"
          @click="openAddTransactionDialog"
        >
          <el-icon><Plus /></el-icon>
        </button>
      </div>
      
      <!-- iOS风格筛选按钮 -->
      <div v-if="isMobile" class="ios-filter-toggle">
        <button 
          class="ios-filter-btn"
          @click="showMobileFilters = !showMobileFilters"
        >
          <el-icon><Filter /></el-icon>
          <span>筛选</span>
        </button>
      </div>
      
      <!-- iOS风格筛选面板 -->
      <div class="ios-filters-section" :class="{ 'ios-hidden': isMobile && !showMobileFilters }">
        <div class="ios-filter-card">
          <div class="ios-filters-content">
            <div class="ios-filter-item">
              <label class="ios-filter-label">筛选月份</label>
              <!-- 使用新的iOS风格月份选择器 -->
              <MobileMonthPicker
                v-model="filters.dateRange"
                placeholder="选择月份"
                class="ios-month-picker"
              />
            </div>
          
            <div class="ios-filter-item">
              <label class="ios-filter-label">交易类型</label>
              <el-select v-model="filters.type" placeholder="全部类型" clearable size="small" class="ios-select">
                <el-option
                  v-for="type in transactionTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </div>
            
            <div class="ios-filter-item">
              <label class="ios-filter-label">账户</label>
              <el-select v-model="filters.account_id" placeholder="全部账户" clearable size="small" class="ios-select">
                <el-option
                  v-for="account in accounts"
                  :key="account.id"
                  :label="account.account_name"
                  :value="account.id"
                />
              </el-select>
          </div>
          
            <div class="ios-filter-item">
              <label class="ios-filter-label">关键词</label>
            <el-input 
              v-model="filters.keyword" 
              placeholder="搜索描述" 
              clearable
              size="small"
                class="ios-input"
                :prefix-icon="Search"
            />
          </div>
          
            <div class="ios-filter-actions">
              <button class="ios-btn ios-btn-primary" @click="applyFilter">筛选</button>
              <button class="ios-btn ios-btn-secondary" @click="resetFilter">重置</button>
              <button class="ios-btn ios-btn-tertiary" @click="refreshData">
                <el-icon><Refresh /></el-icon>
              </button>
          </div>
        </div>
        </div>
      </div>
      
      <!-- iOS风格交易列表 -->
      <div class="ios-transactions-list">
        <div v-if="loading" class="ios-loading-state">
          <el-skeleton :rows="5" animated />
        </div>
        
        <div v-else-if="filteredTransactions.length === 0" class="ios-empty-state">
          <div class="ios-empty-icon">📝</div>
          <p class="ios-empty-text">暂无交易记录</p>
          <button class="ios-btn ios-btn-primary ios-empty-btn" @click="openAddTransactionDialog">
            添加第一笔交易
          </button>
            </div>
            
        <div v-else class="ios-transaction-groups">
          <div v-for="group in groupedTransactions" :key="group.dateLabel" class="ios-transaction-group">
            <!-- iOS风格日期头部 -->
            <div class="ios-group-header">
              <h2 class="ios-date-label">{{ group.dateLabel }}</h2>
              <div class="ios-daily-summary">
                <span v-if="group.dailyTotal.expense > 0" class="ios-expense-total">
                  支：{{ group.dailyTotal.expense.toFixed(2) }}
                </span>
                <span v-if="group.dailyTotal.income > 0" class="ios-income-total">
                  收：{{ group.dailyTotal.income.toFixed(2) }}
              </span>
            </div>
            </div>
            
            <!-- iOS风格交易项容器 -->
            <div class="ios-transaction-items">
              <div
                v-for="transaction in group.transactions"
                :key="transaction.id" 
                class="ios-transaction-wrapper"
              >
                <!-- iOS风格滑动操作按钮 -->
                <div class="ios-swipe-actions" v-if="isMobile">
                  <button class="ios-action-edit" @click.stop="openEditTransactionDialog(transaction)">
                    <el-icon><Edit /></el-icon>
                    <span>编辑</span>
                  </button>
                  <button class="ios-action-delete" @click.stop="confirmDeleteTransaction(transaction)">
                    <el-icon><Delete /></el-icon>
                    <span>删除</span>
                  </button>
                </div>
                
                <!-- iOS风格交易项主体 -->
                <div
                  class="ios-transaction-item"
                  :class="{ 
                    'ios-swiped': isTransactionSwiped(transaction.id),
                    [`ios-transaction-${transaction.type}`]: true
                  }"
                  :style="{ 
                    transform: swipeOffsets[transaction.id] ? `translateX(-${swipeOffsets[transaction.id]}px)` : '' 
                  }"
                  @touchstart="handleTouchStart($event, transaction.id)"
                  @touchmove="handleTouchMove($event, transaction.id)"
                  @touchend="handleTouchEnd($event, transaction.id)"
                  @click="handleTransactionClick(transaction)"
                >
                  <!-- iOS风格交易内容 -->
                  <div class="ios-transaction-content">
                    <!-- 左侧图标区域 -->
                    <div class="ios-transaction-left">
                      <div class="ios-transaction-icon" :class="`ios-icon-${transaction.type}`">
                        <el-icon size="20">
                          <component :is="getIconComponent(getCategoryIcon(transaction.category, transaction.type)) || Money" />
                        </el-icon>
                      </div>
                    </div>
                    
                    <!-- 中间信息区域 -->
                    <div class="ios-transaction-info">
                      <div class="ios-transaction-main">
                        <h3 class="ios-transaction-title">
                          {{ getCategoryName(transaction.category, transaction.type) }}
                        </h3>
                        <p class="ios-transaction-subtitle">
                            {{ getAccountName(transaction.account_id || transaction.from_account_id || 0) }}
                          <span v-if="transaction.description && transaction.description !== '无描述'" class="ios-transaction-desc">
                            · {{ transaction.description }}
                          </span>
                        </p>
                      </div>
                    </div>
                    
                    <!-- 右侧金额和按钮区域 -->
                    <div class="ios-transaction-right">
                      <div class="ios-transaction-amount" :class="`ios-amount-${transaction.type}`">
                        {{ formatAmount(transaction.type, transaction.amount) }}
                      </div>
                      <button class="ios-transfer-btn" v-if="transaction.type === 'transfer'">
                        转账
                      </button>
                      <div class="ios-transaction-time" v-else>
                        {{ formatDate(transaction.date).split(' ')[1] || '' }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- iOS风格添加交易弹窗 -->
      <div v-if="transactionDialogVisible && isMobile" class="ios-modal-overlay" @click="handleOverlayClick">
        <div class="ios-transaction-modal" @click.stop>
          <!-- 弹窗头部 -->
          <div class="ios-modal-header">
            <button class="ios-header-btn cancel-btn" @click="transactionDialogVisible = false">
              取消
            </button>
            <h3 class="ios-modal-title">{{ isEditing ? '编辑交易' : '添加交易' }}</h3>
            <button class="ios-header-btn confirm-btn" @click="saveTransaction" :disabled="submitting">
              {{ submitting ? '保存中...' : '确定' }}
            </button>
          </div>

          <!-- 表单内容 -->
          <div class="ios-form-content">
            <!-- 日期字段 -->
            <div class="ios-form-group">
              <div class="ios-form-row" @click="showDatePicker = true">
                <div class="ios-form-label">日期</div>
                <div class="ios-form-value">
                  <span class="ios-value-text">{{ formatDisplayDate(transactionForm.date) }}</span>
                  <el-icon class="ios-chevron"><ArrowRight /></el-icon>
                </div>
              </div>
            </div>

            <!-- 类型字段 -->
            <div class="ios-form-group">
              <div class="ios-form-row" @click="showTypePicker = true">
                <div class="ios-form-label">类型</div>
                <div class="ios-form-value">
                  <span class="ios-value-text" :class="{ 'ios-placeholder': !transactionForm.type }">
                    {{ getTypeLabel(transactionForm.type) || '支出' }}
                  </span>
                  <el-icon class="ios-chevron"><ArrowRight /></el-icon>
                </div>
              </div>
            </div>

            <!-- 金额字段 -->
            <div class="ios-form-group">
              <div class="ios-form-row">
                <div class="ios-form-label">金额</div>
                <div class="ios-form-input">
                  <input
                    v-model.number="transactionForm.amount"
                    type="number"
                    step="0.01"
                    min="0.01"
                    placeholder="0.00"
                    class="ios-amount-input"
                    @input="validateAmount"
                  />
                </div>
              </div>
            </div>

            <!-- 分类字段 -->
            <div class="ios-form-group" v-if="transactionForm.type !== 'transfer'">
              <div class="ios-form-row" @click="showCategoryPicker = true">
                <div class="ios-form-label">分类</div>
                <div class="ios-form-value">
                  <span class="ios-value-text" :class="{ 'ios-placeholder': !selectedCategoryName }">
                    {{ selectedCategoryName || '请选择分类' }}
                  </span>
                  <el-icon class="ios-chevron"><ArrowRight /></el-icon>
                </div>
              </div>
            </div>

            <!-- 账户字段 -->
            <div class="ios-form-group" v-if="transactionForm.type !== 'transfer'">
              <div class="ios-form-row" @click="showAccountPicker = true">
                <div class="ios-form-label">账户</div>
                <div class="ios-form-value">
                  <span class="ios-value-text" :class="{ 'ios-placeholder': !selectedAccountName }">
                    {{ selectedAccountName || '请选择账户' }}
                  </span>
                  <el-icon class="ios-chevron"><ArrowRight /></el-icon>
                </div>
              </div>
            </div>

            <!-- 转出账户字段 -->
            <div class="ios-form-group" v-if="transactionForm.type === 'transfer'">
              <div class="ios-form-row" @click="showFromAccountPicker = true">
                <div class="ios-form-label">转出账户</div>
                <div class="ios-form-value">
                  <span class="ios-value-text" :class="{ 'ios-placeholder': !selectedFromAccountName }">
                    {{ selectedFromAccountName || '请选择转出账户' }}
                  </span>
                  <el-icon class="ios-chevron"><ArrowRight /></el-icon>
                </div>
              </div>
            </div>

            <!-- 转入账户字段 -->
            <div class="ios-form-group" v-if="transactionForm.type === 'transfer'">
              <div class="ios-form-row" @click="showToAccountPicker = true">
                <div class="ios-form-label">转入账户</div>
                <div class="ios-form-value">
                  <span class="ios-value-text" :class="{ 'ios-placeholder': !selectedToAccountName }">
                    {{ selectedToAccountName || '请选择转入账户' }}
                  </span>
                  <el-icon class="ios-chevron"><ArrowRight /></el-icon>
                </div>
              </div>
            </div>

            <!-- 描述字段 -->
            <div class="ios-form-group">
              <div class="ios-form-row ios-textarea-row">
                <div class="ios-form-label-with-count">
                  <span class="ios-form-label">描述</span>
                  <span class="char-count">{{ transactionForm.description?.length || 0 }}/50</span>
                </div>
                <div class="ios-form-textarea">
                  <textarea
                    v-model="transactionForm.description"
                    placeholder="添加备注信息（可选）"
                    class="ios-textarea auto-resize"
                    maxlength="50"
                    @input="autoResizeTextarea"
                    ref="descriptionTextarea"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 底部按钮 -->
          <div class="ios-modal-footer">
            <button class="ios-btn ios-btn-cancel" @click="transactionDialogVisible = false">
              取消
            </button>
            <button
              class="ios-btn ios-btn-primary"
              @click="saveTransaction"
              :disabled="submitting"
            >
              {{ submitting ? '保存中...' : (isEditing ? '更新' : '确定') }}
            </button>
          </div>
        </div>
      </div>

      <!-- 桌面端对话框保持不变 -->
      <el-dialog
        v-if="!isMobile"
        v-model="transactionDialogVisible"
        :title="isEditing ? '编辑交易' : '添加交易'"
        width="500px"
        center
      >
        <div class="form-container desktop-form">
          <!-- 桌面端表单 -->
          <el-form :model="transactionForm" class="desktop-transaction-form">
            <el-form-item label="日期">
              <MobileDatetimePicker v-model="transactionForm.date" />
            </el-form-item>

            <el-form-item label="类型">
              <el-select v-model="transactionForm.type" @change="transactionForm.category = ''" placeholder="请选择类型">
                <el-option
                  v-for="type in transactionTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="金额">
              <el-input v-model.number="transactionForm.amount" type="number" step="0.01" placeholder="0" />
            </el-form-item>

            <el-form-item v-if="transactionForm.type !== 'transfer'" label="分类">
              <el-select v-model="transactionForm.category" placeholder="请选择分类">
                <el-option
                  v-for="category in availableCategories"
                  :key="category.value"
                  :label="category.label"
                  :value="category.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item v-if="transactionForm.type === 'transfer'" label="转出账户">
              <el-select v-model="transactionForm.from_account_id" placeholder="请选择转出账户">
                <el-option
                  v-for="account in accounts"
                  :key="account.id"
                  :label="account.account_name"
                  :value="account.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item v-if="transactionForm.type === 'transfer'" label="转入账户">
              <el-select v-model="transactionForm.to_account_id" placeholder="请选择转入账户">
                <el-option
                  v-for="account in accounts"
                  :key="account.id"
                  :label="account.account_name"
                  :value="account.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item v-if="transactionForm.type !== 'transfer'" label="账户">
              <el-select v-model="transactionForm.account_id" placeholder="请选择账户">
                <el-option
                  v-for="account in accounts"
                  :key="account.id"
                  :label="account.account_name"
                  :value="account.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="描述">
              <el-input v-model="transactionForm.description" placeholder="请输入交易描述" />
            </el-form-item>
          </el-form>
        </div>

        <!-- 桌面端底部按钮 -->
        <template #footer v-if="!isMobile">
          <span class="dialog-footer">
            <el-button @click="transactionDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="saveTransaction" :loading="submitting">
              {{ isEditing ? '更新' : '添加' }}
            </el-button>
          </span>
        </template>
      </el-dialog>





      <!-- iOS风格轻量选择器 -->
      <!-- 类型选择器 -->
      <div v-if="showTypePicker" class="ios-picker-overlay" @click="showTypePicker = false">
        <div class="ios-picker-modal" @click.stop>
          <div class="ios-picker-header">
            <button class="ios-picker-btn" @click="showTypePicker = false">取消</button>
            <span class="ios-picker-title">选择类型</span>
            <button class="ios-picker-btn confirm" @click="showTypePicker = false">完成</button>
          </div>
          <div class="ios-picker-content">
            <div
              v-for="type in transactionTypes"
              :key="type.value"
              class="ios-picker-item"
              :class="{ 'active': type.value === transactionForm.type }"
              @click="selectType(type.value)"
            >
              <span class="item-label">{{ type.label }}</span>
              <el-icon v-if="type.value === transactionForm.type" class="item-check">
                <Check />
              </el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 分类选择器 -->
      <div v-if="showCategoryPicker" class="ios-picker-overlay" @click="showCategoryPicker = false">
        <div class="ios-picker-modal" @click.stop>
          <div class="ios-picker-header">
            <button class="ios-picker-btn" @click="showCategoryPicker = false">取消</button>
            <span class="ios-picker-title">选择分类</span>
            <button class="ios-picker-btn confirm" @click="showCategoryPicker = false">完成</button>
          </div>
          <div class="ios-picker-content">
            <div
              v-for="category in availableCategories"
              :key="category.value"
              class="ios-picker-item"
              :class="{ 'active': category.value === transactionForm.category }"
              @click="selectCategory(category.value)"
            >
              <div class="item-content">
                <div class="item-icon-wrapper">
                  <el-icon v-if="getIconComponent(category.icon)" class="item-icon">
                    <component :is="getIconComponent(category.icon)" />
                  </el-icon>
                  <span v-else class="item-icon-text">{{ category.icon }}</span>
                </div>
                <span class="item-label">{{ category.label }}</span>
              </div>
              <el-icon v-if="category.value === transactionForm.category" class="item-check">
                <Check />
              </el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 账户选择器 -->
      <div v-if="showAccountPicker" class="ios-picker-overlay" @click="showAccountPicker = false">
        <div class="ios-picker-modal" @click.stop>
          <div class="ios-picker-header">
            <button class="ios-picker-btn" @click="showAccountPicker = false">取消</button>
            <span class="ios-picker-title">选择账户</span>
            <button class="ios-picker-btn confirm" @click="showAccountPicker = false">完成</button>
          </div>
          <div class="ios-picker-content">
            <div
              v-for="account in accounts"
              :key="account.id"
              class="ios-picker-item"
              :class="{ 'active': account.id === transactionForm.account_id }"
              @click="selectAccount(account.id)"
            >
              <div class="item-info">
                <span class="item-label">{{ account.account_name }}</span>
                <span class="item-type">{{ getAccountTypeLabel(account.account_type) }}</span>
              </div>
              <el-icon v-if="account.id === transactionForm.account_id" class="item-check">
                <Check />
              </el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 转出账户选择器 -->
      <div v-if="showFromAccountPicker" class="ios-picker-overlay" @click="showFromAccountPicker = false">
        <div class="ios-picker-modal" @click.stop>
          <div class="ios-picker-header">
            <button class="ios-picker-btn" @click="showFromAccountPicker = false">取消</button>
            <span class="ios-picker-title">选择转出账户</span>
            <button class="ios-picker-btn confirm" @click="showFromAccountPicker = false">完成</button>
          </div>
          <div class="ios-picker-content">
            <div
              v-for="account in accounts"
              :key="account.id"
              class="ios-picker-item"
              :class="{ 'active': account.id === transactionForm.from_account_id }"
              @click="selectFromAccount(account.id)"
            >
              <div class="item-info">
                <span class="item-label">{{ account.account_name }}</span>
                <span class="item-type">{{ getAccountTypeLabel(account.account_type) }}</span>
              </div>
              <el-icon v-if="account.id === transactionForm.from_account_id" class="item-check">
                <Check />
              </el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 转入账户选择器 -->
      <div v-if="showToAccountPicker" class="ios-picker-overlay" @click="showToAccountPicker = false">
        <div class="ios-picker-modal" @click.stop>
          <div class="ios-picker-header">
            <button class="ios-picker-btn" @click="showToAccountPicker = false">取消</button>
            <span class="ios-picker-title">选择转入账户</span>
            <button class="ios-picker-btn confirm" @click="showToAccountPicker = false">完成</button>
          </div>
          <div class="ios-picker-content">
            <div
              v-for="account in accounts"
              :key="account.id"
              class="ios-picker-item"
              :class="{ 'active': account.id === transactionForm.to_account_id }"
              :disabled="account.id === transactionForm.from_account_id"
              @click="account.id !== transactionForm.from_account_id && selectToAccount(account.id)"
            >
              <div class="item-info">
                <span class="item-label" :class="{ 'disabled': account.id === transactionForm.from_account_id }">
                  {{ account.account_name }}
                </span>
                <span class="item-type" :class="{ 'disabled': account.id === transactionForm.from_account_id }">
                  {{ getAccountTypeLabel(account.account_type) }}
                </span>
              </div>
              <el-icon v-if="account.id === transactionForm.to_account_id" class="item-check">
                <Check />
              </el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 日期时间选择器 -->
      <el-dialog
        v-model="showDatePicker"
        title="选择日期时间"
        :width="isMobile ? '100%' : '400px'"
        :fullscreen="isMobile"
        center
      >
        <div class="date-picker-content">
          <MobileDatetimePicker
            v-model="transactionForm.date"
            placeholder="选择交易日期和时间"
          />
          <div class="date-picker-actions">
            <button class="picker-btn cancel" @click="showDatePicker = false">取消</button>
            <button class="picker-btn confirm" @click="showDatePicker = false">确定</button>
          </div>
        </div>
      </el-dialog>
    </div>
  </AppLayout>
</template>

<style lang="scss" scoped>
// iOS风格颜色和变量
$ios-primary-color: #007AFF;
$ios-primary-light: #3395FF;
$ios-primary-lighter: #66B0FF;
$ios-secondary-color: #34C759;
$ios-secondary-light: #52D478;
$ios-warning-color: #FF9500;
$ios-warning-light: #FFA31A;
$ios-warning-dark: #E6850E;
$ios-warning-darker: #CC7A0D;
$ios-danger-color: #FF3B30;
$ios-danger-light: #FF5550;
$ios-gray-1: #F2F2F7;
$ios-gray-2: #E5E5EA;
$ios-gray-3: #C7C7CC;
$ios-gray-4: #8E8E93;
$ios-gray-5: #636366;
$ios-gray-6: #48484A;
$ios-text-primary: #1C1C1E;
$ios-text-secondary: #8E8E93;
$ios-background: #F2F2F7;
$ios-card-background: rgba(255, 255, 255, 0.95);
$ios-shadow: 0 2px 16px rgba(0, 0, 0, 0.06);
$ios-shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.1);
$ios-border-radius: 16px;
$ios-border-radius-small: 12px;

.ios-transactions-view {
  padding: 20px;
  background: linear-gradient(135deg, #F2F2F7 0%, #E5E5EA 100%);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;

  // iOS风格页面头部
  .ios-page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 0 4px;

    .ios-page-title {
      font-size: 34px;
      font-weight: 700;
      color: $ios-text-primary;
      letter-spacing: -0.5px;
      margin: 0;
      line-height: 1.2;
    }

    .ios-add-btn {
      width: 44px;
      height: 44px;
      border-radius: 22px;
      background: linear-gradient(135deg, $ios-primary-color 0%, $ios-primary-lighter 100%);
      border: none;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      box-shadow: $ios-shadow;

      &:hover {
        transform: scale(1.05);
        box-shadow: $ios-shadow-hover;
      }

      &:active {
        transform: scale(0.95);
      }

      .el-icon {
        font-size: 20px;
      }
    }
  }

  // iOS风格筛选按钮
  .ios-filter-toggle {
    margin-bottom: 16px;

    .ios-filter-btn {
      background: $ios-card-background;
      border: 1px solid $ios-gray-2;
      border-radius: $ios-border-radius-small;
      padding: 12px 20px;
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
      backdrop-filter: blur(20px);
      color: $ios-text-primary;
      font-weight: 500;
      width: 100%;
      justify-content: center;

      &:hover {
        background: rgba(255, 255, 255, 1);
        border-color: $ios-gray-3;
      }

      .el-icon {
        font-size: 16px;
        color: $ios-primary-color;
      }
    }
  }

  // iOS风格筛选面板
  .ios-filters-section {
    background: $ios-card-background;
    border-radius: $ios-border-radius;
    padding: 20px;
    margin-bottom: 20px;
    backdrop-filter: blur(20px);
    border: 1px solid $ios-gray-2;
    box-shadow: $ios-shadow;
    transition: all 0.3s ease;

    &.ios-hidden {
      display: none;
    }

    .ios-filter-card {
      .ios-filters-content {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .ios-filter-item {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .ios-filter-label {
            font-size: 17px;
            font-weight: 600;
            color: $ios-text-primary;
            letter-spacing: -0.2px;
          }

          .ios-date-picker,
          .ios-date-range-picker,
          .ios-select,
          .ios-input {
            width: 100%;
            background: transparent;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: $ios-border-radius-small;
            transition: all 0.2s ease;
            height: 44px;
            box-sizing: border-box;

            // Element Plus 组件样式覆盖
            :deep(.el-input__wrapper) {
              background: transparent !important;
              border: none !important;
              border-radius: $ios-border-radius-small !important;
              box-shadow: none !important;
              transition: all 0.2s ease !important;
              height: 42px !important;
              padding: 0 12px !important;
            }
            
            // 移动端日期范围选择器和月份选择器样式覆盖
            &.ios-date-range-picker,
            &.ios-month-picker {
              border: none !important;
              background: transparent !important;
              height: auto !important;
              padding: 0 !important;
            }

            :deep(.el-input__inner) {
              background: transparent !important;
              border: none !important;
              border-radius: $ios-border-radius-small !important;
              box-shadow: none !important;
              height: 42px !important;
              line-height: 42px !important;
              text-align: left !important;
              padding: 0 !important;
            }

            :deep(.el-select__wrapper) {
              background: transparent !important;
              border: none !important;
              border-radius: $ios-border-radius-small !important;
              box-shadow: none !important;
              height: 42px !important;
              padding: 0 12px !important;
            }
            
            :deep(.el-select__selected-item) {
              text-align: left !important;
              line-height: 42px !important;
            }
            
            :deep(.el-select__placeholder) {
              text-align: left !important;
              line-height: 42px !important;
            }
            
            &:hover {
              border-color: rgba(0, 0, 0, 0.2);
              background: rgba(255, 255, 255, 0.5);

              :deep(.el-input__wrapper),
              :deep(.el-input__inner),
              :deep(.el-select__wrapper) {
                border: none !important;
                background: rgba(255, 255, 255, 0.5) !important;
                box-shadow: none !important;
              }
            }
            
            // 移动端日期范围选择器不应用hover样式
            &.ios-date-range-picker:hover,
            &.ios-month-picker:hover {
              border-color: transparent !important;
              background: transparent !important;
            }

            &:focus-within {
              border-color: $ios-primary-color;
              background: rgba(255, 255, 255, 0.8);
              box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);

              :deep(.el-input__wrapper),
              :deep(.el-input__inner),
              :deep(.el-select__wrapper) {
                border: none !important;
                background: rgba(255, 255, 255, 0.8) !important;
                box-shadow: none !important;
              }
            }
            
            // 移动端日期范围选择器不应用focus样式
            &.ios-date-range-picker:focus-within,
            &.ios-month-picker:focus-within {
              border-color: transparent !important;
              background: transparent !important;
              box-shadow: none !important;
            }

            // 确保选择器的展开状态样式
            &.is-focus :deep(.el-input__wrapper),
            :deep(.el-input__wrapper.is-focus),
            &.is-focus :deep(.el-input__inner),
            :deep(.el-input__inner.is-focus),
            &.is-focus :deep(.el-select__wrapper),
            :deep(.el-select__wrapper.is-focus) {
              border: none !important;
              background: rgba(255, 255, 255, 0.8) !important;
              box-shadow: none !important;
            }
          }
        }

        .ios-filter-actions {
          display: flex;
          gap: 12px;
          margin-top: 8px;

          .ios-btn {
            flex: 1;
            padding: 12px 16px;
            border-radius: $ios-border-radius-small;
            border: none;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;

            &.ios-btn-primary {
              background: linear-gradient(135deg, $ios-primary-color 0%, $ios-primary-light 100%);
              color: white;
              box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
              }
            }

            &.ios-btn-secondary {
              background: $ios-gray-1;
              color: $ios-text-primary;
              border: 1px solid $ios-gray-2;

              &:hover {
                background: $ios-gray-2;
              }
            }

            &.ios-btn-tertiary {
              background: rgba(142, 142, 147, 0.1);
              color: $ios-gray-5;
              width: 44px;
              flex: none;

              &:hover {
                background: rgba(142, 142, 147, 0.2);
              }
            }

            &:active {
              transform: scale(0.95);
            }
          }
        }
      }
    }
  }

  // iOS风格交易列表
  .ios-transactions-list {
    .ios-loading-state,
    .ios-empty-state {
      text-align: center;
      padding: 60px 20px;
      background: $ios-card-background;
      border-radius: $ios-border-radius;
      backdrop-filter: blur(20px);
      border: 1px solid $ios-gray-2;
      box-shadow: $ios-shadow;
    }

    .ios-empty-state {
      .ios-empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }

      .ios-empty-text {
        font-size: 17px;
        color: $ios-text-secondary;
        margin: 0 0 24px 0;
        font-weight: 500;
      }

      .ios-empty-btn {
        background: linear-gradient(135deg, $ios-primary-color 0%, $ios-primary-light 100%);
        color: white;
        border: none;
        padding: 14px 24px;
        border-radius: $ios-border-radius-small;
        font-weight: 600;
        font-size: 16px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
        }
      }
    }

    .ios-transaction-groups {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .ios-transaction-group {
      // iOS风格日期头部
      .ios-group-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 8px 12px 8px;

        .ios-date-label {
          font-size: 22px;
          font-weight: 700;
          color: $ios-text-primary;
          margin: 0;
          letter-spacing: -0.3px;
        }

        .ios-daily-summary {
          display: flex;
          gap: 12px;
          font-size: 15px;
          font-weight: 600;

          .ios-expense-total {
            color: $ios-danger-color;
          }

          .ios-income-total {
            color: $ios-secondary-color;
          }
        }
      }

      // iOS风格交易项容器
      .ios-transaction-items {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .ios-transaction-wrapper {
          position: relative;
          overflow: hidden;
          border-radius: $ios-border-radius;
        
          // iOS风格滑动操作按钮
          .ios-swipe-actions {
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 140px;
            display: flex;
            z-index: 1;
            
            button {
              flex: 1;
              border: none;
              color: white;
              cursor: pointer;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              gap: 4px;
              font-size: 12px;
              font-weight: 600;
              transition: all 0.2s ease;
              
              .el-icon {
                  font-size: 16px;
              }
              
              span {
                  font-size: 11px;
                  font-weight: 500;
              }
            }
            
            .ios-action-edit {
              background: linear-gradient(135deg, $ios-gray-4 0%, $ios-gray-5 100%);
            
              &:active {
                  background: linear-gradient(135deg, $ios-gray-5 0%, $ios-gray-6 100%);
              }
            }
            
            .ios-action-delete {
              background: linear-gradient(135deg, $ios-warning-color 0%, $ios-warning-dark 100%);
            
              &:active {
                background: linear-gradient(135deg, $ios-warning-dark 0%, $ios-warning-darker 100%);
              }
            }
          }

          // iOS风格交易项主体
          .ios-transaction-item {
            position: relative;
            z-index: 2;
            background: $ios-card-background;
            border-radius: $ios-border-radius;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border: 1px solid rgba(0, 0, 0, 0.04);
            box-shadow: $ios-shadow;
            backdrop-filter: blur(20px);
        
            &:hover {
              background: rgba(255, 255, 255, 1);
              box-shadow: $ios-shadow-hover;
              transform: translateY(-2px);
            }

            &:active {
              transform: translateY(0) scale(0.98);
            }

            // iOS风格交易内容
            .ios-transaction-content {
              display: flex;
              align-items: center;
              gap: 16px;
          
              // 左侧图标区域
              .ios-transaction-left {
                flex-shrink: 0;

                .ios-transaction-icon {
                  width: 44px;
                  height: 44px;
                  border-radius: 12px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
                  font-weight: 600;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

                  &.ios-icon-income {
                    background: linear-gradient(135deg, $ios-secondary-color 0%, $ios-secondary-light 100%);
                  }
              
                  &.ios-icon-expense {
                    background: linear-gradient(135deg, $ios-danger-color 0%, $ios-danger-light 100%);
                  }
              
                  &.ios-icon-transfer {
                    background: linear-gradient(135deg, $ios-primary-color 0%, $ios-primary-lighter 100%);
                  }
                }
              }
          
              // 中间信息区域
              .ios-transaction-info {
                flex: 1;
                min-width: 0;
            
                .ios-transaction-main {
                  display: flex;
                  flex-direction: column;
                  gap: 6px;

                  .ios-transaction-title {
                    font-size: 17px;
                    font-weight: 600;
                    color: $ios-text-primary;
                    margin: 0;
                    line-height: 1.3;
                    letter-spacing: -0.2px;
                  }

                  .ios-transaction-subtitle {
                    font-size: 15px;
                    color: $ios-text-secondary;
                    margin: 0;
                    line-height: 1.4;
                    font-weight: 500;

                    .ios-transaction-desc {
                      color: $ios-gray-4;
                      font-style: italic;
                      font-weight: 400;
                    }
                  }
                }
              }
          
              // 右侧金额和按钮区域
              .ios-transaction-right {
                flex-shrink: 0;
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                gap: 8px;
            
                .ios-transaction-amount {
                  font-size: 19px;
                  font-weight: 700;
                  line-height: 1.2;
                  letter-spacing: -0.3px;
                  color: $ios-danger-color; // 根据需求，金额统一使用红色
                }

                .ios-transfer-btn {
                  background: linear-gradient(135deg, $ios-warning-color 0%, $ios-warning-light 100%);
                  color: white;
                  border: none;
                  padding: 6px 12px;
                  border-radius: 8px;
                  font-size: 13px;
                  font-weight: 600;
                  cursor: pointer;
                  transition: all 0.2s ease;
                  box-shadow: 0 1px 4px rgba(255, 149, 0, 0.3);

                  &:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 2px 6px rgba(255, 149, 0, 0.4);
                  }

                  &:active {
                    transform: scale(0.95);
                  }
                }

                .ios-transaction-time {
                  font-size: 13px;
                  color: $ios-gray-4;
                  font-weight: 500;
                }
              }
            }
          }
        }
      }
    }
  }
}

  // iOS风格对话框
  .ios-transaction-dialog {
    .ios-transaction-form {
      .el-form-item {
        margin-bottom: 20px;

        .el-form-item__label {
          font-weight: 600;
          color: $ios-text-primary;
          font-size: 16px;
        }

        .el-input,
        .el-select {
          width: 100%; // 确保el-select组件撑满容器
          border-radius: $ios-border-radius-small;

          .el-input__inner {
            border-radius: $ios-border-radius-small;
            border: none !important;
            background: transparent !important;
            transition: all 0.2s ease;

            &:focus {
              border: none !important;
              background: transparent !important;
              box-shadow: none !important;
            }
          }
        }
      }
    }

    .ios-dialog-footer {
      display: flex;
      gap: 20px;
      padding: 0 8px;
      
      @media (max-width: 768px) {
        gap: 12px;
        padding: 0;
        margin: 0;
      }

      .ios-btn {
        flex: 1;
        padding: 16px 20px;
        border-radius: $ios-border-radius-small;
        border: none;
        font-weight: 600;
        font-size: 16px;
        cursor: pointer;
        transition: all 0.2s ease;
        
        @media (max-width: 768px) {
          padding: 16px 24px;
          font-size: 16px;
          border-radius: 10px;
          font-weight: 600;
        }

        &.ios-btn-primary {
          background: linear-gradient(135deg, $ios-primary-color 0%, $ios-primary-light 100%);
          color: white;
          
          @media (max-width: 768px) {
            background: linear-gradient(135deg, #8b7cf6 0%, #667eea 100%);
          }

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(139, 124, 246, 0.4);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
          }
        }

        &.ios-btn-secondary {
          background: $ios-gray-1;
          color: $ios-text-primary;
          border: 1px solid $ios-gray-2;
          
          @media (max-width: 768px) {
            background: transparent !important;
            color: #666 !important;
            border: none !important;
            box-shadow: none !important;
          }
            
          &:hover {
            background: $ios-gray-2;
            
            @media (max-width: 768px) {
              background: rgba(0, 0, 0, 0.05) !important;
            }
          }
        }

        &:active {
          transform: scale(0.98);
        }
      }
    }
  }

  // Element Plus弹窗默认使用center属性居中

// 移动端适配 - 彻底重写弹窗样式
@media (max-width: 768px) {
  // 移动端弹窗完全自定义样式
  :deep(.ios-transaction-dialog) {
    // 遮罩层
    .el-overlay {
      background: rgba(0, 0, 0, 0.5) !important;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    // 定位到最外层的弹窗容器，移除它的背景和阴影
    .el-overlay-dialog {
      background: none !important;
      box-shadow: none !important;
    }
    
    // 弹窗容器 - 无背景透明
    .el-dialog {
      all: unset !important;
      display: flex !important;
      flex-direction: column !important;
      width: 90vw !important;
      max-width: 400px !important;
      max-height: 85vh !important;
      margin: 0 !important; // 移除外边距，由 flex 居中
      background: white !important;
      border-radius: 20px !important;
      overflow: hidden !important;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
      position: relative !important;
    }

    // 头部 - 渐变背景
    .el-dialog__header {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      position: relative !important;
      padding: 24px !important;
      background: linear-gradient(135deg, #8b7cf6 0%, #667eea 100%) !important;
      border-top-left-radius: 20px !important;
      border-top-right-radius: 20px !important;
      margin: 0 !important;
      
      .el-dialog__title {
        font-size: 20px !important;
        font-weight: 600 !important;
        color: white !important;
        margin: 0 !important;
        text-align: center !important;
      }
      
      .el-dialog__headerbtn {
        position: absolute !important;
        top: 20px !important;
        right: 20px !important;
        width: 32px !important;
        height: 32px !important;
        background: none !important;
        border: none !important;
        padding: 0 !important;
        
        .el-dialog__close {
          font-size: 24px !important;
          color: white !important;
          font-weight: normal !important;
        }
      }
    }

    // 内容区域 - 统一白色背景
    .el-dialog__body {
      flex: 1 !important;
      overflow-y: auto !important;
      padding: 20px 16px !important;
      background: white !important;
      margin: 0 !important;

      /* 隐藏滚动条 */
      scrollbar-width: none !important; /* Firefox */
      -ms-overflow-style: none !important; /* IE and Edge */

      &::-webkit-scrollbar {
        display: none !important; /* Chrome, Safari, Opera */
      }
    }
    
    // 底部按钮区域 - 白色背景连接
    .el-dialog__footer {
      display: flex !important;
      justify-content: flex-end !important;
      align-items: center !important;
      gap: 12px !important;
      padding: 16px 16px 20px !important;
      background: white !important;
      border-bottom-left-radius: 20px !important;
      border-bottom-right-radius: 20px !important;
      margin: 0 !important;
      
      .el-button {
        margin: 0 !important;
      }
    }
    
    // 表单样式 - 简洁布局
    .ios-transaction-form {
      .el-form-item {
        margin-bottom: 20px !important;
        display: flex !important;
        align-items: center !important;
        
        .el-form-item__label {
          font-size: 16px !important;
          font-weight: normal !important;
          color: #333 !important;
          width: 50px !important;
          text-align: left !important;
          margin-bottom: 0 !important;
          margin-right: 12px !important;
          padding: 0 !important;
          line-height: 1.4 !important;
          flex-shrink: 0 !important;
        }
        
        .el-form-item__content {
          flex: 1 !important;
          margin-left: 0 !important;
        }
        
        .el-input,
        .el-select,
        .el-date-editor {
          .el-input__wrapper {
            background: white !important;
            border: 1px solid #e5e7eb !important;
            border-radius: 6px !important;
            padding: 10px 12px !important;
            box-shadow: none !important;
            transition: all 0.2s ease !important;
            
            &:hover,
            &:focus-within {
              border-color: #8b7cf6 !important;
            }
          }
          
          .el-input__inner {
            font-size: 16px !important;
            color: #333 !important;
            padding: 0 !important;
            height: auto !important;
            line-height: 1.4 !important;
            
            &::placeholder {
              color: #9ca3af !important;
            }
          }
        }
        
        .el-select {
          .el-select__wrapper {
            background: white !important;
            border: 1px solid #e5e7eb !important;
            border-radius: 6px !important;
            padding: 10px 12px !important;
            box-shadow: none !important;
            
            &:hover,
            &.is-focused {
              border-color: #8b7cf6 !important;
            }
          }
          
          .el-select__placeholder {
            color: #9ca3af !important;
          }
          
          .el-select__suffix {
            .el-select__caret {
              color: #8E8E93 !important;
              font-size: 14px !important;
            }
          }
        }
         
         // 移动端日期选择器特殊优化
         .el-date-editor {
           width: 100% !important;
           
           .el-input__wrapper {
             .el-input__inner {
               font-size: 16px !important;
               text-align: left !important;
             }
             
             .el-input__suffix {
               .el-input__suffix-inner {
                 .el-icon {
                   color: #8E8E93 !important;
                   font-size: 16px !important;
                 }
               }
             }
           }
         }
      }
    }
  }

  .ios-transactions-view {
    padding: 16px;
    
    .ios-page-header {
      margin-bottom: 20px;
      
      .ios-page-title {
        font-size: 28px;
      }
      
      .ios-add-btn {
        width: 40px;
        height: 40px;
        border-radius: 20px;
                
        .el-icon {
          font-size: 18px;
        }
      }
    }

    .ios-filter-toggle {
      margin-bottom: 12px;
    }

    .ios-filters-section {
      padding: 16px;
      margin-bottom: 16px;
      
      .ios-filter-card {
        .ios-filters-content {
          gap: 12px;
          
          .ios-filter-item {
            gap: 6px;
            
            .ios-filter-label {
              font-size: 15px;
            }
          }
          
          .ios-filter-actions {
            gap: 8px;
            
            .ios-btn {
              padding: 10px 12px;
              font-size: 14px;
              
              &.ios-btn-tertiary {
                width: 40px;
              }
            }
          }
        }
      }
    }
    
    .ios-transaction-groups {
      gap: 16px;
      
      .ios-transaction-group {
        .ios-group-header {
          padding: 0 4px 8px 4px;
          
          .ios-date-label {
            font-size: 20px;
          }
          
          .ios-daily-summary {
            font-size: 14px;
          }
        }
        
        .ios-transaction-items {
          gap: 6px;
          
          .ios-transaction-wrapper {
            .ios-transaction-item {
              padding: 16px;
              
              .ios-transaction-content {
                gap: 12px;
        
                .ios-transaction-left {
                  .ios-transaction-icon {
                    width: 40px;
                    height: 40px;
                    border-radius: 10px;
                  }
                }
                
                .ios-transaction-info {
                  .ios-transaction-main {
                    gap: 4px;
            
                    .ios-transaction-title {
                      font-size: 16px;
                    }
                    
                    .ios-transaction-subtitle {
                      font-size: 14px;
                    }
                  }
                }
                
                .ios-transaction-right {
                  gap: 6px;
                  
                  .ios-transaction-amount {
                    font-size: 17px;
                  }
                  
                  .ios-transfer-btn {
                    padding: 5px 10px;
                    font-size: 12px;
                  }
                  
                  .ios-transaction-time {
                    font-size: 12px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// Element Plus 下拉面板样式覆盖
.el-select-dropdown {
  border: 1px solid #e0e0e0 !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) !important;

  .el-select-dropdown__item {
    color: #333 !important;
    text-align: left !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    padding: 8px 16px !important;
    line-height: 1.5 !important;
    min-height: 36px !important;
    
    &:hover {
      background: rgba(0, 122, 255, 0.08) !important;
      color: #007AFF !important;
    }

    &.selected {
      background: rgba(0, 122, 255, 0.1) !important;
      color: #007AFF !important;
      font-weight: 600 !important;
    }
  }
}


/* 最终修复：移动端弹窗样式覆盖 */
@media (max-width: 768px) {
  .el-overlay {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    backdrop-filter: blur(5px);
    background: rgba(0, 0, 0, 0.4);
  }
  .el-overlay-dialog {
    background: none !important;
    box-shadow: none !important;
  }
}

// 采用新的class来定义弹窗样式，避免冲突
:deep(.custom-transaction-dialog) {
  all: unset; // 重置所有el-dialog的默认样式
  display: flex !important;
  flex-direction: column !important;
  width: 90vw !important;
  max-width: 400px !important;
  max-height: 90vh !important;
  background: white !important; // 统一为白色背景
  border-radius: 20px !important;
  overflow: hidden !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

  .custom-dialog-header {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 20px;
    background: linear-gradient(135deg, #8A2BE2, #4B0082); // 紫色渐变
    color: white;

    .custom-dialog-title {
      font-size: 18px;
      font-weight: 600;
    }

    .custom-close-btn {
      position: absolute;
      top: 50%;
      right: 15px;
      transform: translateY(-50%);
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: background 0.2s;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }

      .el-icon {
        font-size: 16px;
      }
    }
  }

  .custom-transaction-form {
    flex: 1;
    overflow-y: auto;
    padding: 24px 20px;
    background: white;

    .el-form-item {
      display: flex;
      align-items: center;
      margin-bottom: 18px;

      .el-form-item__label {
        width: 60px; // 固定标签宽度
        text-align: left;
        color: #333;
        font-size: 16px;
        margin-right: 15px;
        flex-shrink: 0;
      }

      .el-form-item__content {
        flex: 1;
        margin-left: 0 !important;

        .el-select, .el-input, .el-date-editor {
          width: 100%;
        }

        .el-input__wrapper, .el-select__wrapper {
          background-color: #f7f7f7 !important;
          border: 1px solid #e5e5e5 !important;
          border-radius: 12px !important;
          box-shadow: none !important;
          padding: 6px 15px;
          transition: border-color 0.2s;

          &:hover {
            border-color: #c0c0c0 !important;
          }
        }
        
        .el-input__inner {
            height: 32px;
            line-height: 32px;
        }

        .el-select__placeholder, .el-input__inner::placeholder {
          color: #999;
        }
      }
    }
  }

  .custom-dialog-footer {
    flex-shrink: 0;
    display: flex;
    gap: 15px;
    padding: 20px;
    background: white;
    // border-top: 1px solid #f0f0f0; // 移除顶部边框

    .custom-btn {
      flex: 1;
      border-radius: 12px;
      padding: 14px 0;
      font-size: 16px;
      font-weight: 600;
      border: none;
      cursor: pointer;
      transition: all 0.2s;

      &.custom-btn-cancel {
        background: #fff;
        color: #555;
        border: 1px solid #ddd;

        &:hover {
          background: #f5f5f5;
        }
      }

      &.custom-btn-confirm {
        background: #6f42c1; // 主题紫色
        color: white;
        border: 1px solid #6f42c1;

        &:hover {
          background: #5a3d9e;
        }

        &:disabled {
          background: #c8b6e2;
          border-color: #c8b6e2;
          cursor: not-allowed;
        }
      }

      &:active {
        transform: scale(0.98);
      }
    }
  }
}

/* 移动端交易对话框样式 */
.mobile-transaction-modal {
  display: flex !important;
  align-items: flex-end !important;
  justify-content: center !important;
}

.mobile-transaction-dialog {
  :deep(.el-dialog) {
    margin: 0 !important;
    border-radius: 20px 20px 0 0 !important;
    max-height: 95vh !important;
    overflow: hidden !important;

    .el-dialog__header {
      display: none !important;
    }

    .el-dialog__body {
      padding: 0 !important;
      height: 100% !important;
      overflow: hidden !important;
    }
  }
}

.desktop-transaction-dialog {
  :deep(.el-dialog) {
    border-radius: 12px !important;

    .el-dialog__body {
      padding: 20px !important;
    }
  }
}

.mobile-dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;

  .header-btn {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    padding: 8px 16px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.3s ease;
    min-height: 36px;

    &.cancel-btn {
      color: #666;

      &:active {
        background: #f0f0f0;
      }
    }

    &.confirm-btn {
      color: #007AFF;
      font-weight: 600;

      &:active {
        background: #e3f2fd;
      }

      &:disabled {
        color: #ccc;
        cursor: not-allowed;

        &:active {
          background: none;
        }
      }

      &.loading {
        .loading-icon {
          animation: spin 1s linear infinite;
        }
      }
    }
  }

  .header-title {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0;
  }
}

.form-container {
  &.mobile-form {
    height: calc(95vh - 80px);
    overflow-y: auto;

    /* 隐藏滚动条 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }
  }
}

.mobile-form-content {
  padding: 0;
  background: #f8f9fa;
  min-height: calc(100vh - 60px);
  padding-bottom: 100px; /* 为底部按钮留出更多空间 */

  .form-item {
    background: white;
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 60px;

    .item-label {
      font-size: 16px;
      color: #333;
      font-weight: 500;
      min-width: 60px;
    }

    .item-value {
      flex: 1;
      text-align: right;
      font-size: 16px;
      color: #666;
      cursor: pointer;

      &.dropdown {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 8px;

        .dropdown-icon {
          color: #c0c0c0;
          font-size: 14px;
        }
      }
    }

    .amount-input {
      flex: 1;
      text-align: right;
      border: none;
      outline: none;
      background: transparent;
      font-size: 16px;
      color: #333;

      &::placeholder {
        color: #c0c0c0;
      }
    }

    .description-input {
      flex: 1;
      border: none;
      outline: none;
      background: transparent;
      font-size: 16px;
      color: #333;
      resize: none;
      min-height: 60px;

      &::placeholder {
        color: #c0c0c0;
      }
    }
  }

  .form-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    padding: 16px 20px;
    padding-bottom: calc(16px + env(safe-area-inset-bottom)); /* 适配iPhone底部安全区域 */
    border-top: 1px solid #f0f0f0;
    display: flex;
    gap: 12px;
    z-index: 1000;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);

    .action-btn {
      flex: 1;
      height: 48px;
      border: none;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;

      &.cancel-btn {
        background: #f8f9fa;
        color: #666;

        &:active {
          background: #e9ecef;
        }
      }

      &.confirm-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;

        &:active {
          transform: scale(0.98);
        }

        &:disabled {
          background: #ccc;
          cursor: not-allowed;

          &:active {
            transform: none;
          }
        }
      }
    }
  }

  .form-section {
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #1a1a1a;
      margin-bottom: 12px;

      .section-icon {
        margin-right: 8px;
        color: #007AFF;
      }
    }

    .field-label {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 8px;

      .el-icon {
        margin-right: 6px;
        color: #666;
        font-size: 16px;
      }

      .optional {
        color: #999;
        font-weight: 400;
        margin-left: 4px;
      }
    }
  }

  /* 交易类型选择器 */
  .type-selector {
    display: flex;
    gap: 12px;

    .type-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px 12px;
      background: #f8f9fa;
      border: 2px solid transparent;
      border-radius: 16px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
      }

      &.active {
        background: #e3f2fd;
        border-color: #007AFF;

        .type-icon {
          transform: scale(1.1);
        }

        .type-label {
          color: #007AFF;
          font-weight: 600;
        }
      }

      .type-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        transition: all 0.3s ease;

        .el-icon {
          font-size: 20px;
          color: white;
        }
      }

      .type-label {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        transition: all 0.3s ease;
      }
    }
  }

  /* 金额输入 */
  .amount-input-container {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 16px;
    padding: 16px 20px;
    transition: all 0.3s ease;

    &:focus-within {
      border-color: #007AFF;
      background: #fff;
      box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
    }

    .currency-symbol {
      font-size: 24px;
      font-weight: 600;
      color: #007AFF;
      margin-right: 8px;
    }

    .amount-input {
      flex: 1;
      border: none;
      background: transparent;
      font-size: 28px;
      font-weight: 600;
      color: #1a1a1a;
      outline: none;

      &::placeholder {
        color: #999;
      }

      /* 移除数字输入框的箭头 */
      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      &[type=number] {
        -moz-appearance: textfield;
      }
    }
  }

  .amount-tips {
    margin-top: 8px;
    text-align: center;

    .tip-text {
      font-size: 12px;
      color: #666;
    }
  }

  /* 表单行布局 */
  .form-row {
    display: flex;
    gap: 12px;

    .form-col {
      flex: 1;
    }
  }

  /* 自定义选择器 */
  .custom-select {
    width: 100%;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 14px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 48px;

    &:active {
      transform: scale(0.98);
      border-color: #007AFF;
      background: #fff;
    }

    .select-value {
      font-size: 15px;
      color: #1a1a1a;
      flex: 1;

      &.placeholder {
        color: #999;
      }
    }

    .select-arrow {
      color: #666;
      font-size: 16px;
      transform: rotate(0deg);
      transition: transform 0.3s ease;
    }

    &:active .select-arrow {
      transform: rotate(90deg);
    }
  }

  /* 账户选项样式 */
  .account-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .account-name {
      font-weight: 500;
      color: #1a1a1a;
    }

    .account-type {
      font-size: 12px;
      color: #666;
      background: #f0f0f0;
      padding: 2px 8px;
      border-radius: 8px;
    }
  }

  /* 移动端日期时间选择器 */
  .mobile-datetime-picker {
    :deep(.picker-input) {
      background: #f8f9fa;
      border: 2px solid #e9ecef;
      border-radius: 12px;
      padding: 12px 16px;
      height: auto;
      transition: all 0.3s ease;

      &:active {
        border-color: #007AFF;
        background: #fff;
      }

      .picker-value {
        font-size: 15px;
        color: #1a1a1a;

        &.placeholder {
          color: #999;
        }
      }
    }
  }

  /* 移动端文本域 */
  .mobile-textarea {
    width: 100%;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 14px 16px;
    font-size: 15px;
    color: #1a1a1a;
    resize: none;
    outline: none;
    transition: all 0.3s ease;
    font-family: inherit;
    min-height: 48px;

    &:focus {
      border-color: #007AFF;
      background: #fff;
      box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
    }

    &::placeholder {
      color: #999;
    }
  }

  .char-count {
    text-align: right;
    font-size: 12px;
    color: #999;
    margin-top: 4px;
  }
}

/* 动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 深色模式适配 */
:global(.dark-mode) {
  .mobile-dialog-header {
    background: #1c1c1e;
    border-bottom-color: #3a3a3c;

    .header-title {
      color: #e2e2e6;
    }

    .header-btn.cancel-btn {
      color: #a0a0a0;
    }
  }

  .mobile-form-content {
    .section-title {
      color: #e2e2e6;
    }

    .field-label {
      color: #e2e2e6;

      .el-icon {
        color: #a0a0a0;
      }
    }

    .type-card {
      background: #2c2c2e;

      &.active {
        background: #1c3a5e;
      }

      .type-label {
        color: #e2e2e6;
      }
    }

    .amount-input-container {
      background: #2c2c2e;
      border-color: #3a3a3c;

      &:focus-within {
        background: #1c1c1e;
      }

      .amount-input {
        color: #e2e2e6;

        &::placeholder {
          color: #8e8e93;
        }
      }
    }

    .mobile-select {
      :deep(.el-input__wrapper) {
        background: #2c2c2e;
        border-color: #3a3a3c;

        &.is-focus {
          background: #1c1c1e;
        }
      }

      :deep(.el-input__inner) {
        color: #e2e2e6;
      }
    }

    .mobile-datetime-picker {
      :deep(.picker-input) {
        background: #2c2c2e;
        border-color: #3a3a3c;

        &:active {
          background: #1c1c1e;
        }

        .picker-value {
          color: #e2e2e6;
        }
      }
    }

    .mobile-textarea {
      background: #2c2c2e;
      border-color: #3a3a3c;
      color: #e2e2e6;

      &:focus {
        background: #1c1c1e;
      }
    }
  }
}

/* 选择器弹窗样式 */
.picker-list {
  max-height: 60vh;
  overflow-y: auto;

  .picker-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: #f8f9fa;
    }

    &.active {
      background: #e3f2fd;

      .item-name {
        color: #007AFF;
        font-weight: 600;
      }
    }

    &:active {
      transform: scale(0.98);
    }

    .item-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4px;

      .item-name {
        font-size: 16px;
        color: #1a1a1a;
        font-weight: 500;
      }

      .item-type {
        font-size: 12px;
        color: #666;
        background: #f0f0f0;
        padding: 2px 8px;
        border-radius: 8px;
        align-self: flex-start;
      }
    }

    .check-icon {
      color: #007AFF;
      font-size: 20px;
    }
  }
}

.date-picker-content {
  padding: 20px 0;

  .date-picker-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;

    .picker-btn {
      flex: 1;
      height: 48px;
      border: none;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;

      &.cancel {
        background: #f8f9fa;
        color: #666;

        &:hover {
          background: #e9ecef;
        }
      }

      &.confirm {
        background: #007AFF;
        color: white;

        &:hover {
          background: #0056CC;
        }
      }

      &:active {
        transform: scale(0.98);
      }
    }
  }
}

/* 深色模式适配选择器 */
:global(.dark-mode) {
  .mobile-form-content {
    .custom-select {
      background: #2c2c2e;
      border-color: #3a3a3c;

      &:active {
        background: #1c1c1e;
      }

      .select-value {
        color: #e2e2e6;

        &.placeholder {
          color: #8e8e93;
        }
      }

      .select-arrow {
        color: #a0a0a0;
      }
    }
  }

  .picker-list {
    .picker-item {
      border-bottom-color: #3a3a3c;

      &:hover {
        background: #2c2c2e;
      }

      &.active {
        background: #1c3a5e;
      }

      .item-content {
        .item-name {
          color: #e2e2e6;
        }

        .item-type {
          background: #3a3a3c;
          color: #a0a0a0;
        }
      }
    }
  }

  .date-picker-content {
    .picker-btn {
      &.cancel {
        background: #2c2c2e;
        color: #e2e2e6;

        &:hover {
          background: #3a3a3c;
        }
      }
    }
  }
}

/* iOS风格弹窗样式 */
.ios-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.ios-transaction-modal {
  width: 100%;
  max-width: 375px;
  max-height: 85vh;
  background: #f2f2f7;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

/* 弹窗头部 */
.ios-modal-header {
  background: #ffffff;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 0.5px solid #e5e5e7;
  flex-shrink: 0;

  .ios-header-btn {
    background: none;
    border: none;
    font-size: 17px;
    font-weight: 400;
    cursor: pointer;
    padding: 0;
    min-width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;

    &.cancel-btn {
      color: #007aff;
    }

    &.confirm-btn {
      color: #007aff;
      font-weight: 600;

      &:disabled {
        color: #8e8e93;
        cursor: not-allowed;
      }
    }
  }

  .ios-modal-title {
    font-size: 17px;
    font-weight: 600;
    color: #000000;
    margin: 0;
    text-align: center;
  }

  .ios-header-placeholder {
    min-width: 44px;
  }
}

/* 表单内容区域 */
.ios-form-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 0;

  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.ios-form-group {
  background: #ffffff;
  border-radius: 12px;
  margin: 0 16px 20px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.ios-form-row {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  min-height: 56px;
  cursor: pointer;
  transition: background-color 0.15s ease;

  &:active {
    background-color: #f8f8f8;
  }

  &.ios-textarea-row {
    align-items: flex-start;
    cursor: default;

    &:active {
      background-color: transparent;
    }
  }
}

// iOS风格轻量选择器样式
.ios-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 3000;
  animation: fadeIn 0.3s ease;
}

.ios-picker-modal {
  background: white;
  border-radius: 20px 20px 0 0;
  width: 100%;
  max-width: 500px;
  max-height: 60vh;
  overflow: hidden;
  box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
  animation: slideUp 0.3s ease;
}

.ios-picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 0.5px solid #e5e5e7;
  background: #f8f9fa;
}

.ios-picker-btn {
  background: none;
  border: none;
  font-size: 16px;
  color: #007aff;
  cursor: pointer;
  padding: 4px 8px;

  &.confirm {
    font-weight: 600;
  }
}

.ios-picker-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.ios-picker-content {
  max-height: 300px;
  overflow-y: auto;
  background: white;

  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.ios-picker-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 0.5px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #f8f9fa;
  }

  &:active {
    background: #e9ecef;
  }

  &.active {
    background: rgba(0, 122, 255, 0.05);
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  &:last-child {
    border-bottom: none;
  }

  .item-content {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .item-icon-wrapper {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;

    .item-icon {
      font-size: 16px;
      color: #666;
    }

    .item-icon-text {
      font-size: 12px;
      color: #666;
    }
  }
}

.item-label {
  font-size: 16px;
  color: #333;

  &.disabled {
    color: #999;
  }
}

.item-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.item-type {
  font-size: 14px;
  color: #8e8e93;

  &.disabled {
    color: #ccc;
  }
}

.item-check {
  color: #007aff;
  font-size: 18px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.ios-form-label {
  font-size: 17px;
  color: #000000;
  font-weight: 400;
  min-width: 80px;
  flex-shrink: 0;
}

.ios-form-value {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  margin-left: 16px;

  .ios-value-text {
    font-size: 17px;
    color: #000000;
    text-align: right;

    &.ios-placeholder {
      color: #8e8e93;
    }
  }

  .ios-chevron {
    color: #c7c7cc;
    font-size: 14px;
    flex-shrink: 0;
  }
}

.ios-form-input {
  flex: 1;
  margin-left: 16px;

  .ios-amount-input {
    width: 100%;
    border: none;
    background: transparent;
    font-size: 17px;
    color: #000000;
    text-align: right;
    outline: none;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

    &::placeholder {
      color: #8e8e93;
    }

    &:focus {
      color: #007aff;
    }
  }
}

.ios-form-label-with-count {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 80px;
  flex-shrink: 0;

  .ios-form-label {
    font-size: 17px;
    color: #000000;
    font-weight: 400;
  }

  .char-count {
    font-size: 12px;
    color: #8e8e93;
    margin-left: 8px;
  }
}

.ios-form-textarea {
  flex: 1;
  margin-left: 16px;
  margin-top: 4px;

  .ios-textarea {
    width: 100%;
    border: none;
    background: transparent;
    font-size: 17px;
    color: #000000;
    resize: none;
    outline: none;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.4;
    min-height: 22px;
    max-height: 120px;
    overflow: hidden;

    /* 隐藏滚动条 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }

    &::placeholder {
      color: #8e8e93;
    }

    &:focus {
      color: #000000;
    }

    &.auto-resize {
      transition: height 0.2s ease;
    }
  }
}

/* 底部按钮 */
.ios-modal-footer {
  background: #ffffff;
  padding: 16px 20px 20px;
  display: flex;
  gap: 12px;
  flex-shrink: 0;
  border-top: 0.5px solid #e5e5e7;
}

.ios-btn {
  flex: 1;
  height: 50px;
  border: none;
  border-radius: 12px;
  font-size: 17px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.15s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  &.ios-btn-cancel {
    background: #f2f2f7;
    color: #000000;

    &:hover {
      background: #e5e5ea;
    }

    &:active {
      background: #d1d1d6;
      transform: scale(0.98);
    }
  }

  &.ios-btn-primary {
    background: #007aff;
    color: #ffffff;

    &:hover {
      background: #0056cc;
    }

    &:active {
      background: #004499;
      transform: scale(0.98);
    }

    &:disabled {
      background: #c7c7cc;
      color: #ffffff;
      cursor: not-allowed;
      transform: none;
    }
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .ios-transaction-modal {
    background: #1c1c1e;
  }

  .ios-modal-header {
    background: #2c2c2e;
    border-bottom-color: #38383a;

    .ios-modal-title {
      color: #ffffff;
    }

    .ios-header-btn {
      &.cancel-btn {
        color: #0a84ff;
      }

      &.confirm-btn {
        color: #0a84ff;

        &:disabled {
          color: #8e8e93;
        }
      }
    }
  }

  .ios-form-group {
    background: #2c2c2e;
  }

  .ios-form-label {
    color: #ffffff;
  }

  .ios-form-value .ios-value-text {
    color: #ffffff;

    &.ios-placeholder {
      color: #8e8e93;
    }
  }

  .ios-form-input .ios-amount-input {
    color: #ffffff;

    &:focus {
      color: #0a84ff;
    }
  }

  .ios-form-textarea .ios-textarea {
    color: #ffffff;
  }

  .ios-modal-footer {
    background: #2c2c2e;
    border-top-color: #38383a;
  }

  .ios-btn {
    &.ios-btn-cancel {
      background: #48484a;
      color: #ffffff;

      &:hover {
        background: #5a5a5c;
      }

      &:active {
        background: #6c6c6e;
      }
    }

    &.ios-btn-primary {
      background: #0a84ff;

      &:hover {
        background: #0066cc;
      }

      &:active {
        background: #004499;
      }
    }
  }
}
</style>


