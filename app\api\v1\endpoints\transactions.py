from typing import List, Any, Dict
from fastapi import APIRouter, Query, Depends, HTTPException, Body, Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, text
import logging
from decimal import Decimal

from app.db.session import get_db
from app.modules.transactions.models import Transaction
from app.modules.accounts.models import Account
from sqlalchemy import insert
from datetime import datetime
from app.core.security import get_current_user
from app.modules.users.schemas import User
from app.modules.categories.models import Category
from sqlalchemy import and_, or_
from sqlalchemy.orm import selectinload

router = APIRouter()
logger = logging.getLogger(__name__)

# 分类名称映射，前端英文名称到数据库中文名称的映射
CATEGORY_NAME_MAP = {
    # 支出分类
    'food': '餐饮',
    'transport': '交通',
    'shopping': '购物',
    'entertainment': '娱乐',
    'housing': '住房',
    'utilities': '水电煤',
    'communication': '通讯',
    # 收入分类
    'salary': '工资',
    'bonus': '奖金',
    'investment': '投资收益'
}

# 移除模拟数据
# mock_transactions: List[Dict[str, Any]] = [...]

@router.get("")
async def get_all_transactions(
    account_id: int = Query(None, description="按账户ID过滤交易记录"),
    start_date: str = Query(None, description="开始日期 (ISO格式)"),
    end_date: str = Query(None, description="结束日期 (ISO格式)"),
    transaction_type: str = Query(None, description="交易类型"),
    keyword: str = Query(None, description="关键词搜索"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """
    获取当前用户的所有交易记录.
    可以通过account_id参数过滤特定账户的交易记录.
    从数据库中获取真实数据.
    """
    try:
        # 创建查询语句，添加用户ID过滤
        # 使用join来预加载category和account信息，避免后续单独查询
        stmt = select(Transaction).filter(
            Transaction.user_id == current_user.id
        ).options(
            # 使用selectinload策略加载相关的category和account数据
            selectinload(Transaction.category),
            selectinload(Transaction.account)
        )
        
        # 如果提供了account_id参数，添加账户ID过滤条件
        if account_id is not None:
            logger.info(f"按账户ID过滤交易记录: {account_id}")
            stmt = stmt.filter(Transaction.account_id == account_id)
            
        # 处理日期范围筛选
        if start_date:
            try:
                # 解析多种可能的日期格式
                try:
                    # 尝试解析ISO格式日期 "YYYY-MM-DDThh:mm:ss"
                    if 'T' in start_date:
                        start_date_obj = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                    # 尝试解析纯日期格式 "YYYY-MM-DD"
                    else:
                        start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
                except ValueError:
                    # 尝试解析带时间的格式 "YYYY-MM-DD hh:mm:ss"
                    if ' ' in start_date:
                        start_date_obj = datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S")
                    else:
                        # 如果都失败，使用当天开始时间
                        start_date_obj = datetime.strptime(start_date.split('T')[0], "%Y-%m-%d")
                        
                logger.info(f"按开始日期过滤: {start_date_obj}")
                stmt = stmt.filter(Transaction.transaction_date >= start_date_obj)
            except Exception as e:
                logger.warning(f"无效的开始日期格式: {start_date}, 错误: {str(e)}")
                
        if end_date:
            try:
                # 解析多种可能的日期格式
                try:
                    # 尝试解析ISO格式日期 "YYYY-MM-DDThh:mm:ss"
                    if 'T' in end_date:
                        end_date_obj = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                    # 尝试解析纯日期格式 "YYYY-MM-DD"
                    else:
                        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
                        # 如果只有日期，设置为当天结束时间
                        end_date_obj = end_date_obj.replace(hour=23, minute=59, second=59)
                except ValueError:
                    # 尝试解析带时间的格式 "YYYY-MM-DD hh:mm:ss"
                    if ' ' in end_date:
                        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")
                    else:
                        # 如果都失败，使用当天结束时间
                        end_date_obj = datetime.strptime(end_date.split('T')[0], "%Y-%m-%d")
                        end_date_obj = end_date_obj.replace(hour=23, minute=59, second=59)
                        
                logger.info(f"按结束日期过滤: {end_date_obj}")
                stmt = stmt.filter(Transaction.transaction_date <= end_date_obj)
            except Exception as e:
                logger.warning(f"无效的结束日期格式: {end_date}, 错误: {str(e)}")
                
        # 处理交易类型筛选
        if transaction_type:
            logger.info(f"按交易类型过滤: {transaction_type}")
            stmt = stmt.filter(Transaction.transaction_type == transaction_type)
            
        # 处理关键词搜索
        if keyword:
            search_pattern = f"%{keyword}%"
            logger.info(f"按关键词搜索: {keyword}")
            stmt = stmt.filter(Transaction.description.ilike(search_pattern))
            
        # 添加排序
        stmt = stmt.order_by(Transaction.transaction_date.desc())
        
        # 执行查询
        result = await db.execute(stmt)
        transactions = result.scalars().all()
        
        # 转换数据格式
        transaction_list = []
        for transaction in transactions:
            category_name = None
            if transaction.category is not None:
                category_name = transaction.category.name

            account_name = None
            if transaction.account is not None:
                account_name = transaction.account.account_name

            transaction_dict = {
                "id": transaction.id,
                "date": transaction.transaction_date.strftime('%Y-%m-%d %H:%M:%S'),
                "type": transaction.transaction_type,
                "amount": float(transaction.amount),
                "category": category_name,  # 使用category_name代替直接访问transaction.category
                "category_name": category_name,  # 添加category_name字段
                "category_id": transaction.category_id,
                "account_id": transaction.account_id,
                "account_name": account_name,  # 添加account_name字段
                "description": transaction.description,
                "from_account_id": transaction.from_account_id if transaction.transaction_type == 'transfer' else None,
                "to_account_id": transaction.to_account_id if transaction.transaction_type == 'transfer' else None,
                "created_at": transaction.created_at.strftime('%Y-%m-%d %H:%M:%S') if transaction.created_at else None
            }
            transaction_list.append(transaction_dict)
            
        return transaction_list
    except Exception as e:
        logger.error(f"获取交易记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取交易记录失败: {str(e)}")

@router.get("/recent")
async def get_recent_transactions(
    limit: int = Query(5, ge=1, le=20),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """
    获取当前用户的近期交易记录.
    """
    try:
        # 创建查询语句，添加用户ID过滤
        stmt = select(Transaction).filter(
            Transaction.user_id == current_user.id
        ).options(
            selectinload(Transaction.category),
            selectinload(Transaction.account)
        ).order_by(Transaction.transaction_date.desc()).limit(limit)
        
        # 执行查询
        result = await db.execute(stmt)
        transactions = result.scalars().all()
        
        # 转换数据格式
        transaction_list = []
        for transaction in transactions:
            category_name = None
            if transaction.category is not None:
                category_name = transaction.category.name

            account_name = None
            if transaction.account is not None:
                account_name = transaction.account.account_name

            transaction_dict = {
                "id": transaction.id,
                "date": transaction.transaction_date.strftime('%Y-%m-%d %H:%M:%S'),
                "type": transaction.transaction_type,
                "amount": float(transaction.amount),
                "category": category_name,
                "category_name": category_name,  # 添加category_name字段
                "category_id": transaction.category_id,
                "account_id": transaction.account_id,
                "account_name": account_name,  # 添加account_name字段
                "description": transaction.description,
                "from_account_id": transaction.from_account_id if transaction.transaction_type == 'transfer' else None,
                "to_account_id": transaction.to_account_id if transaction.transaction_type == 'transfer' else None,
                "created_at": transaction.created_at.strftime('%Y-%m-%d %H:%M:%S') if transaction.created_at else None
            }
            transaction_list.append(transaction_dict)
            
        return transaction_list
    except Exception as e:
        logger.error(f"获取近期交易记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取近期交易记录失败: {str(e)}")

@router.post("")
async def create_transaction(
    transaction_data: Dict[str, Any] = Body(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    创建一笔新交易记录
    """
    try:
        logger.info(f"收到创建交易请求: {transaction_data}")
        
        # 确保必需字段存在
        required_fields = ['amount', 'transaction_type']
        
        # 处理type字段（完全移除，防止传入不存在的列名）
        if 'type' in transaction_data:
            if 'transaction_type' not in transaction_data:
                transaction_data['transaction_type'] = transaction_data['type']
            # 一定要删除type字段，因为数据库表中没有这个列
            transaction_data.pop('type')
        
        # 检查其他必须字段
        for field in required_fields:
            if field not in transaction_data:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")
        
        # 处理category字段 (从name转为id)
        if 'category' in transaction_data:
            category_name = transaction_data.pop('category')
            
            # 如果是英文名称，使用映射转为中文
            if category_name in CATEGORY_NAME_MAP:
                mapped_name = CATEGORY_NAME_MAP[category_name]
                logger.info(f"分类名称映射: {category_name} -> {mapped_name}")
                category_name = mapped_name
                
            # 查找对应的category_id
            category_stmt = select(Category).where(
                and_(
                    or_(
                        Category.user_id == current_user.id,
                        Category.user_id.is_(None)
                    ),
                    Category.name == category_name
                )
            ).limit(1)  # 只取第一个匹配的结果
            result = await db.execute(category_stmt)
            category = result.scalar_one_or_none()
            
            if category:
                transaction_data['category_id'] = category.id
                logger.info(f"找到分类 '{category_name}', ID为 {category.id}")
            else:
                # 如果找不到对应的分类，记录警告但不阻止创建交易
                logger.warning(f"找不到名称为 '{category_name}' 的分类，交易将不关联任何分类")
        
        # 获取交易类型
        transaction_type = transaction_data['transaction_type']
        
        # 根据交易类型检查必需字段
        if transaction_type == 'transfer':
            # 转账交易需要from_account_id和to_account_id
            if 'from_account_id' not in transaction_data:
                raise HTTPException(status_code=400, detail="转账交易缺少来源账户ID (from_account_id)")
            if 'to_account_id' not in transaction_data:
                raise HTTPException(status_code=400, detail="转账交易缺少目标账户ID (to_account_id)")
            # 对于转账，account_id等于from_account_id
            transaction_data['account_id'] = transaction_data['from_account_id']
        else:
            # 非转账交易需要account_id
            if 'account_id' not in transaction_data:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: account_id")
        
        # 处理日期字段
        if 'date' in transaction_data:
            # 如果前端传入的是字符串格式的日期，转换为datetime对象
            if isinstance(transaction_data['date'], str):
                try:
                    # 尝试解析完整的日期时间格式 "YYYY-MM-DD HH:MM:SS"
                    if ' ' in transaction_data['date']:
                        transaction_data['transaction_date'] = datetime.strptime(transaction_data['date'], "%Y-%m-%d %H:%M:%S")
                    else:
                        # 兼容纯日期格式 "YYYY-MM-DD"
                        transaction_data['transaction_date'] = datetime.strptime(transaction_data['date'], "%Y-%m-%d")
                except ValueError:
                    # 尝试其他常见日期格式
                    try:
                        # ISO格式 "YYYY-MM-DDTHH:MM:SS"
                        if 'T' in transaction_data['date']:
                            transaction_data['transaction_date'] = datetime.strptime(transaction_data['date'].split('.')[0], "%Y-%m-%dT%H:%M:%S")
                        else:
                            # 如果所有格式都失败，使用当前时间
                            transaction_data['transaction_date'] = datetime.now()
                    except ValueError:
                        transaction_data['transaction_date'] = datetime.now()
                # 删除原始的date字段，因为数据库使用的是transaction_date
                del transaction_data['date']
            else:
                transaction_data['transaction_date'] = datetime.now()
        else:
            transaction_data['transaction_date'] = datetime.now()
        
        # 添加必要的时间戳
        transaction_data['created_at'] = datetime.now()
        transaction_data['updated_at'] = datetime.now()
        
        # 添加当前用户ID
        transaction_data['user_id'] = current_user.id
        
        # 获取账户ID和交易类型
        account_id = transaction_data['account_id']
        
        # 转换金额为Decimal类型
        amount = Decimal(str(transaction_data['amount']))
        transaction_data['amount'] = amount
        
        # 首先检查账户是否存在且属于当前用户
        account_stmt = select(Account).where(
            Account.id == account_id, 
            Account.user_id == current_user.id
        )
        account_result = await db.execute(account_stmt)
        account = account_result.scalar_one_or_none()
        
        if not account:
            raise HTTPException(status_code=404, detail=f"账户不存在或不属于当前用户: {account_id}")
        
        # 构建插入语句
        stmt = insert(Transaction).values(**transaction_data)
        
        # 执行插入
        result = await db.execute(stmt)
        
        # 更新账户余额
        if transaction_type == 'income':
            # 收入处理逻辑
            if account.account_type == 'debt':
                # 负债账户：收入减少负债（余额减少，表示还款）
                # 负债账户余额为正数表示欠款金额，收入使其减少
                new_balance = account.current_balance - amount
            else:
                # 资产账户：收入增加资产（余额增加）
                new_balance = account.current_balance + amount
            balance_update = update(Account).where(Account.id == account_id).values(current_balance=new_balance)
            await db.execute(balance_update)
        elif transaction_type == 'expense':
            # 支出处理逻辑
            if account.account_type == 'debt':
                # 负债账户：支出增加负债（余额增加，表示借款）
                # 负债账户余额为正数表示欠款金额，支出使其增加
                new_balance = account.current_balance + amount
            else:
                # 资产账户：支出减少资产（余额减少）
                new_balance = account.current_balance - amount
            balance_update = update(Account).where(Account.id == account_id).values(current_balance=new_balance)
            await db.execute(balance_update)
        elif transaction_type == 'transfer':
            # 转账减少源账户余额，增加目标账户余额
            to_account_id = transaction_data.get('to_account_id')
            if to_account_id:
                # 检查目标账户是否存在且属于当前用户
                to_account_stmt = select(Account).where(
                    Account.id == to_account_id,
                    Account.user_id == current_user.id
                )
                to_account_result = await db.execute(to_account_stmt)
                to_account = to_account_result.scalar_one_or_none()
                
                if to_account:
                    # 详细记录转账前的状态
                    logger.info(f"转账前状态 - 源账户(ID:{account_id}, 类型:{account.account_type}): {account.current_balance}, "
                               f"目标账户(ID:{to_account_id}, 类型:{to_account.account_type}): {to_account.current_balance}")
                    
                    # 源账户减少余额
                    new_from_balance = account.current_balance - amount
                    logger.info(f"源账户新余额: {new_from_balance} (减少了 {amount})")
                    from_balance_update = update(Account).where(Account.id == account_id).values(current_balance=new_from_balance)
                    await db.execute(from_balance_update)
                    
                    # 目标账户处理
                    if to_account.account_type == 'debt':
                        # 负债账户 - 转入资金减少负债（余额减少，表示还款）
                        # 负债账户余额为正数表示欠款金额，转入资金使其减少
                        new_to_balance = to_account.current_balance - amount
                        logger.info(f"目标账户是负债账户，负债从 {to_account.current_balance} 减少到 {new_to_balance} (减少了 {amount})")
                    else:
                        # 资产账户 - 增加资产（余额增加）
                        new_to_balance = to_account.current_balance + amount
                        logger.info(f"目标账户是资产账户，余额从 {to_account.current_balance} 增加到 {new_to_balance} (增加了 {amount})")
                    
                    to_balance_update = update(Account).where(Account.id == to_account_id).values(current_balance=new_to_balance)
                    await db.execute(to_balance_update)
                    
                    # 记录转账后的状态
                    logger.info(f"转账后状态 - 源账户: {new_from_balance}, 目标账户: {new_to_balance}")
                else:
                    # 如果目标账户不存在或不属于当前用户，抛出错误
                    raise HTTPException(status_code=404, detail=f"目标账户不存在或不属于当前用户: {to_account_id}")
            else:
                # 如果没有目标账户，仍然减少源账户余额
                new_balance = account.current_balance - amount
                balance_update = update(Account).where(Account.id == account_id).values(current_balance=new_balance)
                await db.execute(balance_update)
        
        # 提交事务
        await db.commit()
        
        # 返回成功消息
        return {"message": "交易创建成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建交易时出错: {str(e)}")
        # 回滚事务
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"创建交易失败: {str(e)}")

@router.put("/{transaction_id}")
async def update_transaction(
    transaction_id: int = Path(..., description="要更新的交易ID"),
    transaction_data: Dict[str, Any] = Body(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    更新交易记录，确保只能更新当前用户的交易
    """
    try:
        logger.info(f"收到更新交易请求: ID={transaction_id}, 数据={transaction_data}")
        
        # 1. 获取原交易记录并验证所有权
        query = select(Transaction).where(
            Transaction.id == transaction_id,
            Transaction.user_id == current_user.id
        )
        result = await db.execute(query)
        transaction = result.scalar_one_or_none()
        
        if not transaction:
            raise HTTPException(status_code=404, detail=f"交易记录不存在或不属于当前用户: {transaction_id}")
        
        old_account_id = transaction.account_id
        old_type = transaction.transaction_type
        old_amount = transaction.amount
        
        # 2. 获取旧账户并验证所有权
        query = select(Account).where(
            Account.id == old_account_id,
            Account.user_id == current_user.id
        )
        result = await db.execute(query)
        account = result.scalar_one_or_none()
        
        if not account:
            raise HTTPException(status_code=404, detail=f"账户不存在或不属于当前用户: {old_account_id}")
        
        old_balance = account.current_balance
        
        # 3. 处理传入的数据
        # 处理日期字段
        if 'date' in transaction_data:
            date_str = transaction_data.pop('date')
            if isinstance(date_str, str):
                try:
                    # 尝试解析完整的日期时间格式
                    if ' ' in date_str:
                        transaction_data['transaction_date'] = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
                    elif 'T' in date_str:
                        # ISO格式
                        transaction_data['transaction_date'] = datetime.strptime(date_str.split('.')[0], "%Y-%m-%dT%H:%M:%S")
                    else:
                        # 纯日期格式
                        transaction_data['transaction_date'] = datetime.strptime(date_str, "%Y-%m-%d")
                except ValueError:
                    # 如果解析失败，保持原样
                    logger.warning(f"无法解析日期格式: {date_str}")
                    pass
        
        # 处理交易类型
        if 'type' in transaction_data:
            if 'transaction_type' not in transaction_data:
                transaction_data['transaction_type'] = transaction_data['type']
            # 一定要删除type字段，因为数据库表中没有这个列
            transaction_data.pop('type')
        
        # 处理分类字段 (从name转为id)
        if 'category' in transaction_data:
            category_name = transaction_data.pop('category')
            
            # 如果是英文名称，使用映射转为中文
            if category_name in CATEGORY_NAME_MAP:
                mapped_name = CATEGORY_NAME_MAP[category_name]
                logger.info(f"分类名称映射: {category_name} -> {mapped_name}")
                category_name = mapped_name
            
            # 查找对应的category_id
            category_stmt = select(Category).where(
                and_(
                    or_(
                        Category.user_id == current_user.id,
                        Category.user_id.is_(None)
                    ),
                    Category.name == category_name
                )
            ).limit(1)  # 只取第一个匹配的结果
            result = await db.execute(category_stmt)
            category = result.scalar_one_or_none()
            
            if category:
                transaction_data['category_id'] = category.id
                logger.info(f"找到分类 '{category_name}', ID为 {category.id}")
            else:
                # 如果找不到对应的分类，记录警告但不阻止更新交易
                logger.warning(f"找不到名称为 '{category_name}' 的分类，交易将不关联任何分类")
                
        # 获取新的账户ID和交易类型
        new_account_id = transaction_data.get('account_id', old_account_id)
        new_type = transaction_data.get('transaction_type', old_type)
        
        # 处理金额字段
        new_amount = old_amount  # 默认使用旧金额
        if 'amount' in transaction_data:
            # 确保是字符串，避免浮点精度问题
            new_amount = Decimal(str(transaction_data['amount']))
            transaction_data['amount'] = new_amount
        
        # 4. 更新时间戳
        transaction_data['updated_at'] = datetime.now()
        
        # 5. 验证新账户所有权（如果变更了账户）
        if new_account_id != old_account_id:
            query = select(Account).where(
                Account.id == new_account_id,
                Account.user_id == current_user.id
            )
            result = await db.execute(query)
            new_account = result.scalar_one_or_none()
            
            if not new_account:
                raise HTTPException(status_code=404, detail=f"新账户不存在或不属于当前用户: {new_account_id}")
        
        # 5. 开始更新数据库
        # 5.1 撤销旧交易对账户余额的影响
        if old_type == 'income':
            if account.account_type == 'debt':
                # 撤销负债账户收入：增加负债（撤销还款）
                account.current_balance += old_amount
            else:
                # 撤销资产账户收入：减少余额
                account.current_balance -= old_amount
            db.add(account)
        elif old_type == 'expense':
            if account.account_type == 'debt':
                # 撤销负债账户支出：减少负债（撤销借款）
                account.current_balance -= old_amount
            else:
                # 撤销资产账户支出：增加余额
                account.current_balance += old_amount
            db.add(account)
        
        # 5.2 更新交易记录
        for key, value in transaction_data.items():
            setattr(transaction, key, value)
        db.add(transaction)
        
        # 5.3 应用新交易的影响
        if new_account_id == old_account_id:
            # 同一账户的新影响
            if new_type == 'income':
                if account.account_type == 'debt':
                    # 负债账户：收入减少负债
                    account.current_balance -= new_amount
                else:
                    # 资产账户：收入增加余额
                    account.current_balance += new_amount
                db.add(account)
            elif new_type == 'expense':
                if account.account_type == 'debt':
                    # 负债账户：支出增加负债
                    account.current_balance += new_amount
                else:
                    # 资产账户：支出减少余额
                    account.current_balance -= new_amount
                db.add(account)
        else:
            # 不同账户，需要分别处理
            # 获取新账户
            query = select(Account).where(
                Account.id == new_account_id,
                Account.user_id == current_user.id
            )
            result = await db.execute(query)
            new_account = result.scalar_one_or_none()

            if new_type == 'income':
                if new_account.account_type == 'debt':
                    # 负债账户：收入减少负债
                    new_account.current_balance -= new_amount
                else:
                    # 资产账户：收入增加余额
                    new_account.current_balance += new_amount
                db.add(new_account)
            elif new_type == 'expense':
                if new_account.account_type == 'debt':
                    # 负债账户：支出增加负债
                    new_account.current_balance += new_amount
                else:
                    # 资产账户：支出减少余额
                    new_account.current_balance -= new_amount
                db.add(new_account)
        
        # 6. 提交事务
        await db.commit()
        
        return {"message": "交易更新成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新交易时出错: {str(e)}")
        # 回滚事务
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"更新交易失败: {str(e)}")

@router.delete("/{transaction_id}")
async def delete_transaction(
    transaction_id: int = Path(..., description="要删除的交易ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    删除交易记录，确保只能删除当前用户的交易
    """
    try:
        logger.info(f"收到删除交易请求: ID={transaction_id}")
        
        # 1. 获取交易记录并验证所有权
        query = select(Transaction).where(
            Transaction.id == transaction_id,
            Transaction.user_id == current_user.id
        )
        result = await db.execute(query)
        transaction = result.scalar_one_or_none()
        
        if not transaction:
            raise HTTPException(status_code=404, detail=f"交易记录不存在或不属于当前用户: {transaction_id}")
        
        account_id = transaction.account_id
        transaction_type = transaction.transaction_type
        amount = transaction.amount
        
        # 2. 获取账户并验证所有权
        query = select(Account).where(
            Account.id == account_id,
            Account.user_id == current_user.id
        )
        result = await db.execute(query)
        account = result.scalar_one_or_none()
        
        if not account:
            raise HTTPException(status_code=404, detail=f"账户不存在或不属于当前用户: {account_id}")
        
        # 3. 更新账户余额
        if transaction_type == 'income':
            if account.account_type == 'debt':
                # 删除负债账户收入交易，增加负债（撤销还款）
                account.current_balance += amount
                logger.info(f"删除负债账户收入交易，增加负债 {amount}，当前余额: {account.current_balance}")
            else:
                # 删除资产账户收入交易，减少账户余额
                account.current_balance -= amount
                logger.info(f"删除资产账户收入交易，减少账户余额 {amount}，当前余额: {account.current_balance}")
            db.add(account)
        elif transaction_type == 'expense':
            if account.account_type == 'debt':
                # 删除负债账户支出交易，减少负债（撤销借款）
                account.current_balance -= amount
                logger.info(f"删除负债账户支出交易，减少负债 {amount}，当前余额: {account.current_balance}")
            else:
                # 删除资产账户支出交易，增加账户余额
                account.current_balance += amount
                logger.info(f"删除资产账户支出交易，增加账户余额 {amount}，当前余额: {account.current_balance}")
            db.add(account)
        elif transaction_type == 'transfer':
            # 处理转账交易的删除，需要恢复两个账户的余额
            from_account_id = transaction.from_account_id
            to_account_id = transaction.to_account_id
            
            if from_account_id is not None and to_account_id is not None:
                # 获取转出账户
                from_query = select(Account).where(
                    Account.id == from_account_id,
                    Account.user_id == current_user.id
                )
                from_result = await db.execute(from_query)
                from_account = from_result.scalar_one_or_none()
                
                # 获取转入账户
                to_query = select(Account).where(
                    Account.id == to_account_id,
                    Account.user_id == current_user.id
                )
                to_result = await db.execute(to_query)
                to_account = to_result.scalar_one_or_none()
                
                # 如果两个账户都存在，恢复余额
                if from_account and to_account:
                    # 转出账户增加余额（取消转出）
                    from_account.current_balance += amount
                    db.add(from_account)
                    logger.info(f"删除转账交易，恢复转出账户余额 +{amount}，当前余额: {from_account.current_balance}")
                    
                    # 转入账户减少余额（取消转入）
                    to_account.current_balance -= amount
                    db.add(to_account)
                    logger.info(f"删除转账交易，恢复转入账户余额 -{amount}，当前余额: {to_account.current_balance}")
                else:
                    logger.warning(f"转账账户不完整，无法完全恢复余额: from={from_account_id}, to={to_account_id}")
            else:
                logger.warning(f"转账记录不完整，无法恢复账户余额: from={from_account_id}, to={to_account_id}")
        
        # 4. 删除交易记录
        await db.delete(transaction)
        
        # 5. 提交事务
        await db.commit()
        
        return {"message": "交易删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除交易时出错: {str(e)}")
        # 回滚事务
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"删除交易失败: {str(e)}") 

@router.get("/by-account/{account_id}")
async def get_account_transactions(
    account_id: int = Path(..., description="账户ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """
    获取特定账户的所有交易记录.
    """
    try:
        # 首先检查账户是否存在且属于当前用户
        account_stmt = select(Account).where(
            Account.id == account_id, 
            Account.user_id == current_user.id
        )
        account_result = await db.execute(account_stmt)
        account = account_result.scalar_one_or_none()
        
        if not account:
            raise HTTPException(status_code=404, detail=f"账户不存在或不属于当前用户: {account_id}")
        
        # 创建查询语句，添加账户ID过滤
        stmt = select(Transaction).filter(
            Transaction.account_id == account_id,
            Transaction.user_id == current_user.id
        ).options(
            selectinload(Transaction.category),
            selectinload(Transaction.account)
        ).order_by(Transaction.transaction_date.desc())
        
        # 执行查询
        result = await db.execute(stmt)
        transactions = result.scalars().all()
        
        # 转换数据格式
        transaction_list = []
        for transaction in transactions:
            category_name = None
            if transaction.category is not None:
                category_name = transaction.category.name

            account_name = None
            if transaction.account is not None:
                account_name = transaction.account.account_name

            transaction_dict = {
                "id": transaction.id,
                "date": transaction.transaction_date.strftime('%Y-%m-%d %H:%M:%S'),
                "type": transaction.transaction_type,
                "amount": float(transaction.amount),
                "category": category_name,
                "category_name": category_name,  # 添加category_name字段
                "category_id": transaction.category_id,
                "account_id": transaction.account_id,
                "account_name": account_name,  # 添加account_name字段
                "description": transaction.description,
                "from_account_id": transaction.from_account_id if transaction.transaction_type == 'transfer' else None,
                "to_account_id": transaction.to_account_id if transaction.transaction_type == 'transfer' else None,
                "created_at": transaction.created_at.strftime('%Y-%m-%d %H:%M:%S') if transaction.created_at else None
            }
            transaction_list.append(transaction_dict)
            
        return transaction_list
    except HTTPException as he:
        # 直接重抛HTTP异常
        raise he
    except Exception as e:
        logger.error(f"获取账户交易记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取账户交易记录失败: {str(e)}") 