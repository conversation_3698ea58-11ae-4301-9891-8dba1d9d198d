import axios from 'axios'
import MockAdapter from 'axios-mock-adapter'
import { 
  mockUserData, 
  mockAccountData, 
  mockTransactionData, 
  mockBudgetData,
  mockAnalyticsData 
} from './mockData'

let mock: MockAdapter | null = null;

// 暴露mock函数，可以在main.ts中调用
export default function setupMock() {
  console.log('Mock API 已启用')
  
  // 创建一个新的mock adapter实例
  mock = new MockAdapter(axios, { delayResponse: 500 })

  // API路径前缀
  const apiPrefix = '/api/v1';

  // 根路径
  mock.onGet(apiPrefix).reply(200, {
    message: 'Welcome to 金融账本系统 API - Mock版本'
  });

  // 用户相关接口 - OAuth2格式
  mock.onPost(`${apiPrefix}/users/login/access-token`).reply(config => {
    // 解析表单数据
    const formData = new URLSearchParams(config.data);
    const username = formData.get('username');
    const password = formData.get('password');
    
    if (username === 'admin' && password === '123456') {
      return [200, {
        access_token: 'mock-token-oauth2',
        token_type: 'bearer'
      }]
    }
    return [401, {
      detail: '用户名或密码错误'
    }]
  });

  // 用户注册
  mock.onPost(`${apiPrefix}/users/register`).reply(config => {
    console.log('Mock服务收到注册请求:', config.url, config.data);
    try {
      const userData = JSON.parse(config.data);
      console.log('Mock服务解析的用户数据:', userData);
      
      // 模拟成功响应，返回用户对象
      return [201, {
        id: 2,
        username: userData.username,
        email: userData.email,
        phone: userData.phone || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }];
    } catch (error) {
      console.error('Mock服务处理注册请求时出错:', error);
      return [400, {
        detail: '无效的请求数据'
      }];
    }
  });

  // 获取用户信息
  mock.onGet(`${apiPrefix}/users/me`).reply(200, {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    phone: null,
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z'
  });

  // 登出
  mock.onPost(`${apiPrefix}/users/logout`).reply(200, {
    code: 0,
    message: 'Successfully logged out',
    data: null
  });

  // 账户相关接口
  mock.onGet(`${apiPrefix}/accounts`).reply(200, {
    code: 0,
    message: 'success',
    data: mockAccountData.accounts
  });

  mock.onGet(new RegExp(`${apiPrefix}/accounts/\\d+`)).reply(config => {
    const id = parseInt(config.url?.split('/').pop() || '0')
    const account = mockAccountData.accounts.find(a => a.id === id)
    if (account) {
      return [200, {
        code: 0,
        message: 'success',
        data: account
      }]
    }
    return [404, {
      code: 404,
      message: '账户不存在',
      data: null
    }]
  })

  mock.onPost(`${apiPrefix}/accounts`).reply(config => {
    const newAccount = JSON.parse(config.data)
    return [200, {
      code: 0,
      message: '创建成功',
      data: {
        id: mockAccountData.accounts.length + 1,
        ...newAccount,
        userId: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    }]
  })

  // 交易相关接口
  mock.onGet(`${apiPrefix}/transactions`).reply(config => {
    const { page = 1, pageSize = 10 } = config.params || {}
    const total = mockTransactionData.transactions.length
    const list = mockTransactionData.transactions.slice((page - 1) * pageSize, page * pageSize)
    
    return [200, {
      code: 0,
      message: 'success',
      data: {
        list,
        total,
        page,
        pageSize
      }
    }]
  })

  mock.onGet(`${apiPrefix}/transactions/all`).reply(200, {
    code: 0,
    message: 'success',
    data: mockTransactionData.transactions
  })

  mock.onGet(`${apiPrefix}/transactions/recent`).reply(config => {
    const { limit = 5 } = config.params || {}
    const recentTransactions = mockTransactionData.transactions
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, limit)
      
    return [200, {
      code: 0,
      message: 'success',
      data: recentTransactions
    }]
  })

  // 预算相关接口
  mock.onGet(`${apiPrefix}/budgets`).reply(config => {
    const { month } = config.params || {}
    if (month) {
      const filteredBudgets = mockBudgetData.budgets.filter(b => b.month === month)
      return [200, {
        code: 0,
        message: 'success',
        data: filteredBudgets
      }]
    }
    
    return [200, {
      code: 0,
      message: 'success',
      data: mockBudgetData.budgets
    }]
  })

  // 分析相关接口
  mock.onGet(`${apiPrefix}/analytics/dashboard`).reply(200, {
    code: 0,
    message: 'success',
    data: {
      totalAssets: 89340,
      totalLiabilities: 12450,
      netWorth: 76890,
      assetGrowth: 0.041,
      recentTransactions: mockTransactionData.transactions.slice(0, 5)
    }
  })

  mock.onGet(`${apiPrefix}/analytics/data`).reply(200, {
    code: 0,
    message: 'success',
    data: mockAnalyticsData
  })
}

// 用于关闭mock服务的函数
export function resetMock() {
  if (mock) {
    mock.restore();
    mock = null;
    console.log('Mock API 已禁用');
  }
} 