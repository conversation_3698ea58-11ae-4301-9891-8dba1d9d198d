/* TypeScript类型声明文件 */

// 声明Vue组件
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 声明Element Plus语言包
declare module 'element-plus/dist/locale/zh-cn.mjs' {
  const locale: any
  export default locale
}

// 声明图片模块
declare module '*.png'
declare module '*.jpg'
declare module '*.jpeg'
declare module '*.gif'
declare module '*.svg' 