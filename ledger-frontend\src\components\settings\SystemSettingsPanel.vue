<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElForm, ElFormItem, ElSelect, ElOption, ElSwitch, ElButton, ElMessage } from 'element-plus'
import { updateUserSettings } from '../../api/user'
import type { UserSettings } from '../../api/types'
import darkModeDetector from '../../plugins/darkModeDetector'

const props = defineProps<{
  settings: Partial<UserSettings>
}>()

const emit = defineEmits(['settings-updated'])

// 组件状态
const isLoading = ref(false)

// 表单数据
const settingsForm = reactive<Partial<UserSettings>>({
  language: props.settings.language || 'zh',
  currency: props.settings.currency || 'CNY',
  dark_mode: props.settings.dark_mode || false,
  notifications: props.settings.notifications || true,
  auto_backup: props.settings.auto_backup || false
})

// 语言选项
const languageOptions = [
  { label: '简体中文', value: 'zh' },
  { label: 'English', value: 'en' }
]

// 货币选项
const currencyOptions = [
  { label: '人民币 (¥)', value: 'CNY' },
  { label: '美元 ($)', value: 'USD' },
  { label: '欧元 (€)', value: 'EUR' },
  { label: '英镑 (£)', value: 'GBP' },
  { label: '日元 (¥)', value: 'JPY' }
]

// 保存设置
const saveSettings = async () => {
  try {
    isLoading.value = true
    
    // 调用API保存设置
    const updatedSettings = await updateUserSettings(settingsForm)
    
    // 确保界面数据与服务器返回的数据同步
    settingsForm.language = updatedSettings.language || 'zh'
    settingsForm.currency = updatedSettings.currency || 'CNY'
    settingsForm.dark_mode = updatedSettings.dark_mode || false
    settingsForm.notifications = updatedSettings.notifications || true
    settingsForm.auto_backup = updatedSettings.auto_backup || false
    
    // 应用深色模式设置
    applyDarkMode(settingsForm.dark_mode)
    
    ElMessage({
      type: 'success',
      message: '系统设置已保存',
      duration: 2000,
      showClose: true
    })
    
    // 通知父组件设置已更新
    emit('settings-updated', updatedSettings)
  } catch (error) {
    console.error('保存系统设置失败', error)
    ElMessage({
      type: 'error',
      message: '保存系统设置失败，请检查网络连接或刷新页面重试',
      duration: 5000,
      showClose: true
    })
  } finally {
    isLoading.value = false
  }
}

// 应用深色模式
const applyDarkMode = (isDark: boolean | undefined) => {
  const darkMode = isDark === undefined ? false : isDark;
  document.documentElement.classList.toggle('dark-mode', darkMode);
  document.body.classList.toggle('dark-mode', darkMode);
  // 保存到localStorage
  localStorage.setItem('darkMode', String(darkMode));
}

// 手动更新图表背景色
const updateChartsBackground = (isDark: boolean) => {
  const chartContainers = document.querySelectorAll('.chart-container')
  chartContainers.forEach(container => {
    if (container instanceof HTMLElement) {
      container.style.backgroundColor = isDark ? '#282838' : '#fff'
      
      // 查找图表内部的canvas元素
      const canvas = container.querySelector('canvas')
      if (canvas instanceof HTMLElement) {
        canvas.style.backgroundColor = isDark ? '#282838' : '#fff'
      }
    }
  })

  // 查找所有ECharts实例
  const echartsElements = document.querySelectorAll('[_echarts_instance_]')
  echartsElements.forEach(element => {
    if (element instanceof HTMLElement) {
      element.style.backgroundColor = isDark ? '#282838' : '#fff'
    }
  })
}

// 监听深色模式变化
const toggleDarkMode = (value: boolean | string | number) => {
  // 将值转换为布尔类型
  const isDark = Boolean(value);
  settingsForm.dark_mode = isDark;
  applyDarkMode(isDark);
}

// 初始化时应用深色模式
onMounted(() => {
  // 从localStorage读取深色模式设置，优先级高于props
  const darkModeSetting = localStorage.getItem('darkMode')
  
  // 如果localStorage中有设置，使用该设置
  if (darkModeSetting !== null) {
    settingsForm.dark_mode = darkModeSetting === 'true'
  }
  
  // 应用初始深色模式设置
  applyDarkMode(settingsForm.dark_mode)
})

// 监听深色模式变化
watch(() => settingsForm.dark_mode, (newValue) => {
  applyDarkMode(newValue)
})
</script>

<template>
  <div class="system-settings-panel">
    <el-form :model="settingsForm" label-position="top">
      <el-form-item label="语言">
        <el-select v-model="settingsForm.language" style="width: 100%">
          <el-option 
            v-for="option in languageOptions" 
            :key="option.value" 
            :label="option.label" 
            :value="option.value" 
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="货币单位">
        <el-select v-model="settingsForm.currency" style="width: 100%">
          <el-option 
            v-for="option in currencyOptions" 
            :key="option.value" 
            :label="option.label" 
            :value="option.value" 
          />
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <div class="switch-item">
          <span>深色模式</span>
          <el-switch 
            v-model="settingsForm.dark_mode" 
            @change="toggleDarkMode"
          />
        </div>
      </el-form-item>
      
      <el-form-item>
        <div class="switch-item">
          <span>开启通知</span>
          <el-switch v-model="settingsForm.notifications" />
        </div>
      </el-form-item>
      
      <el-form-item>
        <div class="switch-item">
          <span>自动备份</span>
          <el-switch v-model="settingsForm.auto_backup" />
        </div>
      </el-form-item>
      
      <el-form-item>
        <el-button 
          type="primary" 
          @click="saveSettings" 
          :loading="isLoading"
        >
          保存设置
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.system-settings-panel {
  width: 100%;
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}
</style> 