// pages/accounts/list.js
const api = require('../../utils/api.js')

Page({
  data: {
    accounts: [],
    totalAssets: '0.00',
    totalCash: '0.00',
    totalBank: '0.00',
    totalInvestment: '0.00',
    totalDebt: '0.00',
    totalOther: '0.00',
    loading: false,
    hideAmounts: false
  },

  onLoad() {
    console.log('账户管理页面加载')
    this.loadHideAmountsState()
    this.loadAccounts()
  },

  onShow() {
    // 从其他页面返回时刷新数据
    this.loadAccounts()
  },

  onPullDownRefresh() {
    this.loadAccounts()
  },

  // 加载账户列表
  async loadAccounts() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const accounts = await api.accounts.getList()

      // 处理账户数据 - 根据后端实际返回的字段调整
      const processedAccounts = accounts.map(account => ({
        ...account,
        icon: this.getAccountIcon(account.account_type),
        balanceText: this.formatAmount(account.current_balance),
        type_display: this.getAccountTypeDisplay(account.account_type)
      }))

      // 计算各类型总额
      let totalCash = 0
      let totalBank = 0
      let totalInvestment = 0
      let totalDebt = 0

      processedAccounts.forEach(account => {
        const balance = parseFloat(account.current_balance) || 0
        switch (account.account_type) {
          case 'bank':
            totalBank += balance
            break
          case 'fund':
          case 'stock':
            totalInvestment += balance
            break
          case 'debt':
            totalDebt += balance // 负债金额（正数）
            break
          default:
            totalCash += balance
        }
      })

      // 净资产 = 资产 - 负债
      const totalAssets = totalCash + totalBank + totalInvestment - totalDebt

      this.setData({
        accounts: processedAccounts,
        totalAssets: this.formatAmount(totalAssets),
        totalCash: this.formatAmount(totalCash),
        totalBank: this.formatAmount(totalBank),
        totalInvestment: this.formatAmount(totalInvestment),
        totalDebt: this.formatAmount(totalDebt)
      })

    } catch (error) {
      console.error('加载账户列表失败:', error)

      if (!error.message.includes('登录已过期')) {
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    } finally {
      this.setData({ loading: false })
      wx.stopPullDownRefresh()
    }
  },

  // 格式化金额
  formatAmount(amount) {
    return parseFloat(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  },

  // 获取账户图标
  getAccountIcon(type) {
    const iconMap = {
      'bank': '🏦',
      'fund': '📊',
      'stock': '📈',
      'debt': '💳'
    }
    return iconMap[type] || '💰'
  },

  // 获取账户类型显示名称
  getAccountTypeDisplay(type) {
    const typeMap = {
      'bank': '银行账户',
      'fund': '基金账户',
      'stock': '股票账户',
      'debt': '负债账户'
    }
    return typeMap[type] || '其他账户'
  },

  // 查看账户详情
  viewAccountDetail(e) {
    const accountId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/accounts/detail?id=${accountId}`
    })
  },

  // 添加账户
  addAccount() {
    wx.navigateTo({
      url: '/pages/accounts/add'
    })
  },

  // 加载隐藏金额状态
  loadHideAmountsState() {
    try {
      const hideAmounts = wx.getStorageSync('hideAmounts')
      this.setData({
        hideAmounts: hideAmounts === 'true' || hideAmounts === true
      })
    } catch (error) {
      console.error('加载隐藏金额状态失败:', error)
    }
  },

  // 切换金额显示/隐藏
  toggleAmountVisibility() {
    const newHideState = !this.data.hideAmounts
    this.setData({
      hideAmounts: newHideState
    })

    // 保存状态到本地存储
    try {
      wx.setStorageSync('hideAmounts', newHideState)
    } catch (error) {
      console.error('保存隐藏金额状态失败:', error)
    }
  }
})
