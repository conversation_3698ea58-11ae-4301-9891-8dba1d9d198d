<script setup lang="ts">
import { ref, computed, nextTick, watch, onBeforeUnmount } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'

interface Props {
  modelValue?: string[]
  placeholder?: string
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '选择月份'
})

const emit = defineEmits<Emits>()

// 响应式数据
const showPicker = ref(false)
const selectedYear = ref(new Date().getFullYear())
const selectedMonth = ref(new Date().getMonth() + 1)
const yearWheel = ref<HTMLElement>()
const monthWheel = ref<HTMLElement>()

// 触摸相关数据
const touchData = ref({
  startY: 0,
  startTime: 0,
  lastY: 0,
  lastTime: 0,
  velocity: 0,
  isScrolling: false,
  activeColumn: '' as 'year' | 'month' | ''
})

// 年份范围（当前年份前后各10年）
const years = computed(() => {
  const currentYear = new Date().getFullYear()
  const yearsList = []
  for (let i = currentYear - 10; i <= currentYear + 10; i++) {
    yearsList.push(i)
  }
  return yearsList
})

// 月份列表
const months = computed(() => [
  { value: 1, label: '01月' },
  { value: 2, label: '02月' },
  { value: 3, label: '03月' },
  { value: 4, label: '04月' },
  { value: 5, label: '05月' },
  { value: 6, label: '06月' },
  { value: 7, label: '07月' },
  { value: 8, label: '08月' },
  { value: 9, label: '09月' },
  { value: 10, label: '10月' },
  { value: 11, label: '11月' },
  { value: 12, label: '12月' }
])

// 显示文本
const displayText = computed(() => {
  if (props.modelValue && props.modelValue.length > 0) {
    // 如果有值，解析并显示
    const dateStr = props.modelValue[0]
    if (dateStr) {
      const date = new Date(dateStr)
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      return `${year}年${String(month).padStart(2, '0')}月`
    }
  }
  return props.placeholder
})

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && newValue.length > 0) {
    const dateStr = newValue[0]
    if (dateStr) {
      const date = new Date(dateStr)
      selectedYear.value = date.getFullYear()
      selectedMonth.value = date.getMonth() + 1
    }
  }
}, { immediate: true })

// 项目高度常量
const ITEM_HEIGHT = 44

// 获取滚轮位置
const getWheelTransform = (wheel: HTMLElement) => {
  const transform = wheel.style.transform
  const match = transform.match(/translateY\((-?\d+(?:\.\d+)?)px\)/)
  return match ? parseFloat(match[1]) : 0
}

// 设置滚轮位置
const setWheelTransform = (wheel: HTMLElement, y: number, transition = true) => {
  wheel.style.transition = transition ? 'transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)' : 'none'
  wheel.style.transform = `translateY(${y}px)`
}

// 滚动到指定项目
const scrollToItem = (column: 'year' | 'month', index: number, transition = true) => {
  const wheel = column === 'year' ? yearWheel.value : monthWheel.value
  if (!wheel) return
  
  const targetY = -index * ITEM_HEIGHT
  setWheelTransform(wheel, targetY, transition)
  
  // 更新选中值
  if (column === 'year') {
    selectedYear.value = years.value[index]
  } else {
    selectedMonth.value = months.value[index].value
  }
}

// 获取最近的项目索引
const getNearestIndex = (y: number, maxIndex: number) => {
  const index = Math.round(-y / ITEM_HEIGHT)
  return Math.max(0, Math.min(index, maxIndex))
}

// 处理触摸开始
const handleTouchStart = (event: TouchEvent, column: 'year' | 'month') => {
  event.preventDefault()
  
  const touch = event.touches[0]
  touchData.value = {
    startY: touch.clientY,
    startTime: Date.now(),
    lastY: touch.clientY,
    lastTime: Date.now(),
    velocity: 0,
    isScrolling: true,
    activeColumn: column
  }
  
  const wheel = column === 'year' ? yearWheel.value : monthWheel.value
  if (wheel) {
    wheel.style.transition = 'none'
  }
}

// 处理触摸移动
const handleTouchMove = (event: TouchEvent, column: 'year' | 'month') => {
  if (!touchData.value.isScrolling || touchData.value.activeColumn !== column) return
  
  event.preventDefault()
  
  const touch = event.touches[0]
  const currentTime = Date.now()
  const deltaY = touch.clientY - touchData.value.startY
  const timeDelta = currentTime - touchData.value.lastTime
  
  // 计算速度
  if (timeDelta > 0) {
    touchData.value.velocity = (touch.clientY - touchData.value.lastY) / timeDelta
  }
  
  touchData.value.lastY = touch.clientY
  touchData.value.lastTime = currentTime
  
  const wheel = column === 'year' ? yearWheel.value : monthWheel.value
  if (!wheel) return
  
  const currentY = getWheelTransform(wheel)
  const newY = currentY + deltaY
  
  // 限制滚动范围
  const maxIndex = column === 'year' ? years.value.length - 1 : months.value.length - 1
  const minY = -maxIndex * ITEM_HEIGHT
  const maxY = 0
  
  const clampedY = Math.max(minY, Math.min(maxY, newY))
  setWheelTransform(wheel, clampedY, false)
  
  touchData.value.startY = touch.clientY
}

// 处理触摸结束
const handleTouchEnd = (event: TouchEvent, column: 'year' | 'month') => {
  if (!touchData.value.isScrolling || touchData.value.activeColumn !== column) return
  
  touchData.value.isScrolling = false
  touchData.value.activeColumn = ''
  
  const wheel = column === 'year' ? yearWheel.value : monthWheel.value
  if (!wheel) return
  
  const currentY = getWheelTransform(wheel)
  const maxIndex = column === 'year' ? years.value.length - 1 : months.value.length - 1
  
  // 计算惯性滚动
  let finalY = currentY
  if (Math.abs(touchData.value.velocity) > 0.5) {
    const deceleration = 0.003
    const duration = Math.abs(touchData.value.velocity) / deceleration
    finalY = currentY + touchData.value.velocity * duration * 0.5
  }
  
  // 对齐到最近的项目
  const targetIndex = getNearestIndex(finalY, maxIndex)
  scrollToItem(column, targetIndex, true)
}

// 处理鼠标滚轮
const handleWheel = (event: WheelEvent, column: 'year' | 'month') => {
  event.preventDefault()
  
  const wheel = column === 'year' ? yearWheel.value : monthWheel.value
  if (!wheel) return
  
  const currentY = getWheelTransform(wheel)
  const deltaY = event.deltaY > 0 ? -ITEM_HEIGHT : ITEM_HEIGHT
  const newY = currentY + deltaY
  
  const maxIndex = column === 'year' ? years.value.length - 1 : months.value.length - 1
  const targetIndex = getNearestIndex(newY, maxIndex)
  
  scrollToItem(column, targetIndex, true)
}

// 初始化滚轮位置
const initializeWheels = async () => {
  await nextTick()
  
  // 设置年份位置
  if (yearWheel.value) {
    const yearIndex = years.value.findIndex(year => year === selectedYear.value)
    if (yearIndex !== -1) {
      scrollToItem('year', yearIndex, false)
    }
  }
  
  // 设置月份位置
  if (monthWheel.value) {
    const monthIndex = months.value.findIndex(month => month.value === selectedMonth.value)
    if (monthIndex !== -1) {
      scrollToItem('month', monthIndex, false)
    }
  }
}

// 确认选择
const confirmSelection = () => {
  const year = selectedYear.value
  const month = selectedMonth.value
  
  // 构造日期范围：选中月份的第一天到最后一天
  const firstDay = new Date(year, month - 1, 1)
  const lastDay = new Date(year, month, 0)
  
  const startDate = `${year}-${String(month).padStart(2, '0')}-01`
  const endDate = `${year}-${String(month).padStart(2, '0')}-${String(lastDay.getDate()).padStart(2, '0')}`
  
  emit('update:modelValue', [startDate, endDate])
  showPicker.value = false
}

// 取消选择
const cancelSelection = () => {
  // 恢复到原始值
  if (props.modelValue && props.modelValue.length > 0) {
    const dateStr = props.modelValue[0]
    if (dateStr) {
      const date = new Date(dateStr)
      selectedYear.value = date.getFullYear()
      selectedMonth.value = date.getMonth() + 1
    }
  } else {
    selectedYear.value = new Date().getFullYear()
    selectedMonth.value = new Date().getMonth() + 1
  }
  showPicker.value = false
}

// 处理遮罩点击
const handleOverlayClick = (event: Event) => {
  if (event.target === event.currentTarget) {
    cancelSelection()
  }
}

// 监听选择器显示状态
watch(showPicker, (newValue) => {
  if (newValue) {
    initializeWheels()
    // 阻止页面滚动
    document.body.style.overflow = 'hidden'
  } else {
    // 恢复页面滚动
    document.body.style.overflow = ''
  }
})

// 组件卸载时清理
onBeforeUnmount(() => {
  document.body.style.overflow = ''
})
</script>

<template>
  <div class="ios-month-picker-wrapper">
    <!-- 触发按钮 -->
    <button 
      class="ios-month-picker-trigger"
      @click="showPicker = true"
    >
      <span class="ios-picker-text">
        {{ displayText }}
      </span>
      <el-icon class="ios-picker-icon">
        <ArrowDown />
      </el-icon>
    </button>

    <!-- iOS风格模态遮罩和选择器 -->
    <div 
      v-if="showPicker" 
      class="ios-picker-overlay"
      @click="handleOverlayClick"
    >
      <div class="ios-picker-modal" @click.stop>
        <!-- 标题栏 -->
        <div class="ios-picker-header">
          <button class="ios-picker-cancel" @click="cancelSelection">
            取消
          </button>
          <h3 class="ios-picker-title">选择月份</h3>
          <button class="ios-picker-confirm" @click="confirmSelection">
            确定
          </button>
        </div>

        <!-- 滚轮选择器容器 -->
        <div class="ios-picker-content">
          <!-- 年份选择器 -->
          <div class="ios-picker-column">
            <div class="ios-picker-mask ios-picker-mask-top"></div>
            <div class="ios-picker-mask ios-picker-mask-bottom"></div>
            <div 
              class="ios-picker-wheel"
              ref="yearWheel"
              @touchstart="handleTouchStart($event, 'year')"
              @touchmove="handleTouchMove($event, 'year')"
              @touchend="handleTouchEnd($event, 'year')"
              @wheel="handleWheel($event, 'year')"
            >
              <div 
                v-for="year in years"
                :key="year"
                class="ios-picker-item"
                :class="{ 'ios-picker-item-selected': year === selectedYear }"
              >
                {{ year }}年
              </div>
            </div>
            <div class="ios-picker-indicator"></div>
          </div>

          <!-- 月份选择器 -->
          <div class="ios-picker-column">
            <div class="ios-picker-mask ios-picker-mask-top"></div>
            <div class="ios-picker-mask ios-picker-mask-bottom"></div>
            <div 
              class="ios-picker-wheel"
              ref="monthWheel"
              @touchstart="handleTouchStart($event, 'month')"
              @touchmove="handleTouchMove($event, 'month')"
              @touchend="handleTouchEnd($event, 'month')"
              @wheel="handleWheel($event, 'month')"
            >
              <div 
                v-for="month in months"
                :key="month.value"
                class="ios-picker-item"
                :class="{ 'ios-picker-item-selected': month.value === selectedMonth }"
              >
                {{ month.label }}
              </div>
            </div>
            <div class="ios-picker-indicator"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// iOS设计系统颜色
$ios-primary: #007AFF;
$ios-background: #F2F2F7;
$ios-card-background: rgba(255, 255, 255, 0.95);
$ios-text-primary: #1C1C1E;
$ios-text-secondary: #8E8E93;
$ios-text-tertiary: #C7C7CC;
$ios-separator: #C6C6C8;
$ios-shadow: 0 2px 16px rgba(0, 0, 0, 0.06);
$ios-border-radius: 16px;
$ios-border-radius-small: 12px;

.ios-month-picker-wrapper {
  width: 100%;
}

// 触发按钮
.ios-month-picker-trigger {
  width: 100%;
  height: 44px;
  background: transparent;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: $ios-border-radius-small;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
  color: $ios-text-primary;

  &:hover {
    border-color: rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.5);
  }

  &:active {
    transform: scale(0.98);
  }

  .ios-picker-text {
    flex: 1;
    text-align: left;
    font-weight: 500;
  }

  .ios-picker-icon {
    color: $ios-text-secondary;
    font-size: 14px;
    transition: transform 0.2s ease;
  }
}

// 模态遮罩
.ios-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: flex-end;
  animation: ios-fade-in 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  @media (min-width: 769px) {
    align-items: center;
    justify-content: center;
  }
}

// 选择器模态框
.ios-picker-modal {
  width: 100%;
  max-width: 400px;
  background: $ios-card-background;
  border-radius: $ios-border-radius $ios-border-radius 0 0;
  backdrop-filter: blur(40px);
  box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.12);
  animation: ios-slide-up 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
  
  @media (min-width: 769px) {
    border-radius: $ios-border-radius;
    box-shadow: 0 16px 64px rgba(0, 0, 0, 0.15);
    animation: ios-scale-in 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}

// 标题栏
.ios-picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid $ios-separator;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);

  .ios-picker-cancel,
  .ios-picker-confirm {
    background: none;
    border: none;
    font-size: 17px;
    font-weight: 500;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.2s ease;
    min-width: 60px;
    text-align: center;
  }

  .ios-picker-cancel {
    color: $ios-text-secondary;

    &:hover {
      background: rgba(142, 142, 147, 0.1);
    }

    &:active {
      transform: scale(0.95);
    }
  }

  .ios-picker-confirm {
    color: $ios-primary;
    font-weight: 600;

    &:hover {
      background: rgba(0, 122, 255, 0.1);
    }

    &:active {
      transform: scale(0.95);
    }
  }

  .ios-picker-title {
    font-size: 17px;
    font-weight: 600;
    color: $ios-text-primary;
    margin: 0;
    letter-spacing: -0.2px;
  }
}

// 选择器内容
.ios-picker-content {
  display: flex;
  padding: 20px 0;
  background: rgba(255, 255, 255, 0.95);
  min-height: 220px;
}

// 选择器列
.ios-picker-column {
  flex: 1;
  position: relative;
  height: 220px;
  overflow: hidden;

  &:first-child {
    border-right: 1px solid $ios-separator;
  }
}

// 滚轮
.ios-picker-wheel {
  padding: 88px 0;
  transform: translateY(0);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  touch-action: none;
  user-select: none;
}

// 选择器项目
.ios-picker-item {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 500;
  color: $ios-text-secondary;
  transition: all 0.2s ease;
  cursor: pointer;

  &.ios-picker-item-selected {
    color: $ios-text-primary;
    font-weight: 600;
    transform: scale(1.05);
  }
}

// 遮罩渐变
.ios-picker-mask {
  position: absolute;
  left: 0;
  right: 0;
  height: 88px;
  pointer-events: none;
  z-index: 1;

  &.ios-picker-mask-top {
    top: 0;
    background: linear-gradient(to bottom, 
      rgba(255, 255, 255, 0.95) 0%, 
      rgba(255, 255, 255, 0.8) 50%, 
      rgba(255, 255, 255, 0) 100%);
  }

  &.ios-picker-mask-bottom {
    bottom: 0;
    background: linear-gradient(to top, 
      rgba(255, 255, 255, 0.95) 0%, 
      rgba(255, 255, 255, 0.8) 50%, 
      rgba(255, 255, 255, 0) 100%);
  }
}

// 选中指示器
.ios-picker-indicator {
  position: absolute;
  left: 20px;
  right: 20px;
  top: 50%;
  height: 44px;
  margin-top: -22px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  background: rgba(0, 122, 255, 0.02);
  border-radius: 6px;
  pointer-events: none;
  z-index: 2;
}

// 动画定义
@keyframes ios-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes ios-slide-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes ios-scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 移动端优化
@media (max-width: 768px) {
  .ios-picker-header {
    padding: 12px 16px;

    .ios-picker-title {
      font-size: 16px;
    }

    .ios-picker-cancel,
    .ios-picker-confirm {
      font-size: 16px;
      padding: 6px 10px;
    }
  }

  .ios-picker-content {
    min-height: 200px;
  }

  .ios-picker-column {
    height: 200px;
  }

  .ios-picker-wheel {
    padding: 78px 0;
  }

  .ios-picker-mask {
    height: 78px;
  }

  .ios-picker-item {
    font-size: 18px;
  }

  .ios-picker-indicator {
    left: 16px;
    right: 16px;
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .ios-month-picker-trigger {
    background: rgba(28, 28, 30, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
    color: #FFFFFF;

    &:hover {
      border-color: rgba(255, 255, 255, 0.2);
      background: rgba(44, 44, 46, 0.95);
    }

    .ios-picker-icon {
      color: #8E8E93;
    }
  }

  .ios-picker-overlay {
    background: rgba(0, 0, 0, 0.6);
  }

  .ios-picker-modal {
    background: rgba(28, 28, 30, 0.95);
  }

  .ios-picker-header {
    background: rgba(28, 28, 30, 0.9);
    border-bottom-color: rgba(255, 255, 255, 0.1);

    .ios-picker-title {
      color: #FFFFFF;
    }

    .ios-picker-cancel {
      color: #8E8E93;

      &:hover {
        background: rgba(142, 142, 147, 0.2);
      }
    }

    .ios-picker-confirm {
      color: #0A84FF;

      &:hover {
        background: rgba(10, 132, 255, 0.2);
      }
    }
  }

  .ios-picker-content {
    background: rgba(28, 28, 30, 0.95);
  }

  .ios-picker-column {
    &:first-child {
      border-right-color: rgba(255, 255, 255, 0.1);
    }
  }

  .ios-picker-item {
    color: #8E8E93;

    &.ios-picker-item-selected {
      color: #FFFFFF;
    }
  }

  .ios-picker-mask {
    &.ios-picker-mask-top {
      background: linear-gradient(to bottom, 
        rgba(28, 28, 30, 0.95) 0%, 
        rgba(28, 28, 30, 0.8) 50%, 
        rgba(28, 28, 30, 0) 100%);
    }

    &.ios-picker-mask-bottom {
      background: linear-gradient(to top, 
        rgba(28, 28, 30, 0.95) 0%, 
        rgba(28, 28, 30, 0.8) 50%, 
        rgba(28, 28, 30, 0) 100%);
    }
  }

  .ios-picker-indicator {
    border-color: rgba(255, 255, 255, 0.06);
    background: rgba(10, 132, 255, 0.05);
  }
}
</style> 