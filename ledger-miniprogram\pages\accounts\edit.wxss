/* pages/accounts/edit.wxss */
.edit-account-page {
  min-height: 100vh;
  background-color: #F2F2F7;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background-color: #FFFFFF;
  border-bottom: 1rpx solid #E5E5EA;
}

.header-left {
  display: flex;
  align-items: center;
  color: #007AFF;
  font-size: 32rpx;
}

.back-icon {
  font-size: 48rpx;
  margin-right: 8rpx;
  font-weight: 300;
}

.back-text {
  font-size: 32rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

.header-right {
  width: 80rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #E5E5EA;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  color: #8E8E93;
  font-size: 28rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 表单容器 */
.form-container {
  padding: 40rpx 32rpx;
}

.form-group {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
}

.form-label {
  padding: 32rpx 32rpx 16rpx 32rpx;
  font-size: 28rpx;
  color: #8E8E93;
  font-weight: 500;
}

.form-input-container {
  position: relative;
  padding: 0 32rpx 32rpx 32rpx;
}

.form-input {
  width: 100%;
  font-size: 34rpx;
  color: #000000;
  background: transparent;
  border: none;
  outline: none;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #E5E5EA;
}

.form-input:focus {
  border-bottom-color: #007AFF;
}

.form-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 32rpx 32rpx 32rpx;
  cursor: pointer;
}

.picker-text {
  font-size: 34rpx;
  color: #000000;
}

.picker-text.placeholder {
  color: #C7C7CC;
}

.picker-arrow {
  font-size: 32rpx;
  color: #C7C7CC;
  font-weight: 500;
}

.form-switch {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 32rpx 32rpx 32rpx;
}

.switch-label {
  font-size: 34rpx;
  color: #000000;
}

/* 底部按钮 */
.form-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background-color: #FFFFFF;
  border-top: 1rpx solid #E5E5EA;
}

.btn-primary {
  width: 100%;
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 32rpx;
  font-size: 36rpx;
  font-weight: 500;
}

.btn-primary:disabled {
  background-color: #C7C7CC;
  color: #8E8E93;
}

/* 选择器样式 */
.picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.picker-modal {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  width: 100%;
  max-height: 60vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
  background: #F8F9FA;
}

.picker-cancel,
.picker-confirm {
  font-size: 32rpx;
  color: #007AFF;
}

.picker-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

.picker-content {
  max-height: 50vh;
  overflow-y: scroll;
}

.picker-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
  cursor: pointer;
}

.picker-item:last-child {
  border-bottom: none;
}

.picker-item.active {
  background-color: #F0F9FF;
}

.picker-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
}

.picker-label {
  flex: 1;
  font-size: 34rpx;
  color: #000000;
}

.picker-check {
  font-size: 32rpx;
  color: #007AFF;
  font-weight: 600;
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}
