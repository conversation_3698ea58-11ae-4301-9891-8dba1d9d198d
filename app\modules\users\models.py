from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from app.db.base_class import Base
import datetime

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    phone = Column(String(20), nullable=True)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    last_login = Column(DateTime, nullable=True)
    device_info = Column(String(255), nullable=True)
    deleted = Column(Boolean, default=False)

    # 邮箱验证相关字段
    email_verified = Column(Boolean, default=False, nullable=False)  # 邮箱是否已验证
    email_verification_code = Column(String(6), nullable=True)  # 邮箱验证码
    email_verification_expires = Column(DateTime, nullable=True)  # 验证码过期时间

    # 密码重置相关字段
    password_reset_code = Column(String(6), nullable=True)  # 密码重置验证码
    password_reset_expires = Column(DateTime, nullable=True)  # 密码重置验证码过期时间
    
    # 关系
    budgets = relationship("Budget", back_populates="user", cascade="all, delete-orphan")
    categories = relationship("Category", back_populates="user", cascade="all, delete-orphan")
    settings = relationship("UserSettings", back_populates="user", uselist=False, cascade="all, delete-orphan")

class UserSettings(Base):
    __tablename__ = "user_settings"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, unique=True)
    language = Column(String(10), default="zh")
    currency = Column(String(10), default="CNY")
    dark_mode = Column(Boolean, default=False)
    notifications = Column(Boolean, default=True)
    auto_backup = Column(Boolean, default=False)
    last_backup = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="settings")