import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

// 根据环境设置 baseURL
const isDev = import.meta.env.DEV;
const baseURL = isDev 
  ? '/api/v1' 
  : `${window.location.protocol}//${window.location.hostname}:8000/api/v1`;

// 创建新的axios实例，不使用全局实例，避免被mock影响
const service = axios.create({
  baseURL: baseURL,  // 使用根据环境设置的 baseURL
  timeout: 30000,      // 增加超时时间到30秒
  headers: {
    'Content-Type': 'application/json;charset=utf-8'
  }
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    console.log(`发送${config.method?.toUpperCase()}请求到: ${config.url}`, config.data || config.params)
    
    // 在发送请求之前做些什么
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    // 处理请求错误
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    // 记录响应
    console.log(`收到响应: ${response.config.url}`, response.status, response.data)
    
    // 对于登录请求，直接返回整个响应
    if (response.config.url?.includes('/login/access-token')) {
      return response
    }
    
    // 2xx 范围内的状态码都会触发该函数
    const res = response.data
    // 如果返回的状态码不为200或201，说明接口请求可能有问题
    if (response.status !== 200 && response.status !== 201) {
      ElMessage({
        message: res.message || '请求失败',
        type: 'error',
        duration: 5 * 1000,   // 设置为5秒
        showClose: true        // 显示关闭按钮
      })
      
      // 401: 未授权
      if (response.status === 401) {
        // 跳转到登录页
        window.location.href = '/login'
      }
      
      return Promise.reject(new Error(res.message || '请求失败'))
    } else {
      return res
    }
  },
  (error) => {
    // 超出 2xx 范围的状态码都会触发该函数
    console.error('响应错误:', error)
    const { response } = error
    
    if (response) {
      console.error('错误响应详情:', response.status, response.data)
      
      // 401: 未授权
      if (response.status === 401) {
        // 如果是登录接口的401错误，不要跳转，让组件处理
        if (response.config.url?.includes('/login/access-token')) {
          const errorMsg = response.data?.detail || '用户名或密码错误，请检查后重试'
          ElMessage({
            message: errorMsg,
            type: 'error',
            duration: 5 * 1000,   // 设置为5秒
            showClose: true       // 显示关闭按钮
          })
        } else {
          // 其他接口的401错误，说明token过期
          ElMessage({
            message: '登录已过期，请重新登录',
            type: 'error',
            duration: 5 * 1000,   // 设置为5秒
            showClose: true       // 显示关闭按钮
          })
          // 跳转到登录页
          window.location.href = '/login'
        }
      }
      // 处理交易特定的错误
      else if (response.config.url?.includes('/transactions') && response.status === 500) {
        let errorMsg = '处理交易时出错'

        // 尝试提取详细错误信息
        if (response.data?.detail) {
          errorMsg = `${errorMsg}: ${response.data.detail}`
        } else if (typeof response.data === 'string' && response.data.includes('property')) {
          errorMsg = '交易创建失败: 系统内部错误，请联系管理员'
        }

        ElMessage({
          message: errorMsg,
          type: 'error',
          duration: 5 * 1000,   // 设置为5秒
          showClose: true        // 显示关闭按钮
        })
      }
      // 处理用户注册和登录相关的错误
      else if (response.config.url?.includes('/users/')) {
        let errorMsg = response.data?.detail || error.message || '操作失败'

        // 将常见的英文错误信息转换为中文（兼容旧版本API）
        if (errorMsg.includes('already registered')) {
          if (response.config.url?.includes('register')) {
            errorMsg = '该邮箱已被注册，请使用其他邮箱或直接登录'
          } else {
            errorMsg = '该邮箱已被注册'
          }
        } else if (errorMsg.includes('Username already registered')) {
          errorMsg = '该用户名已被注册，请选择其他用户名'
        } else if (errorMsg.includes('Incorrect username or password')) {
          errorMsg = '用户名或密码错误，请检查后重试'
        } else if (errorMsg.includes('Invalid verification code')) {
          errorMsg = '验证码无效或已过期，请重新获取验证码'
        }

        ElMessage({
          message: errorMsg,
          type: 'error',
          duration: 5 * 1000,   // 设置为5秒
          showClose: true       // 显示关闭按钮
        })
      }
      else {
        const errorMsg = response.data?.detail || error.message || '请求失败'
        ElMessage({
          message: errorMsg,
          type: 'error',
          duration: 5 * 1000,   // 设置为5秒
          showClose: true       // 显示关闭按钮
        })
      }
    } else {
      ElMessage({
        message: '网络错误，请检查您的网络连接',
        type: 'error',
        duration: 5 * 1000,   // 设置为5秒
        showClose: true       // 显示关闭按钮
      })
    }
    
    return Promise.reject(error)
  }
)

// 封装GET请求
export function get<T>(url: string, params?: any): Promise<T> {
  return service.get(url, { params })
}

// 封装POST请求
export function post<T>(url: string, data?: any): Promise<T> {
  return service.post(url, data)
}

// 封装PUT请求
export function put<T>(url: string, data?: any): Promise<T> {
  return service.put(url, data)
}

// 封装DELETE请求
export function del<T>(url: string, params?: any): Promise<T> {
  return service.delete(url, { params })
}

export default service 