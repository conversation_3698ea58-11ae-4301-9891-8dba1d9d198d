/**
 * ECharts深色模式修复专用样式
 * 针对ECharts生成的DOM元素直接进行样式覆盖
 */

/* 图例文字颜色 */
.dark-mode [_echarts_instance_] .ec-legend {
  color: #e2e2e6 !important;
}

.dark-mode [_echarts_instance_] .ec-legend-item {
  color: #e2e2e6 !important;
}

.dark-mode [_echarts_instance_] text {
  fill: #e2e2e6 !important;
  color: #e2e2e6 !important;
}

/* SVG元素颜色修复 */
.dark-mode [_echarts_instance_] path[stroke="#333"] {
  stroke: #e2e2e6 !important;
}

.dark-mode [_echarts_instance_] path[fill="#333"] {
  fill: #e2e2e6 !important;
}

.dark-mode [_echarts_instance_] g {
  fill: #e2e2e6 !important;
}

.dark-mode [_echarts_instance_] line[stroke="#ccc"] {
  stroke: #363646 !important;
}

.dark-mode [_echarts_instance_] line[stroke="#eee"] {
  stroke: #363646 !important;
}

/* 直接选择图表元素 */
.dark-mode [_echarts_instance_] .ec-legend-item span {
  color: #e2e2e6 !important;
}

.dark-mode [_echarts_instance_] .ec-legend text {
  fill: #e2e2e6 !important;
}

/* 收入与支出图例特别处理 */
.dark-mode .analytics-view .income-expense-chart [_echarts_instance_] text {
  fill: #e2e2e6 !important;
}

.dark-mode .analytics-view .income-expense-chart [_echarts_instance_] .ec-legend-item {
  color: #e2e2e6 !important;
}

/* 极端情况处理 - 使用重要性覆盖内联样式 */
.dark-mode [_echarts_instance_] text[style*="fill:#333"] {
  fill: #e2e2e6 !important;
}

.dark-mode [_echarts_instance_] text[style*="color:#333"] {
  color: #e2e2e6 !important;
}

/* 覆盖ECharts默认主题的文本颜色 */
.dark-mode [_echarts_instance_] .ec-title text,
.dark-mode [_echarts_instance_] .ec-axis text,
.dark-mode [_echarts_instance_] .ec-legend text {
  fill: #e2e2e6 !important;
}

/* 背景色覆盖 */
.dark-mode [_echarts_instance_] canvas {
  background-color: #282838 !important;
} 