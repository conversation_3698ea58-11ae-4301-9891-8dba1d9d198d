<script setup lang="ts">
import { ref } from 'vue'
import { ElButton, ElProgress, ElMessage, ElMessageBox } from 'element-plus'
import { backupUserData, exportUserData, restoreUserData, getUserBackups, restoreLatestBackup } from '../../api/user'

const props = defineProps<{
  lastBackup: string | null
}>()

const emit = defineEmits(['backup-completed'])

const isBackingUp = ref(false)
const isExporting = ref(false)
const isRestoring = ref(false)
const backupProgress = ref(0)

// 备份数据
const backupData = async () => {
  try {
    isBackingUp.value = true
    backupProgress.value = 0
    
    // 模拟进度
    const interval = setInterval(() => {
      if (backupProgress.value < 90) {
        backupProgress.value += 10
      }
    }, 300)
    
    ElMessage({
      type: 'info',
      message: '数据备份已开始，请稍后...'
    })
    
    const settings = await backupUserData()
    
    // 完成进度
    backupProgress.value = 100
    clearInterval(interval)
    
    ElMessage({
      type: 'success',
      message: '数据备份完成'
    })
    
    // 通知父组件备份已完成
    emit('backup-completed', settings.last_backup)
  } catch (error) {
    console.error('备份数据失败', error)
    ElMessage({
      type: 'error',
      message: '备份数据失败'
    })
  } finally {
    // 重置进度条
    setTimeout(() => {
      backupProgress.value = 0
      isBackingUp.value = false
    }, 1000)
  }
}

// 导出数据
const exportData = async () => {
  try {
    isExporting.value = true
    
    ElMessage({
      type: 'info',
      message: '正在准备导出数据...'
    })
    
    const response = await exportUserData()
    
    // 创建下载链接
    const blob = new Blob([response], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `ledger_export_${new Date().toISOString().slice(0, 10)}.xlsx`
    
    link.click()
    URL.revokeObjectURL(link.href)
    
    ElMessage({
      type: 'success',
      message: '数据导出成功'
    })
  } catch (error) {
    console.error('导出数据失败', error)
    ElMessage({
      type: 'error',
      message: '导出数据失败'
    })
  } finally {
    isExporting.value = false
  }
}

// 数据恢复 (手动选择文件恢复)
const restoreData = () => {
  ElMessageBox.confirm(
    '恢复数据将覆盖您当前的所有数据，是否继续？',
    '恢复确认',
    {
      confirmButtonText: '确认恢复',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 创建一个隐藏的文件上传输入框
    const fileInput = document.createElement('input')
    fileInput.type = 'file'
    fileInput.accept = 'application/json'
    fileInput.style.display = 'none'
    document.body.appendChild(fileInput)
    
    // 监听文件选择事件
    fileInput.addEventListener('change', async (event) => {
      const files = (event.target as HTMLInputElement).files
      if (!files || files.length === 0) {
        return
      }
      
      try {
        isRestoring.value = true
        const file = files[0]
        
        ElMessage({
          type: 'info',
          message: '正在恢复数据，请勿关闭页面...'
        })
        
        await restoreUserData(file)
        
        ElMessage({
          type: 'success',
          message: '数据恢复成功，页面将在3秒后刷新'
        })
        
        // 3秒后刷新页面以加载新数据
        setTimeout(() => {
          window.location.reload()
        }, 3000)
      } catch (error) {
        console.error('恢复数据失败', error)
        ElMessage({
          type: 'error',
          message: '恢复数据失败，请确保上传的文件格式正确'
        })
      } finally {
        isRestoring.value = false
        document.body.removeChild(fileInput)
      }
    })
    
    // 触发文件选择
    fileInput.click()
  }).catch(() => {
    // 用户取消恢复操作
  })
}

// 自动恢复最新备份
const restoreLatest = async () => {
  try {
    // 先确认用户是否有备份
    const backupsResponse = await getUserBackups()
    const backups = backupsResponse.data
    
    if (!backups || backups.length === 0) {
      ElMessage({
        type: 'warning',
        message: '未找到任何备份文件，无法恢复数据'
      })
      return
    }
    
    // 显示最新备份信息并确认
    const latestBackup = backups[0]
    const backupTime = new Date(latestBackup.created_at).toLocaleString()
    
    ElMessageBox.confirm(
      `将使用最新备份 (${backupTime}) 进行恢复，此操作将覆盖当前所有数据，是否继续？`,
      '自动恢复确认',
      {
        confirmButtonText: '确认恢复',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(async () => {
      try {
        isRestoring.value = true
        
        ElMessage({
          type: 'info',
          message: '正在恢复数据，请勿关闭页面...'
        })
        
        await restoreLatestBackup()
        
        ElMessage({
          type: 'success',
          message: '数据恢复成功，页面将在3秒后刷新'
        })
        
        // 3秒后刷新页面以加载新数据
        setTimeout(() => {
          window.location.reload()
        }, 3000)
      } catch (error: any) {
        console.error('恢复最新备份失败', error)
        ElMessage({
          type: 'error',
          message: '恢复数据失败: ' + (error.response?.data?.detail || '未知错误')
        })
      } finally {
        isRestoring.value = false
      }
    }).catch(() => {
      // 用户取消恢复操作
    })
  } catch (error: any) {
    console.error('获取备份列表失败', error)
    ElMessage({
      type: 'error',
      message: '获取备份列表失败'
    })
  }
}
</script>

<template>
  <div class="data-backup-panel">
    <div class="backup-info">
      <div class="info-label">上次备份时间:</div>
      <div class="info-value">{{ lastBackup || '从未备份' }}</div>
    </div>
    
    <div v-if="isBackingUp" class="backup-progress">
      <el-progress :percentage="backupProgress" :striped="true" :animated="true" />
    </div>
    
    <div class="action-buttons">
      <el-button 
        type="primary" 
        @click="backupData" 
        :loading="isBackingUp"
      >
        立即备份
      </el-button>
      
      <el-button 
        @click="exportData" 
        :loading="isExporting"
      >
        导出数据
      </el-button>
      
      <el-button 
        type="warning" 
        @click="restoreLatest" 
        :loading="isRestoring"
      >
        恢复数据
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.data-backup-panel {
  padding: 10px 0;
  width: 100%;
}

.backup-info {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  
  .info-label {
    font-weight: 500;
    margin-right: 10px;
    color: var(--apple-gray);
  }
  
  .info-value {
    color: var(--apple-dark-gray);
  }
}

.backup-progress {
  margin: 20px 0;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  
  @media (max-width: 480px) {
    flex-direction: column;
    gap: 12px;
    width: 100%; /* 确保容器本身是全宽 */
    
    .el-button {
      height: 44px;
      font-size: 15px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 0;
      margin-right: 0;
      box-sizing: border-box;
      padding-left: 0;
      padding-right: 0;
    }
  }
}

/* 添加响应式调整 */
@media (max-width: 480px) {
  .data-backup-panel {
    padding: 15px 0;
  }
  
  .backup-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
    margin-bottom: 20px;
    
    .info-label {
      font-size: 14px;
    }
    
    .info-value {
      font-size: 16px;
      font-weight: 500;
    }
  }
}
</style> 