# 金融账本系统 - 后端产品文档

## 1. 系统概述

金融账本系统是一个全面的个人财务管理平台，旨在帮助用户追踪和管理多种金融账户，包括银行储蓄账户、基金理财账户、股票投资账户以及负债账户。系统采用前后端分离架构，本文档主要介绍后端部分的功能和技术实现。

### 1.1 核心功能

- **多账户管理**：支持添加、编辑、查询和删除多种类型的金融账户
- **用户认证与授权**：基于JWT的安全认证机制
- **数据统计与分析**：提供账户余额、资产分布等统计功能
- **交易记录**：支持记录和查询各类账户的交易明细
- **转账功能**：支持不同账户间的转账，包括资产账户和负债账户
- **预算管理**：支持按类别设置月度预算，追踪支出情况
- **数据管理**：支持用户数据的备份、恢复、导出和账户管理
- **前端适配支持**：提供适当的API结构和响应格式，支持现代前端UI实现

### 1.2 技术栈

- **编程语言**：Python 3.8+
- **Web框架**：FastAPI
- **数据库**：MySQL 5.7.36
- **ORM**：SQLAlchemy
- **缓存**：Redis
- **认证**：JWT (JSON Web Tokens)
- **API文档**：Swagger/OpenAPI
- **Docker容器化**：支持容器部署

## 2. 系统架构

### 2.1 整体架构

系统采用领域驱动设计(DDD)思想，按功能模块划分，每个模块包含自己的模型、服务和API路由。

```
app/
├── api/                # API层
│   └── v1/             # API版本
│       ├── endpoints/  # API端点
│       │   ├── users/  # 用户相关API
│       │   ├── accounts/ # 账户相关API
│       │   ├── transactions/ # 交易相关API
│       │   ├── categories/ # 分类相关API
│       │   └── budgets/ # 预算相关API
│       └── api.py      # API路由注册
├── core/               # 核心配置
│   ├── config.py       # 配置管理
│   └── security.py     # 安全相关
├── db/                 # 数据库
│   ├── base.py         # 基础模型
│   └── session.py      # 数据库会话
├── modules/            # 业务模块
│   ├── users/          # 用户模块
│   │   ├── models.py   # 数据模型
│   │   ├── schemas.py  # 数据验证模式
│   │   └── service.py  # 业务逻辑
│   ├── accounts/       # 账户模块
│   ├── transactions/   # 交易模块
│   ├── categories/     # 分类模块
│   └── budgets/        # 预算模块
├── redis/              # Redis缓存
│   └── client.py       # Redis客户端
├── services/           # 通用服务
│   ├── backup_service.py  # 数据备份服务
│   └── export_service.py  # 数据导出服务
└── main.py             # 应用入口
```

### 2.2 数据流

1. 客户端发送HTTP请求到API端点
2. API路由将请求转发到相应的处理函数
3. 处理函数调用业务服务层处理业务逻辑
4. 业务服务层与数据库交互，执行CRUD操作
5. 处理结果返回给客户端

### 2.3 与前端架构的集成

后端API设计充分考虑了前端需求，特别是:

- **统一的响应格式**：便于前端处理各种情况
- **精细的错误信息**：帮助前端显示友好的错误提示
- **分页和过滤支持**：支持前端实现高效的数据展示和筛选
- **高效的数据结构**：减少前端数据处理的复杂性
- **响应式支持**：API响应格式适合桌面和移动设备展示
- **CORS支持**：允许前端跨域请求

## 3. 数据库设计

系统采用MySQL关系型数据库存储数据，主要表结构如下：

### 3.1 用户表 (users)

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | INT | 用户ID | 主键，自增 |
| username | VARCHAR(50) | 用户名 | 唯一，非空 |
| email | VARCHAR(100) | 电子邮箱 | 唯一，非空 |
| password_hash | VARCHAR(255) | 密码哈希 | 非空 |
| phone | VARCHAR(20) | 手机号码 | 可空 |
| created_at | DATETIME | 创建时间 | 默认当前时间 |
| updated_at | DATETIME | 更新时间 | 默认当前时间 |
| last_login | DATETIME | 最后登录时间 | 可空 |
| device_info | VARCHAR(255) | 设备信息 | 可空 |
| deleted | BOOLEAN | 是否删除 | 默认FALSE |

### 3.2 账户表 (accounts)

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | INT | 账户ID | 主键，自增 |
| user_id | INT | 所属用户ID | 外键，非空 |
| account_name | VARCHAR(100) | 账户名称 | 非空 |
| account_type | VARCHAR(50) | 账户类型 | 非空 |
| institution | VARCHAR(100) | 金融机构 | 可空 |
| account_number | VARCHAR(50) | 账号 | 可空 |
| currency | VARCHAR(10) | 货币类型 | 默认'CNY' |
| initial_balance | DECIMAL(15,2) | 初始余额 | 默认0 |
| current_balance | DECIMAL(15,2) | 当前余额 | 默认0 |
| is_active | BOOLEAN | 是否激活 | 默认TRUE |
| created_at | DATETIME | 创建时间 | 默认当前时间 |
| updated_at | DATETIME | 更新时间 | 默认当前时间 |
| deleted | BOOLEAN | 是否删除 | 默认FALSE |

### 3.3 分类表 (categories)

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | INT | 类别ID | 主键，自增 |
| name | VARCHAR(50) | 类别名称 | 非空 |
| type | ENUM | 类别类型 | 'income'或'expense' |
| color | VARCHAR(20) | 类别颜色 | 默认'#3498db' |
| user_id | INT | 所属用户ID | 外键，可空(NULL表示系统默认分类) |

### 3.4 交易记录表 (transactions)

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | INT | 交易ID | 主键，自增 |
| account_id | INT | 关联账户ID | 外键，非空 |
| user_id | INT | 所属用户ID | 外键，非空 |
| category_id | INT | 交易类别ID | 外键，可空 |
| transaction_type | ENUM | 交易类型 | 'income','expense','transfer'等 |
| amount | DECIMAL(15,2) | 交易金额 | 非空 |
| transaction_date | DATETIME | 交易日期 | 非空 |
| description | VARCHAR(255) | 交易描述 | 可空 |
| from_account_id | INT | 转账来源账户ID | 外键，可空 |
| to_account_id | INT | 转账目标账户ID | 外键，可空 |
| created_at | DATETIME | 创建时间 | 默认当前时间 |
| updated_at | DATETIME | 更新时间 | 默认当前时间 |

### 3.5 预算表 (budgets)

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | INT | 预算ID | 主键，自增 |
| user_id | INT | 所属用户ID | 外键，非空 |
| category_id | INT | 预算类别ID | 外键，非空 |
| amount | DECIMAL(10,2) | 预算金额 | 非空 |
| spent | DECIMAL(10,2) | 已使用金额 | 默认0 |
| year | INT | 预算年份 | 非空 |
| month | INT | 预算月份 | 非空(1-12) |
| created_at | DATETIME | 创建时间 | 默认当前时间 |
| updated_at | DATETIME | 更新时间 | 默认当前时间 |

## 4. API设计

系统提供RESTful API，遵循统一的响应格式。

### 4.1 API响应格式

成功响应：
```json
{
  "data": {
    // 响应数据
  },
  "message": "操作成功",
  "code": 200
}
```

错误响应：
```json
{
  "detail": "错误信息",
  "code": 400
}
```

### 4.2 API端点

#### 4.2.1 用户模块

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| POST | /api/v1/users/register | 注册新用户 | 否 |
| POST | /api/v1/users/login/access-token | 用户登录获取令牌 | 否 |
| GET | /api/v1/users/me | 获取当前用户信息 | 是 |

#### 4.2.2 账户模块

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| POST | /api/v1/accounts/ | 创建新账户 | 是 |
| GET | /api/v1/accounts/ | 获取账户列表 | 是 |
| GET | /api/v1/accounts/{id} | 获取单个账户详情 | 是 |
| PUT | /api/v1/accounts/{id} | 更新账户信息 | 是 |
| DELETE | /api/v1/accounts/{id} | 删除账户 | 是 |

#### 4.2.3 交易模块

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| POST | /api/v1/transactions/ | 创建新交易 | 是 |
| GET | /api/v1/transactions/ | 获取交易列表 | 是 |
| GET | /api/v1/transactions/by-account/{account_id} | 获取单个账户的交易 | 是 |
| GET | /api/v1/transactions/{id} | 获取单个交易详情 | 是 |
| PUT | /api/v1/transactions/{id} | 更新交易信息 | 是 |
| DELETE | /api/v1/transactions/{id} | 删除交易 | 是 |
| GET | /api/v1/transactions/recent | 获取最近交易 | 是 |

#### 4.2.4 分类模块

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| GET | /api/v1/categories/ | 获取分类列表 | 是 |
| GET | /api/v1/categories/{id} | 获取单个分类详情 | 是 |
| POST | /api/v1/categories/ | 创建新分类 | 是 |
| PUT | /api/v1/categories/{id} | 更新分类信息 | 是 |
| DELETE | /api/v1/categories/{id} | 删除分类 | 是 |

#### 4.2.5 预算模块

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| GET | /api/v1/budgets/ | 获取月度预算列表和汇总 | 是 |
| POST | /api/v1/budgets/ | 创建新预算 | 是 |
| PUT | /api/v1/budgets/{id} | 更新预算金额 | 是 |
| DELETE | /api/v1/budgets/{id} | 删除预算 | 是 |

#### 4.2.6 统计分析模块

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| GET | /api/v1/analytics/dashboard | 获取仪表盘数据 | 是 |
| GET | /api/v1/statistics/balance | 获取账户余额统计 | 是 |
| GET | /api/v1/statistics/distribution | 获取资产分布统计 | 是 |
| GET | /api/v1/statistics/trend | 获取资产趋势统计 | 是 |

#### 4.2.7 数据管理模块

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| POST | /api/v1/users/backup | 创建用户数据备份 | 是 |
| GET | /api/v1/users/backups | 获取用户备份文件列表 | 是 |
| GET | /api/v1/users/export | 导出用户数据为Excel | 是 |
| POST | /api/v1/users/restore | 从上传的备份文件恢复数据 | 是 |
| POST | /api/v1/users/restore-latest | 从最新备份文件自动恢复数据 | 是 |
| GET | /api/v1/users/restore-status | 检查数据恢复状态 | 是 |
| DELETE | /api/v1/users/clear-data | 清空用户所有数据 | 是 |
| DELETE | /api/v1/users/account | 删除用户账号 | 是 |

#### 4.2.8 负债账户专用API

负债账户相关的API端点都经过特殊处理，确保正确的业务逻辑：

| API端点 | 负债账户特殊处理 |
|---------|------------------|
| POST /transactions/ | 自动识别负债账户类型，应用正确的余额计算逻辑 |
| PUT /transactions/{id} | 正确撤销和应用负债账户的余额变化 |
| DELETE /transactions/{id} | 正确恢复负债账户的余额状态 |
| GET /analytics/dashboard | 负债账户余额计算到总负债中，不取绝对值 |

#### 4.2.9 默认插入的数据
-- 添加默认支出分类
INSERT INTO categories (name, type, color) VALUES
('餐饮', 'expense', '#FF9500'),
('交通', 'expense', '#007AFF'),
('购物', 'expense', '#FF2D55'),
('娱乐', 'expense', '#5856D6'),
('住房', 'expense', '#FFCC00'),
('水电煤', 'expense', '#4CD964'),
('通讯', 'expense', '#C644FC');

-- 添加默认收入分类
INSERT INTO categories (name, type, color) VALUES
('工资', 'income', '#34C759'),
('奖金', 'income', '#AF52DE'),
('投资收益', 'income', '#30B0C7'); 

### 4.3 为前端UI优化的API特性

后端API设计时特别考虑了前端UI需求，包括：

- **交易记录筛选**：支持多条件组合筛选，便于实现高效的交易记录搜索功能
- **分页和排序**：所有列表API支持分页和排序参数，优化大数据量加载
- **日期范围查询**：支持日期范围参数，用于图表和报表数据
- **数据格式处理**：API返回数据格式优化，减少前端转换工作
- **统计数据聚合**：提供聚合统计API，用于仪表盘和图表展示
- **批量操作支持**：支持批量创建和更新操作，减少请求次数

## 5. 业务逻辑实现

### 5.1 交易处理逻辑

系统支持三种主要交易类型：收入、支出和转账。每种类型的处理逻辑如下：

#### 收入和支出
- 收入交易：增加账户余额
- 支出交易：减少账户余额

#### 转账处理
- 从源账户扣除金额
- 向目标账户增加金额
- 负债账户特殊处理：
  - 资产账户向负债账户转账（还款）：减少源账户余额，减少负债账户余额
  - 负债账户向资产账户转账（借款）：减少负债账户余额，增加目标账户余额
  - 负债账户余额以正数存储，转账逻辑基于此设计

### 5.2 负债账户逻辑
- 负债账户的余额在数据库中以正数表示欠款金额
- 收入交易（还款）时，减少负债账户余额
- 支出交易（借款）时，增加负债账户余额
- 转账到负债账户时，减少负债账户余额（相当于还款）
- 在前端展示时，负债账户余额显示为负数（加负号）

### 5.3 账户余额更新
当交易发生时，系统自动更新相关账户的余额：
- 创建交易：根据交易类型和账户类型调整账户余额
  - 资产账户：收入增加余额，支出减少余额
  - 负债账户：收入减少余额（还款），支出增加余额（借款）
- 更新交易：先撤销旧交易影响，再应用新交易影响
- 删除交易：撤销交易对账户余额的影响
- 转账交易：正确处理不同账户类型间的余额变化

### 5.4 预算管理逻辑
- 用户可以为每个支出类别设置月度预算
- 系统根据交易记录自动计算已使用金额
- 当用户查看预算页面时，后端会：
  1. 计算当月每个预算类别的总支出
  2. 更新预算已用金额
  3. 计算预算使用率和剩余金额
- 预算报表包含总预算、已用金额、剩余金额和使用百分比

### 5.5 数据管理逻辑

#### 备份功能
- 使用`BackupService`创建用户数据的JSON备份
- 备份文件保存在服务器端配置的备份目录中(`BACKUP_DIR`)
- 备份文件使用"用户名_时间戳"格式命名，便于识别和自动恢复
- 备份包含用户的账户、分类、预算和交易数据
- 每次备份后更新用户设置中的`last_backup`时间
- 提供API端点获取用户的所有备份文件列表

#### 导出功能
- 使用`ExcelExportService`将用户数据导出为XLSX格式
- 专注于导出格式化的交易流水，包含时间、分类、类型、金额、账户和备注等信息
- 通过HTTP响应将Excel文件返回给客户端下载

#### 恢复功能
- 提供两种恢复方式：
  1. 从用户上传的JSON备份文件恢复数据
  2. 自动选择最新的备份文件进行恢复（无需用户手动选择文件）
- 恢复过程包括以下步骤：
  1. 删除用户现有的数据(账户、分类、预算、交易)
  2. 从备份文件中读取数据并重新创建
  3. 处理ID映射，确保外键关系正确(新创建的记录会有新ID)
- 恢复操作在数据库事务中执行，确保原子性
- 自动恢复功能特别适合移动端应用场景

#### 数据清空功能
- 清除用户的所有财务数据，包括交易记录、预算、账户和自定义分类
- 保留用户账号本身，相当于恢复到新注册状态
- 操作通过参数化SQL查询执行，确保安全性

#### 账号删除功能
- 标记用户账号为已删除(`deleted=True`)
- 软删除策略，实际数据保留在数据库中但无法访问
- 提供额外的安全检查，确保已删除的账号无法登录

### 5.6 前端UI支持逻辑

虽然系统采用前后端分离架构，但后端设计考虑了前端UI实现需求：

- **响应式布局支持**：API接口返回结构适合各种设备展示
- **高效数据加载**：针对大数据量查询的分页和懒加载支持
- **预处理数据**：后端预处理复杂计算，减轻前端负担
- **错误反馈**：详细的错误信息和状态码，便于前端显示友好提示
- **数据聚合**：提供聚合统计API，用于仪表盘和图表展示
- **图表数据格式**：提供符合ECharts需求的数据结构格式

## 6. 安全设计

### 6.1 认证机制

系统采用JWT (JSON Web Token) 进行用户认证：

1. 用户登录成功后，服务器生成JWT令牌
2. 客户端在后续请求中通过Authorization头部携带令牌
3. 服务器验证令牌的有效性和完整性

### 6.2 密码安全

- 使用bcrypt算法对密码进行哈希存储
- 密码哈希过程包含随机盐值，防止彩虹表攻击
- 不在任何地方明文存储密码

### 6.3 数据安全

- 所有数据库操作使用参数化查询，防止SQL注入
- 实施数据验证和清洗，防止XSS攻击
- 采用软删除策略，防止数据意外丢失
- 用户账号删除时确保账号无法再次登录，避免未授权访问
- 为敏感操作提供额外的验证机制

## 7. 缓存策略

系统使用Redis进行缓存，主要缓存以下数据：

- 用户会话信息
- 频繁访问的账户数据
- 统计报表数据

缓存策略：

- 设置合理的过期时间
- 实施缓存更新和失效机制
- 对计算密集型操作结果进行缓存

## 8. 配置管理

系统采用分层配置管理：

1. 默认配置：硬编码在`app/core/config.py`中
2. 环境变量：通过`.env`文件或系统环境变量提供
3. 运行时配置：可通过API动态修改部分配置

主要配置项：

- 数据库连接参数
- Redis连接参数
- JWT密钥和过期时间
- 日志级别和格式

### 8.1 配置项

系统配置主要包括以下项目：

- **数据库配置**
  - `DB_USER`：数据库用户名
  - `DB_PASSWORD`：数据库密码
  - `DB_HOST`：数据库主机地址
  - `DB_PORT`：数据库端口
  - `DB_NAME`：数据库名称

- **安全配置**
  - `JWT_SECRET_KEY`：JWT密钥
  - `JWT_ALGORITHM`：JWT算法
  - `JWT_ACCESS_TOKEN_EXPIRE_MINUTES`：JWT令牌过期时间

- **服务配置**
  - `API_V1_STR`：API根路径
  - `PROJECT_NAME`：项目名称
  - `BACKUP_DIR`：用户数据备份目录

- **缓存配置**
  - `REDIS_HOST`：Redis主机地址
  - `REDIS_PORT`：Redis端口
  - `REDIS_DB`：Redis数据库索引

## 9. 部署说明

### 9.1 环境要求

- Python 3.8+
- MySQL 5.7+
- Redis 5.0+
- 支持ASGI的Web服务器（如Uvicorn）

### 9.2 部署步骤

1. 克隆代码仓库
2. 安装依赖：`pip install -r requirements.txt`
3. 创建`.env`文件并配置环境变量
4. 执行数据库迁移脚本
5. 启动应用：`uvicorn app.main:app --host 0.0.0.0 --port 8000`

### 9.3 Docker部署

系统支持Docker容器化部署，提供`Dockerfile`和`docker-compose.yml`配置文件。

#### Docker部署步骤

1. 构建Docker镜像：
   ```bash
   docker build -t ledger-backend .
   ```

2. 使用Docker Compose启动服务：
   ```bash
   docker-compose up -d
   ```

3. 使用部署脚本快速部署：
   ```bash
   ./deploy-backend-docker.sh
   ```

## 10. 测试策略

系统采用多层次测试策略：

- 单元测试：测试各个组件的独立功能
- 集成测试：测试组件间的交互
- API测试：测试API端点的功能和性能
- 负载测试：测试系统在高负载下的表现

测试工具：

- pytest：单元测试和集成测试
- Locust：性能和负载测试

## 11. 与前端集成

### 11.1 API优化原则

后端API设计时遵循以下原则，便于前端开发：

- **一致性**：API请求和响应格式保持一致
- **语义化URL**：使用语义明确的URL路径和HTTP方法
- **适当的粒度**：API粒度适中，避免过多请求
- **清晰的错误反馈**：提供明确的错误消息和状态码
- **分页支持**：所有列表类API支持分页
- **版本控制**：通过URL路径实现API版本控制

### 11.2 前端UI交互支持

后端设计特别考虑了前端UI需求：

- **实时验证**：提供用户名、邮箱等字段的实时验证API
- **智能筛选**：交易记录API支持多条件组合筛选
- **图表数据格式**：统计API直接返回适合ECharts使用的数据格式
- **批处理操作**：支持批量操作API，减少网络请求
- **预加载提示**：支持数据量大小提示，优化前端加载体验
- **静态资源处理**：支持前端静态资源的服务和缓存控制

### 11.3 接口文档

系统提供基于OpenAPI规范的详细接口文档：

- 访问`/docs`路径查看Swagger UI文档
- 访问`/redoc`路径查看ReDoc格式文档
- 文档包含每个API的详细参数、响应格式和示例
- 支持从文档页面直接测试API功能

## 12. 未来扩展

已实现的功能扩展：
- 预算管理
- 交易分类管理
- 数据备份与恢复
- 数据导出(Excel格式)
- 账号管理(清空数据和删除账号)
- Docker容器化部署

计划中的功能扩展：
- 多币种支持
- 财务目标设置
- 投资收益分析
- 账单提醒
- 数据导入(从其他金融软件)
- API权限精细控制
- 前端UI适配优化支持

技术扩展：
- 微服务架构演进
- 消息队列集成
- 全文搜索功能
- 实时数据更新
- WebSocket支持实时通知
- 集成第三方金融数据API
