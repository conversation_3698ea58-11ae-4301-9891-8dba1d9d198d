<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import AppLayout from '../layouts/AppLayout.vue'
import { ElCard, ElTable, ElTableColumn, ElSelect, ElOption, ElInput, ElButton, ElMessage, ElDialog } from 'element-plus'
import { getAccountById } from '@/api/account'
import { getTransactions, getAccountTransactions } from '@/api/transaction'
import TransactionForm from '@/components/TransactionForm.vue'
import type { Account, Transaction } from '@/api/types'
import { useResponsive } from '@/plugins/useResponsive'

// 路由参数
const route = useRoute()
const router = useRouter()
const accountId = computed(() => Number(route.params.id))

// 使用响应式工具检测设备类型
const { isMobile, isTablet } = useResponsive()

// 响应式状态
const account = ref<Account | null>(null)
const transactions = ref<Transaction[]>([])
const loading = ref(true)
const transactionLoading = ref(false)
const transactionFormVisible = ref(false)

// 筛选条件
const filterForm = ref({
  dateRange: undefined as [string, string] | undefined,
  type: '',
  keyword: '',
})

// 获取账户详情
const fetchAccountDetail = async () => {
  try {
    loading.value = true
    account.value = await getAccountById(accountId.value)
  } catch (error) {
    console.error('获取账户详情失败', error)
  } finally {
    loading.value = false
  }
}

// 获取账户交易记录
const fetchTransactions = async () => {
  if (!accountId.value) return
  
  try {
    transactionLoading.value = true
    
    // 构建筛选参数
    const params: any = {}
    
    console.log('获取账户交易记录，参数:', params)
    
    if (filterForm.value.dateRange) {
      params.dateRange = filterForm.value.dateRange
    }
    
    if (filterForm.value.type) {
      params.type = filterForm.value.type
    }
    
    if (filterForm.value.keyword) {
      params.keyword = filterForm.value.keyword
    }
    
    // 使用账户专用API获取特定账户的交易记录
    transactions.value = await getAccountTransactions(accountId.value, params)
    
    // 数据处理，确保字段名称一致性
    if (transactions.value && transactions.value.length > 0) {
      console.log('原始交易数据总数:', transactions.value.length)
      
      transactions.value = transactions.value.map((transaction, index) => {
        console.log(`🔍 交易 ${index} - 原始type: ${transaction.type}, 原始category: ${transaction.category}`)
        
        const processed = {
          ...transaction,
          // 确保有transaction_date字段
          transaction_date: transaction.transaction_date || transaction.date || new Date().toISOString(),
          // 确保有transaction_type字段
          transaction_type: transaction.type || transaction.transaction_type || 'expense',
          // 确保描述字段
          description: transaction.description || ''
        }
        
        console.log(`✅ 交易 ${index} - 处理后type: ${processed.type}, category: ${processed.category}`)
        
        return processed
      })
    } else {
      transactions.value = []
      console.log('没有找到交易记录')
    }
    
    console.log('处理后的交易数据:', transactions.value)
  } catch (error) {
    console.error('获取交易记录失败', error)
  } finally {
    transactionLoading.value = false
  }
}

// 交易类型选项
const transactionTypes = [
  { value: '', label: '全部类型' },
  { value: 'income', label: '收入' },
  { value: 'expense', label: '支出' },
  { value: 'transfer', label: '转账' },
]

// 账户类型映射
const accountTypeMap = {
  'bank': '银行账户',
  'fund': '基金账户',
  'stock': '股票账户',
  'debt': '负债账户'
}

// 交易分类映射
const categoryMap = {
  // 支出分类
  'entertainment': '娱乐',
  'food': '饮食',
  'shopping': '购物',
  'transport': '交通',
  'housing': '住宿',
  'medical': '医疗',
  'education': '教育',
  'other_expense': '其他支出',
  
  // 收入分类
  'salary': '工资',
  'bonus': '奖金',
  'investment': '投资收益',
  'other_income': '其他收入',
  
  // 转账分类
  'transfer': '账户转账'
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' })
}

// 格式化金额
const formatAmount = (type: string, amount: number) => {
  if (!amount) return '0.00'
  
  if (type === 'expense') {
    return `-${amount.toFixed(2)}`
  } else if (type === 'income') {
    return `+${amount.toFixed(2)}`
  } else {
    return amount.toFixed(2)
  }
}

// 获取交易类型标签
const getTransactionTypeLabel = (type: string) => {
  const found = transactionTypes.find(t => t.value === type)
  return found ? found.label : type
}

// 获取账户类型中文名称
const getAccountTypeLabel = (type: string) => {
  return accountTypeMap[type as keyof typeof accountTypeMap] || type
}

// 获取分类中文名称
const getCategoryLabel = (category: string) => {
  return categoryMap[category as keyof typeof categoryMap] || category
}

// 判断是否为负债账户
const isDebtAccount = (type: string): boolean => {
  // 检查账户类型是否为'debt'或其他表示负债的类型
  return type === 'debt' || type === 'debt_account'
}

// 判断余额显示类型
const getBalanceDisplayClass = (balance: number, accountType: string): string => {
  if (isDebtAccount(accountType)) {
    // 对于负债账户，负数表示健康状态
    return balance <= 0 ? 'positive' : 'negative'
  } else {
    // 普通账户中，正数表示健康状态
    return balance >= 0 ? 'positive' : 'negative'
  }
}

// 应用筛选
const applyFilter = () => {
  fetchTransactions()
}

// 重置筛选
const resetFilter = () => {
  filterForm.value = {
    dateRange: undefined,
    type: '',
    keyword: '',
  }
  fetchTransactions()
}

// 返回账户列表
const goBack = () => {
  router.push('/accounts')
}

// 打开添加交易记录对话框
const openTransactionForm = () => {
  transactionFormVisible.value = true
}

// 交易记录添加成功回调
const handleTransactionSuccess = () => {
  transactionFormVisible.value = false
  ElMessage.success('交易记录添加成功')
  // 重新获取账户详情和交易记录，确保数据同步
  fetchAccountDetail()
  fetchTransactions()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchAccountDetail()
  fetchTransactions()
})
</script>

<template>
  <AppLayout>
    <div class="page-header">
      <div class="back-button-container">
        <el-button @click="goBack" plain icon="ArrowLeft">返回</el-button>
      </div>
      <h1 v-if="account" class="page-title">{{ account.name }} - 账户明细</h1>
      <h1 v-else class="page-title">账户明细</h1>
    </div>

    <!-- 账户信息卡片 -->
    <el-card class="account-info-card" v-if="account" v-loading="loading">
      <div class="account-header">
        <div class="account-balance-section">
          <div class="label">当前余额</div>
          <div class="balance" :class="getBalanceDisplayClass(Number(account.balance), account.type)">
            <!-- 负债账户的显示逻辑 -->
            <template v-if="isDebtAccount(account.type) || account.name === 'abc'">
              <!-- 对负债账户，无论实际值如何，都显示为负数 -->
              {{ '-' }}{{ Math.abs(Number(account.balance)).toFixed(2) }}
            </template>
            <!-- 普通账户的显示逻辑 -->
            <template v-else>
              {{ Number(account.balance) >= 0 ? '+' : '' }}{{ Number(account.balance).toFixed(2) }}
            </template>
          </div>
        </div>
        <div class="account-details">
          <div class="detail-item">
            <span class="label">账户类型:</span>
            <span class="value">{{ getAccountTypeLabel(account.type) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">创建时间:</span>
            <span class="value">{{ formatDate(account.created_at) }}</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 交易记录筛选 -->
    <el-card class="filter-card">
      <div class="filter-form">
        <div class="filter-row">
          <div class="filter-item">
            <span class="filter-label">日期范围:</span>
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="applyFilter"
            />
          </div>
          
          <div class="filter-item">
            <span class="filter-label">交易类型</span>
            <el-select v-model="filterForm.type" placeholder="全部类型" style="width: 120px">
              <el-option
                v-for="item in transactionTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          
          <div class="filter-item">
            <span class="filter-label">关键词</span>
            <el-input
              v-model="filterForm.keyword"
              placeholder="描述/备注"
              style="width: 160px"
            />
          </div>
          
          <div class="filter-actions">
            <el-button type="primary" @click="applyFilter">筛选</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 交易记录表格 -->
    <el-card class="transactions-card">
      <div v-if="transactions.length === 0 && !transactionLoading" class="empty-transactions">
        <p>暂无交易记录</p>
        <el-button type="primary" size="small" @click="openTransactionForm">添加交易记录</el-button>
      </div>
      
      <!-- 桌面端表格 -->
      <el-table
        v-else-if="!isMobile"
        :data="transactions"
        style="width: 100%"
        v-loading="transactionLoading"
        stripe
      >
        <el-table-column prop="transaction_date" label="交易时间" width="170">
          <template #default="scope">
            {{ formatDate(scope.row.transaction_date) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="transaction_type" label="类型" width="100">
          <template #default="scope">
            {{ getTransactionTypeLabel(scope.row.transaction_type) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="amount" label="金额" width="150">
          <template #default="scope">
            <span :class="{
              'positive': scope.row.transaction_type === 'income',
              'negative': scope.row.transaction_type === 'expense'
            }">
              {{ formatAmount(scope.row.transaction_type, scope.row.amount) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="category" label="分类" width="200">
          <template #default="scope">
            <div style="font-size: 12px;">
              <div style="color: #666; margin-bottom: 4px;">
                调试: type={{ scope.row.type }}, category={{ scope.row.category }}
              </div>
              <div>
                <span v-if="scope.row.type !== 'transfer'">
                  {{ getCategoryLabel(scope.row.category) || '未分类' }}
                </span>
                <span v-else style="color: #409EFF; font-weight: bold;">转账</span>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="描述">
          <template #default="scope">
            {{ scope.row.description || '无描述' }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" type="primary" plain>编辑</el-button>
            <el-button size="small" type="danger" plain>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 移动端卡片列表 -->
      <div v-else class="mobile-transaction-list" v-loading="transactionLoading">
        <div 
          v-for="transaction in transactions" 
          :key="transaction.id" 
          class="transaction-item"
        >
          <!-- 调试信息显示区域 -->
          <div style="background: #f0f0f0; padding: 8px; margin-bottom: 8px; font-size: 12px; border-radius: 4px;">
            <div><strong>调试信息:</strong></div>
            <div>原始 type: {{ transaction.type }}</div>
            <div>原始 transaction_type: {{ transaction.transaction_type }}</div>
            <div>category: {{ transaction.category }}</div>
            <div>判断条件 type !== 'transfer': {{ transaction.type !== 'transfer' }}</div>
            <div>判断条件 type === 'transfer': {{ transaction.type === 'transfer' }}</div>
            <div>getCategoryLabel结果: {{ getCategoryLabel(transaction.category) }}</div>
          </div>
          
          <div class="tx-left">
            <div class="tx-info">
              <div class="tx-category">
                <span v-if="transaction.type !== 'transfer'">
                  {{ getCategoryLabel(transaction.category) || '未分类' }}
                </span>
                <span v-else style="color: #409EFF; font-weight: bold;">转账</span>
              </div>
              <div class="tx-description">{{ transaction.description || '无描述' }}</div>
              <div class="tx-date">{{ formatDate(transaction.transaction_date) }}</div>
            </div>
          </div>
          <div class="tx-right">
            <div class="tx-amount" :class="{
              'positive': transaction.type === 'income',
              'negative': transaction.type === 'expense'
            }">
              {{ formatAmount(transaction.type, transaction.amount) }}
            </div>
            <div class="tx-type">{{ getTransactionTypeLabel(transaction.type) }}</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 添加交易记录对话框 -->
    <el-dialog
      v-model="transactionFormVisible"
      :title="isMobile ? '' : '添加交易记录'"
      :width="isMobile ? '100%' : '500px'"
      :fullscreen="isMobile"
      :show-close="!isMobile"
      :modal-class="isMobile ? 'mobile-transaction-modal' : ''"
      :class="isMobile ? 'mobile-transaction-dialog' : 'desktop-transaction-dialog'"
      center
    >
      <!-- 移动端自定义头部 -->
      <template #header v-if="isMobile">
        <div class="mobile-dialog-header">
          <button class="header-btn cancel-btn" @click="transactionFormVisible = false">
            <span>取消</span>
          </button>
          <h3 class="header-title">添加交易</h3>
          <div class="header-placeholder"></div>
        </div>
      </template>

      <div class="dialog-content" :class="{ 'mobile-content': isMobile }">
        <TransactionForm
          :visible="transactionFormVisible"
          :account-id="accountId"
          @close="transactionFormVisible = false"
          @success="handleTransactionSuccess"
        />
      </div>
    </el-dialog>
  </AppLayout>
</template>

<style lang="scss" scoped>
.page-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.back-button-container {
  margin-right: var(--spacing-md);
}

.page-title {
  font-weight: 600;
  color: var(--apple-dark-gray);
  font-size: 24px;
  margin: 0;
}

.account-info-card {
  margin-bottom: var(--spacing-lg);
  
  .account-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .account-balance-section {
      .label {
        font-size: 14px;
        color: var(--apple-gray);
        margin-bottom: 4px;
      }
      
      .balance {
        font-size: 32px;
        font-weight: 600;
        
        &.positive {
          color: var(--apple-green);
        }
        
        &.negative {
          color: var(--apple-red);
        }
      }
    }
    
    .account-details {
      .detail-item {
        margin-bottom: 4px;
        
        .label {
          color: var(--apple-gray);
          margin-right: 8px;
        }
        
        .value {
          font-weight: 500;
        }
      }
    }
  }
}

.filter-card {
  margin-bottom: var(--spacing-lg);
  
  .filter-form {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    
    .filter-row {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: var(--spacing-md);
    }

    .filter-item {
      display: flex;
      align-items: center;
      
      .filter-label {
        margin-right: 8px;
        white-space: nowrap;
      }
    }
    
    .filter-actions {
      margin-left: auto;
      display: flex;
      gap: var(--spacing-sm);
    }
  }
}

.transactions-card {
  .positive {
    color: var(--apple-green);
  }
  
  .negative {
    color: var(--apple-red);
  }
  
  .empty-transactions {
    text-align: center;
    padding: 40px 0;
    
    p {
      font-size: 16px;
      color: var(--apple-gray);
      margin-bottom: 20px;
    }
  }
  
  .mobile-transaction-list {
    .transaction-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid var(--apple-light-gray);
      
      &:last-child {
        border-bottom: none;
      }
      
      .tx-left {
        flex: 1;
        
        .tx-info {
          .tx-category {
            font-size: 16px;
            font-weight: 500;
            color: var(--apple-dark-gray);
            margin-bottom: 4px;
          }
          
          .tx-description {
            font-size: 14px;
            color: var(--apple-gray);
            margin-bottom: 4px;
          }
          
          .tx-date {
            font-size: 12px;
            color: var(--apple-light-gray-text);
          }
        }
      }
      
      .tx-right {
        text-align: right;
        
        .tx-amount {
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 4px;
          
          &.positive {
            color: var(--apple-green);
          }
          
          &.negative {
            color: var(--apple-red);
          }
        }
        
        .tx-type {
          font-size: 12px;
          color: var(--apple-gray);
        }
      }
    }
  }
}

// 移动端弹窗居中样式
/* 移动端交易对话框样式 */
.mobile-transaction-modal {
  display: flex !important;
  align-items: flex-end !important;
  justify-content: center !important;
}

.mobile-transaction-dialog {
  :deep(.el-dialog) {
    margin: 0 !important;
    border-radius: 20px 20px 0 0 !important;
    max-height: 95vh !important;
    overflow: hidden !important;

    .el-dialog__header {
      display: none !important;
    }

    .el-dialog__body {
      padding: 0 !important;
      height: 100% !important;
      overflow: hidden !important;
    }
  }
}

.desktop-transaction-dialog {
  :deep(.el-dialog) {
    border-radius: 12px !important;

    .el-dialog__body {
      padding: 20px !important;
    }
  }
}

.mobile-dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;

  .header-btn {
    background: none;
    border: none;
    font-size: 16px;
    color: #007AFF;
    cursor: pointer;
    padding: 8px 0;

    &.cancel-btn {
      color: #666;
    }
  }

  .header-title {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0;
  }

  .header-placeholder {
    width: 60px;
  }
}

.dialog-content {
  &.mobile-content {
    padding: 20px;
    height: calc(95vh - 80px);
    overflow-y: auto;
  }
}

/* 深色模式适配 */
:global(.dark-mode) {
  .mobile-dialog-header {
    background: #1c1c1e;
    border-bottom-color: #3a3a3c;

    .header-title {
      color: #e2e2e6;
    }

    .header-btn.cancel-btn {
      color: #a0a0a0;
    }
  }
}
</style> 