<!-- 详细财务分析页面 -->
<view class="detail-analytics-page">
  <!-- 导航栏 -->
  <view class="nav-header">
    <view class="nav-back" bindtap="goBack">
      <text class="back-icon">←</text>
    </view>
    <text class="nav-title">详细分析</text>
    <view class="nav-right">
      <view class="share-btn" bindtap="shareReport">
        <text class="share-icon">📤</text>
      </view>
    </view>
  </view>

  <!-- 时间范围选择 -->
  <view class="time-range-selector">
    <view class="range-tabs">
      <view class="range-tab {{timeRange === 'month' ? 'active' : ''}}" bindtap="switchTimeRange" data-range="month">
        <text>本月</text>
      </view>
      <view class="range-tab {{timeRange === 'quarter' ? 'active' : ''}}" bindtap="switchTimeRange" data-range="quarter">
        <text>本季度</text>
      </view>
      <view class="range-tab {{timeRange === 'year' ? 'active' : ''}}" bindtap="switchTimeRange" data-range="year">
        <text>本年</text>
      </view>
      <view class="range-tab {{timeRange === 'custom' ? 'active' : ''}}" bindtap="switchTimeRange" data-range="custom">
        <text>自定义</text>
      </view>
    </view>
    
    <view wx:if="{{timeRange === 'custom'}}" class="custom-date-picker">
      <picker mode="date" value="{{startDate}}" bindchange="onStartDateChange">
        <view class="date-input">
          <text>{{startDate || '开始日期'}}</text>
        </view>
      </picker>
      <text class="date-separator">至</text>
      <picker mode="date" value="{{endDate}}" bindchange="onEndDateChange">
        <view class="date-input">
          <text>{{endDate || '结束日期'}}</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 核心指标卡片 -->
  <view class="metrics-cards">
    <view class="metric-card">
      <text class="metric-label">总收入</text>
      <text class="metric-value income">{{hideAmounts ? '****' : '¥' + totalIncome}}</text>
      <text class="metric-change {{incomeChange >= 0 ? 'positive' : 'negative'}}">
        {{incomeChange >= 0 ? '+' : ''}}{{incomeChange}}%
      </text>
    </view>
    
    <view class="metric-card">
      <text class="metric-label">总支出</text>
      <text class="metric-value expense">{{hideAmounts ? '****' : '¥' + totalExpense}}</text>
      <text class="metric-change {{expenseChange >= 0 ? 'negative' : 'positive'}}">
        {{expenseChange >= 0 ? '+' : ''}}{{expenseChange}}%
      </text>
    </view>
    
    <view class="metric-card">
      <text class="metric-label">净收入</text>
      <text class="metric-value {{netIncome >= 0 ? 'income' : 'expense'}}">
        {{hideAmounts ? '****' : (netIncome >= 0 ? '+¥' : '-¥') + Math.abs(netIncome).toFixed(2)}}
      </text>
      <text class="metric-change {{netIncomeChange >= 0 ? 'positive' : 'negative'}}">
        {{netIncomeChange >= 0 ? '+' : ''}}{{netIncomeChange}}%
      </text>
    </view>
    
    <view class="metric-card">
      <text class="metric-label">储蓄率</text>
      <text class="metric-value">{{savingsRate}}%</text>
      <text class="metric-change {{savingsRateChange >= 0 ? 'positive' : 'negative'}}">
        {{savingsRateChange >= 0 ? '+' : ''}}{{savingsRateChange}}%
      </text>
    </view>
  </view>

  <!-- 支出分析 -->
  <view class="expense-analysis">
    <view class="section-header">
      <text class="section-title">支出分析</text>
      <view class="view-toggle">
        <text class="toggle-item {{expenseViewType === 'category' ? 'active' : ''}}" 
              bindtap="switchExpenseView" data-type="category">分类</text>
        <text class="toggle-item {{expenseViewType === 'trend' ? 'active' : ''}}" 
              bindtap="switchExpenseView" data-type="trend">趋势</text>
      </view>
    </view>

    <view wx:if="{{expenseViewType === 'category'}}" class="category-analysis">
      <view class="top-categories">
        <view class="category-rank" wx:for="{{topExpenseCategories}}" wx:key="id">
          <view class="rank-number">{{index + 1}}</view>
          <view class="category-info">
            <text class="category-icon">{{item.icon}}</text>
            <view class="category-details">
              <text class="category-name">{{item.name}}</text>
              <text class="category-amount">{{hideAmounts ? '****' : '¥' + item.amountText}}</text>
            </view>
          </view>
          <view class="category-percentage">
            <text class="percentage-text">{{item.percentage}}%</text>
            <view class="percentage-bar">
              <view class="bar-fill" style="width: {{item.percentage}}%"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view wx:if="{{expenseViewType === 'trend'}}" class="trend-analysis">
      <view class="trend-chart-container">
        <!-- 这里可以集成图表组件 -->
        <text class="chart-placeholder">支出趋势图表</text>
      </view>
    </view>
  </view>

  <!-- 收入分析 -->
  <view class="income-analysis">
    <view class="section-header">
      <text class="section-title">收入分析</text>
    </view>

    <view class="income-sources">
      <view class="source-item" wx:for="{{incomeCategories}}" wx:key="id">
        <view class="source-info">
          <text class="source-icon">{{item.icon}}</text>
          <text class="source-name">{{item.name}}</text>
        </view>
        <view class="source-amount">
          <text class="amount-text">{{hideAmounts ? '****' : '¥' + item.amountText}}</text>
          <text class="percentage-text">{{item.percentage}}%</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 月度对比 -->
  <view class="monthly-comparison">
    <view class="section-header">
      <text class="section-title">月度对比</text>
    </view>

    <view class="comparison-chart">
      <view class="month-comparison" wx:for="{{monthlyComparison}}" wx:key="month">
        <view class="month-info">
          <text class="month-name">{{item.monthText}}</text>
          <view class="month-bars">
            <view class="income-bar-comp" style="height: {{item.incomeHeight}}%"></view>
            <view class="expense-bar-comp" style="height: {{item.expenseHeight}}%"></view>
          </view>
          <view class="month-amounts" wx:if="{{!hideAmounts}}">
            <text class="income-text">+{{item.incomeText}}</text>
            <text class="expense-text">-{{item.expenseText}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 财务建议 -->
  <view class="financial-advice">
    <view class="section-header">
      <text class="section-title">财务建议</text>
    </view>

    <view class="advice-list">
      <view class="advice-item" wx:for="{{financialAdvice}}" wx:key="id">
        <view class="advice-icon {{item.type}}">{{item.icon}}</view>
        <view class="advice-content">
          <text class="advice-title">{{item.title}}</text>
          <text class="advice-description">{{item.description}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
