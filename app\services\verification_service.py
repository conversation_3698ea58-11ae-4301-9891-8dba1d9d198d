"""
邮箱验证码服务
"""
import random
import string
from datetime import datetime, timedelta
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from app.models.email_verification import EmailVerification
from app.services.email_service import EmailService
import logging

logger = logging.getLogger(__name__)


class VerificationService:
    """邮箱验证码服务"""
    
    def __init__(self):
        self.email_service = EmailService()
    
    def generate_code(self, length: int = 6) -> str:
        """生成验证码"""
        return ''.join(random.choices(string.digits, k=length))
    
    async def send_verification_code(
        self, 
        db: AsyncSession, 
        email: str, 
        verification_type: str = "register"
    ) -> bool:
        """发送验证码"""
        try:
            # 生成验证码
            code = self.generate_code()
            expires_at = datetime.utcnow() + timedelta(minutes=15)
            
            logger.info(f"生成验证码: {email}, 类型: {verification_type}, 验证码: {code}")
            
            # 删除该邮箱的旧验证码
            await self._cleanup_old_codes(db, email, verification_type)
            
            # 保存新验证码
            verification = EmailVerification(
                email=email,
                verification_code=code,
                verification_type=verification_type,
                expires_at=expires_at,
                used=False
            )
            db.add(verification)
            await db.commit()
            
            logger.info(f"验证码已保存到数据库: {email}")
            
            # 发送邮件
            subject = "【soulgo账本】注册验证码" if verification_type == "register" else "【soulgo账本】密码重置验证码"
            html_content = self._generate_email_content(code, verification_type)
            
            success = await self.email_service.send_email(email, subject, html_content)
            
            if success:
                logger.info(f"验证码邮件发送成功: {email}")
                return True
            else:
                logger.error(f"验证码邮件发送失败: {email}")
                # 发送失败时删除验证码记录
                await db.delete(verification)
                await db.commit()
                return False
                
        except Exception as e:
            logger.error(f"发送验证码失败: {email}, 错误: {str(e)}")
            await db.rollback()
            return False
    
    async def verify_code(
        self, 
        db: AsyncSession, 
        email: str, 
        code: str, 
        verification_type: str = "register"
    ) -> bool:
        """验证验证码"""
        try:
            # 查找有效的验证码
            stmt = select(EmailVerification).where(
                and_(
                    EmailVerification.email == email,
                    EmailVerification.verification_code == code,
                    EmailVerification.verification_type == verification_type,
                    EmailVerification.used == False,
                    EmailVerification.expires_at > datetime.utcnow()
                )
            )
            result = await db.execute(stmt)
            verification = result.scalar_one_or_none()
            
            if verification:
                # 标记为已使用
                verification.used = True
                await db.commit()
                logger.info(f"验证码验证成功: {email}")
                return True
            else:
                logger.warning(f"验证码无效或已过期: {email}, 验证码: {code}")
                return False
                
        except Exception as e:
            logger.error(f"验证验证码失败: {email}, 错误: {str(e)}")
            return False
    
    async def _cleanup_old_codes(
        self, 
        db: AsyncSession, 
        email: str, 
        verification_type: str
    ):
        """清理旧的验证码"""
        try:
            stmt = select(EmailVerification).where(
                and_(
                    EmailVerification.email == email,
                    EmailVerification.verification_type == verification_type,
                    EmailVerification.used == False
                )
            )
            result = await db.execute(stmt)
            old_codes = result.scalars().all()
            
            for old_code in old_codes:
                await db.delete(old_code)
            
            logger.info(f"清理旧验证码: {email}, 清理数量: {len(old_codes)}")
            
        except Exception as e:
            logger.error(f"清理旧验证码失败: {email}, 错误: {str(e)}")
    
    def _generate_email_content(self, code: str, verification_type: str) -> str:
        """生成邮件内容"""
        if verification_type == "register":
            title = "注册验证码"
            description = "感谢您注册soulgo账本！请使用以下验证码完成邮箱验证："
        else:
            title = "密码重置验证码"
            description = "您正在重置soulgo账本的密码，请使用以下验证码："
        
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>{title}</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                    <h1 style="margin: 0; font-size: 28px;">{title}</h1>
                </div>
                
                <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
                    <p style="font-size: 16px; margin-bottom: 20px;">您好！</p>
                    
                    <p style="font-size: 16px; margin-bottom: 20px;">
                        {description}
                    </p>
                    
                    <div style="background: white; border: 2px dashed #667eea; padding: 20px; text-align: center; margin: 30px 0; border-radius: 8px;">
                        <span style="font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 8px;">{code}</span>
                    </div>
                    
                    <p style="font-size: 14px; color: #666; margin-bottom: 20px;">
                        <strong>注意事项：</strong><br>
                        • 验证码有效期为15分钟<br>
                        • 请勿将验证码告知他人<br>
                        • 如果您没有进行此操作，请忽略此邮件
                    </p>
                    
                    <div style="border-top: 1px solid #ddd; padding-top: 20px; margin-top: 30px; text-align: center;">
                        <p style="font-size: 12px; color: #999; margin: 0;">
                            此邮件由系统自动发送，请勿回复<br>
                            © 2024 soulgo账本
                        </p>
                    </div>
                </div>
            </div>
        </body>
        </html>
        """
