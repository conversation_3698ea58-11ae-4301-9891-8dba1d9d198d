// pages/settings/profile.js
const api = require('../../utils/api.js')

Page({
  data: {
    userInfo: {},
    originalUserInfo: {},
    editMode: false,
    saving: false
  },

  onLoad() {
    console.log('个人信息页面加载')
    this.loadUserInfo()
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      // 从本地存储获取用户信息
      const localUserInfo = wx.getStorageSync('userInfo')
      if (localUserInfo) {
        const userInfo = JSON.parse(localUserInfo)
        this.setData({
          userInfo: userInfo,
          originalUserInfo: JSON.parse(JSON.stringify(userInfo))
        })
      }

      // 从服务器获取最新用户信息
      const response = await api.auth.getUserInfo()
      this.setData({
        userInfo: response,
        originalUserInfo: JSON.parse(JSON.stringify(response))
      })
      
      // 更新本地存储
      wx.setStorageSync('userInfo', JSON.stringify(response))
      
    } catch (error) {
      console.error('获取用户信息失败:', error)
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      })
    }
  },

  // 格式化日期
  formatDate(dateString) {
    if (!dateString) return '未知'
    const date = new Date(dateString)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  },

  // 验证用户信息
  validateUserInfo(userInfo) {
    // 用户名验证
    if (!userInfo.username || !userInfo.username.trim()) {
      return { isValid: false, message: '请输入用户名' }
    }

    if (userInfo.username.trim().length < 3) {
      return { isValid: false, message: '用户名至少需要3个字符' }
    }

    if (userInfo.username.trim().length > 20) {
      return { isValid: false, message: '用户名不能超过20个字符' }
    }

    // 用户名格式验证
    const usernameRegex = /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/
    if (!usernameRegex.test(userInfo.username.trim())) {
      return { isValid: false, message: '用户名只能包含字母、数字、下划线和中文' }
    }

    // 邮箱验证
    if (!userInfo.email || !userInfo.email.trim()) {
      return { isValid: false, message: '请输入邮箱' }
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(userInfo.email.trim())) {
      return { isValid: false, message: '邮箱格式不正确' }
    }

    // 手机号验证（可选）
    if (userInfo.phone && userInfo.phone.trim()) {
      const phoneRegex = /^1[3-9]\d{9}$/
      if (!phoneRegex.test(userInfo.phone.trim())) {
        return { isValid: false, message: '手机号格式不正确' }
      }
    }

    return { isValid: true, message: '' }
  },

  // 用户名输入
  onUsernameInput(e) {
    this.setData({
      'userInfo.username': e.detail.value
    })
  },

  // 邮箱输入
  onEmailInput(e) {
    this.setData({
      'userInfo.email': e.detail.value
    })
  },

  // 手机号输入
  onPhoneInput(e) {
    this.setData({
      'userInfo.phone': e.detail.value
    })
  },

  // 进入编辑模式
  enterEditMode() {
    this.setData({
      editMode: true
    })
  },

  // 取消编辑
  cancelEdit() {
    this.setData({
      editMode: false,
      userInfo: JSON.parse(JSON.stringify(this.data.originalUserInfo))
    })
  },

  // 保存个人信息
  async saveProfile() {
    const { userInfo } = this.data

    // 表单验证
    const validationResult = this.validateUserInfo(userInfo)
    if (!validationResult.isValid) {
      wx.showToast({
        title: validationResult.message,
        icon: 'none'
      })
      return
    }

    this.setData({ saving: true })

    try {
      // 调用更新用户信息API
      const updateData = {
        username: userInfo.username.trim(),
        email: userInfo.email.trim(),
        phone: userInfo.phone ? userInfo.phone.trim() : null
      }

      const response = await api.put('/users/me', updateData)
      
      // 更新本地存储
      wx.setStorageSync('userInfo', JSON.stringify(response))
      
      this.setData({
        editMode: false,
        userInfo: response,
        originalUserInfo: JSON.parse(JSON.stringify(response))
      })

      wx.showToast({
        title: '保存成功',
        icon: 'success'
      })

    } catch (error) {
      console.error('保存用户信息失败:', error)
      wx.showToast({
        title: error.message || '保存失败',
        icon: 'none'
      })
    } finally {
      this.setData({ saving: false })
    }
  },

  // 更换头像
  changeAvatar() {
    wx.showActionSheet({
      itemList: ['从相册选择', '拍照'],
      success: (res) => {
        const sourceType = res.tapIndex === 0 ? ['album'] : ['camera']

        wx.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: sourceType,
          success: (chooseRes) => {
            const tempFilePath = chooseRes.tempFilePaths[0]
            console.log('选择的头像:', tempFilePath)

            // 显示预览
            wx.previewImage({
              urls: [tempFilePath],
              success: () => {
                // 这里可以上传头像到服务器
                wx.showModal({
                  title: '头像上传',
                  content: '头像上传功能正在开发中，敬请期待！',
                  showCancel: false
                })
              }
            })
          },
          fail: (error) => {
            console.error('选择头像失败:', error)
            wx.showToast({
              title: '选择头像失败',
              icon: 'none'
            })
          }
        })
      }
    })
  },

  // 修改密码
  changePassword() {
    wx.showModal({
      title: '修改密码',
      content: '密码修改功能开发中',
      showCancel: false
    })
  }
})
