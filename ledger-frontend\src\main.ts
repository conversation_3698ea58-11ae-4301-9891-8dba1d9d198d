import './assets/main.css'
import './assets/dark-mode.css'  // 导入深色模式样式
import './assets/critical-fixes.css'  // 导入关键修复样式
import './assets/text-visibility-fix.css'  // 导入文本可见性修复样式
import './assets/page-specific-fixes.css'  // 导入页面特定修复样式
import './assets/echarts-dark-fix.css'  // 导入ECharts深色模式修复样式
import './assets/mobile.css'  // 导入移动端适配样式
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import darkModeDetector from './plugins/darkModeDetector'
// 改为在组件中按需导入
// import 'vant/lib/index.css'

// 开发环境下启用mock服务
import setupMock, { resetMock } from './mock'

const isDev = import.meta.env.DEV
// 通过调整此变量控制是否使用Mock服务
// true: 使用Mock服务 - 前端模拟API响应，不需要后端
// false: 使用真实后端API - 需要后端服务运行在端口8000
const useMockService = false // 禁用Mock服务，使用真实后端API

// 确保禁用所有mock服务
resetMock();

if (isDev && useMockService) {
  console.log('开发环境启用Mock服务')
  setupMock()
} else {
  console.log('使用真实后端API')
}

// 初始化应用程序
const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
})

// 全局组件注册
// app.component('CustomDatePicker', CustomDatePicker) // 移除此行

// 初始化深色模式检测器
darkModeDetector.init()

app.mount('#app')
