# 账本管理系统前端文档

## 项目概述

这是一个基于Vue 3的现代账本管理系统前端项目，提供了用户登录、注册、账户管理、交易记录、预算管理和财务分析等功能。项目采用了最新的前端技术栈，具有美观的UI和流畅的用户体验，同时支持响应式设计，可在桌面端和移动端完美运行。

## 技术栈

- **Vue 3**: 使用组合式API
- **TypeScript**: 类型安全的JavaScript超集
- **Vite**: 现代前端构建工具
- **Element Plus**: UI组件库
- **Axios**: HTTP客户端
- **Pinia**: 状态管理
- **Vue Router**: 路由管理
- **ECharts**: 图表可视化
- **Vitest & Playwright**: 测试框架
- **Vant**: 移动端组件库（按需导入）

## 项目结构

```
ledger-frontend/
├── dist/                 # 构建输出目录
├── node_modules/         # 依赖包
├── public/               # 静态资源
├── src/                  # 源代码
│   ├── api/              # API接口
│   │   ├── account.ts    # 账户相关API
│   │   ├── analytics.ts  # 分析相关API
│   │   ├── budget.ts     # 预算相关API
│   │   ├── category.ts   # 分类相关API
│   │   ├── config.ts     # API配置
│   │   ├── transaction.ts # 交易相关API
│   │   ├── types.ts      # API类型定义
│   │   └── user.ts       # 用户相关API
│   ├── assets/           # 静态资源
│   │   ├── main.css      # 主样式文件
│   │   ├── dark-mode.css # 深色模式样式
│   │   ├── mobile.css    # 移动端适配样式
│   │   └── ... 其他样式文件
│   ├── components/       # 公共组件
│   │   ├── DebtTransactionForm.vue # 负债账户交易表单
│   │   ├── TransferForm.vue       # 转账表单组件
│   │   └── ... 其他组件
│   ├── layouts/          # 布局组件
│   │   └── AppLayout.vue # 主应用布局
│   ├── router/           # 路由配置
│   ├── stores/           # Pinia状态管理
│   │   ├── user.ts       # 用户状态
│   │   ├── account.ts    # 账户状态
│   │   └── ... 其他状态存储
│   ├── mock/             # 模拟数据（开发环境）
│   ├── plugins/          # 插件
│   │   └── darkModeDetector.ts # 深色模式检测插件
│   ├── styles/           # 全局样式
│   ├── utils/            # 工具函数
│   ├── views/            # 页面视图
│   │   ├── AccountDetailView.vue  # 账户详情页面
│   │   ├── AccountsView.vue      # 账户管理页面
│   │   ├── BudgetView.vue        # 预算管理页面
│   │   ├── DashboardView.vue     # 仪表盘页面
│   │   ├── LoginView.vue         # 登录页面
│   │   ├── TransactionsView.vue  # 交易记录页面
│   │   └── ... 其他页面
│   ├── App.vue           # 根组件
│   ├── main.ts           # 入口文件
│   └── env.d.ts          # 环境声明
├── e2e/                  # E2E测试
├── .vscode/              # VS Code配置
├── index.html            # HTML模板
├── package.json          # 项目依赖配置
├── tsconfig.json         # TypeScript配置
├── vite.config.ts        # Vite配置
└── ... 其他配置文件
```

## 功能模块

### 1. 用户认证

- 登录功能
- 注册功能
- 用户信息获取与更新
- 登出功能
- 账号删除功能

### 2. 账户管理

- 账户列表展示
- 添加新账户
- 编辑账户信息
- 删除账户
- 账户余额显示
- 账户类型支持：银行账户、基金账户、股票账户、负债账户

### 3. 交易记录

- 交易列表展示与筛选
- 添加新交易（收入、支出、转账）
- 编辑交易信息
- 删除交易
- 交易分类管理
- 转账功能支持：
  - 账户间转账
  - 支持资产和负债账户间转账（还款/借款）
  - 正确显示源账户和目标账户信息
  - 负债账户转账逻辑：转账到负债账户相当于还款，减少负债余额

### 4. 账户详情

- 显示特定账户的基本信息
- 仅显示该账户的交易记录
- 支持在账户详情页添加交易

### 5. 预算管理

- 按分类设置月度预算
- 显示预算使用情况和剩余金额
- 预算进度条可视化展示
- 支持添加、修改和删除预算
- 自动更新预算已用金额（基于交易记录）
- 总体预算使用情况汇总

### 6. 财务分析 (仪表盘)

- 收支统计
- 资产变化趋势图表
- 资产分布饼图
  - 包括所有账户类型（含负债账户）
  - 负债账户用特殊颜色标识
- 近期交易记录显示

### 7. 数据管理

- 数据备份功能
  - 服务器端JSON格式备份
  - 显示上次备份时间
- 数据导出功能
  - 将用户交易数据导出为Excel文件
  - 格式化的交易流水报表
- 数据恢复功能
  - 从JSON备份文件恢复数据
  - 支持文件选择和上传
  - 自动恢复最新备份
- 数据清空功能
  - 清空所有财务数据但保留用户账号
  - 二次确认机制防止误操作
- 账号删除功能
  - 永久删除用户账号和所有数据
  - 二次确认和输入"DELETE"的安全机制

## UI设计与布局

### 1. 整体布局

系统采用经典的左侧导航+右侧内容的主布局结构：

- **左侧导航栏**：提供主要功能模块的快速访问
- **顶部标题栏**：显示当前页面标题和用户信息
- **主内容区**：展示各功能模块的具体内容
- **深色模式**：支持自动检测系统主题并切换深色/浅色模式

### 2. 导航栏设计

导航栏采用现代美观的设计风格，具有以下特点：

- **宽度适中**：默认宽度为240px，确保足够的空间显示菜单项
- **醒目的Logo**：顶部显示应用名称与货币符号标识，增强品牌识别
- **清晰的菜单项**：增大菜单项高度(56px)和字体大小(16px)，提高可读性
- **视觉引导**：当前选中的菜单项通过左侧蓝色边条和背景色突显
- **响应式设计**：
  - 可折叠设计，折叠时宽度为64px，仅显示图标
  - 在移动设备上自动隐藏，通过左上角汉堡菜单按钮显示
  - 移动端展开时宽度为260px，更适合触摸操作

### 3. 交易记录页面

交易记录页面采用清晰的三区域设计：

- **筛选区域**：提供日期范围、交易类型、账户和关键词搜索功能
- **交易列表**：表格式展示交易记录详情
- **移动端适配**：在移动设备上使用卡片式布局替代表格

### 4. 仪表盘设计

仪表盘页面采用卡片式布局，包含：

- **资产概览卡片**：显示总资产、总负债和净资产
- **趋势图卡片**：资产变化趋势折线图
- **分布图卡片**：资产分布饼图
- **近期交易卡片**：列表式展示最近交易记录

## API集成

前端通过Axios与后端API进行通信。API配置文件位于 `src/api/config.ts`，默认使用 `/api/v1` 作为基础URL。

```typescript
// 示例: API配置
const http = axios.create({
  baseURL: '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});
```

主要API接口包括：

### 账户相关API
```typescript
// 获取账户列表
export function getAccounts(): Promise<Account[]> {
  return get('/accounts');
}

// 获取账户详情
export function getAccount(id: number): Promise<Account> {
  return get(`/accounts/${id}`);
}
```

### 交易相关API
```typescript
// 获取交易列表
export function getTransactions(params?: any): Promise<Transaction[]> {
  return get('/transactions', params);
}

// 获取指定账户的交易
export function getAccountTransactions(accountId: number): Promise<Transaction[]> {
  return get(`/transactions/by-account/${accountId}`);
}

// 创建交易
export function createTransaction(data: TransactionCreateParams): Promise<Transaction> {
  return post('/transactions', data);
}
```

### 预算相关API
```typescript
// 获取月度预算及汇总
export function getMonthlyBudgets(year?: number, month?: number): Promise<MonthlyBudgetResponse> {
  return get('/budgets/', { year, month });
}

// 创建预算
export function createBudget(data: BudgetCreateParams): Promise<Budget> {
  return post('/budgets/', data);
}

// 更新预算
export function updateBudget(id: number, amount: number): Promise<Budget> {
  return put(`/budgets/${id}`, { amount });
}

// 删除预算
export function deleteBudget(id: number): Promise<void> {
  return del(`/budgets/${id}`);
}
```

### 分类相关API
```typescript
// 获取所有分类
export function getCategories(): Promise<Category[]> {
  return get('/categories/');
}

// 获取支出分类
export function getExpenseCategories(): Promise<Category[]> {
  return get('/categories/', { type: 'expense' });
}
```

### 用户相关API
```typescript
// 获取用户信息
export function getUserInfo(): Promise<UserInfo> {
  return get('/users/me');
}

// 获取用户设置
export function getUserSettings(): Promise<UserSettings> {
  return get('/users/settings');
}

// 数据备份
export function backupUserData(): Promise<UserSettings> {
  return post('/users/backup');
}

// 获取用户备份列表
export function getUserBackups(): Promise<{
  code: number;
  message: string;
  data: Array<{
    filename: string;
    path: string;
    created_at: string;
    size: number;
    size_display: string;
    timestamp: number;
  }>
}> {
  return get('/users/backups');
}

// 导出用户数据
export function exportUserData(): Promise<Blob> {
  return axios.get('/api/v1/users/export', {
    responseType: 'blob'
  });
}

// 恢复用户数据(从上传文件)
export function restoreUserData(file: File): Promise<any> {
  const formData = new FormData();
  formData.append('file', file);
  return post('/users/restore', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  });
}

// 自动恢复最新备份
export function restoreLatestBackup(): Promise<any> {
  return post('/users/restore-latest');
}

// 清空所有用户数据
export function clearAllUserData(): Promise<BaseResponse<null>> {
  return axios.delete('/users/clear-data');
}

// 删除用户账号
export function deleteUserAccount(): Promise<BaseResponse<null>> {
  return axios.delete('/users/account');
}
```

### 仪表盘相关API
```typescript
// 获取仪表盘数据
export function getDashboardData(): Promise<DashboardData> {
  return get('/analytics/dashboard');
}
```

开发环境下，通过Vite的代理配置将API请求转发到后端服务：

```typescript
// vite.config.ts
export default defineConfig({
  // ...
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  }
});
```

## 组件功能与实现

### 1. 主应用布局 (AppLayout.vue)

AppLayout组件实现了整个应用的主布局结构，包括响应式导航和深色模式支持。该组件负责:
- 检测设备类型并调整布局
- 管理侧边栏的折叠状态
- 在移动端提供抽屉式导航
- 监听系统主题变化并自动切换深色模式

### 2. 交易管理组件

TransactionsView.vue 组件是交易管理的核心，它实现:
- 多条件组合筛选交易记录
- 正确显示转账的源账户和目标账户
- 根据设备类型自适应表格或卡片布局
- 集成事务表单组件

### 3. 预算管理组件

BudgetView.vue 组件实现了预算管理功能，特点包括:
- 按月份和年份查询预算信息
- 动态计算和显示预算使用进度
- 根据使用比例显示不同颜色的进度条
- 支持添加、修改和删除预算

### 4. 账户管理组件

AccountsView.vue 组件负责账户管理，包括:
- 显示不同类型账户的余额
- 正确处理负债账户的余额显示（数据库存储正数，前端显示负数）
- 提供账户创建和编辑功能
- 账户卡片式布局，直观展示账户信息
- 负债账户余额颜色标识：负数显示为健康状态（绿色），正数显示为警告状态（红色）

### 5. 仪表盘组件

DashboardView.vue 使用ECharts实现数据可视化:
- 资产趋势图展示资产变化
- 资产分布饼图显示各账户占比
- 自动适应深色模式的图表样式
- 响应式调整图表大小

### 6. 负债账户专用组件

DebtTransactionForm.vue 专门处理负债账户交易:
- 提供还款和借款两种操作模式
- 自动处理负债账户的特殊逻辑
- 集成到交易记录页面和账户详情页面
- 确保负债账户交易的正确性

### 7. 数据管理组件

DataManagementView.vue 提供数据管理功能:
- 显示上次备份时间
- 提供备份、恢复和导出操作
- 实现自动恢复最新备份功能
- 包含安全确认机制

## 状态管理

项目使用Pinia进行状态管理，主要store包括:

- `userStore`: 管理用户信息和认证状态
- `accountStore`: 管理账户信息和余额计算
- `transactionStore`: 管理交易记录和筛选
- `categoryStore`: 管理交易分类
- `budgetStore`: 管理预算信息和使用状态

## 最佳实践与注意事项

1. **负债账户处理**：
   - 负债账户在数据库中存储为正数（表示欠款金额），但在前端显示为负数
   - 转账到负债账户相当于还款，会减少负债余额
   - 负债账户支出相当于借款，会增加负债余额
   - 负债账户收入相当于还款，会减少负债余额
   - 前端需要正确处理负债账户的余额显示和颜色标识

2. **预算管理注意事项**：
   - 每个分类每月只能创建一个预算
   - 添加交易后，预算的已用金额会自动更新
   - 预算进度条会根据使用比例变色：绿色(<80%)、黄色(80-99%)、红色(>=100%)

3. **转账记录展示**：
   - 确保正确显示源账户和目标账户的名称
   - 处理可能的空值或未知账户情况

4. **图表渲染**：
   - 确保DOM元素已经加载完成再初始化图表
   - 监听窗口大小变化，自适应调整图表大小
   - 对无数据情况进行处理
   - 支持深色模式自动切换图表主题

5. **数据刷新**：
   - 交易操作后自动刷新相关数据
   - 使用适当的加载状态指示器

6. **数据管理安全**：
   - 数据清空和账号删除操作使用多重确认机制
   - 账号删除操作要求用户输入特定文本("DELETE")作为确认
   - 操作完成后提供清晰的反馈和适当的页面跳转/刷新

7. **错误处理**：
   - 所有异步操作使用try-catch块处理可能的错误
   - 提供友好的错误消息，显示详细错误原因
   - 操作失败后恢复UI状态

8. **移动端适配**：
   - 导航栏在移动端自动隐藏，通过汉堡菜单按钮控制显示
   - 表格在移动端转换为卡片式布局，更适合触摸操作
   - 表单在移动端优化布局和输入控件尺寸
   - 动态检测设备类型并调整组件布局

## 开发指南

### 环境要求

- Node.js 14+
- npm 6+ 或 yarn 1.22+

### 安装依赖

```bash
cd ledger-frontend
npm install
# 或
yarn
```

### 开发环境启动

```bash
npm run dev
# 或
yarn dev
```

开发服务器将在 http://localhost:5173 启动。

### 构建生产版本

```bash
npm run build
# 或
yarn build
```

构建文件将输出到 `dist` 目录。

### 运行测试

```bash
# 单元测试
npm run test:unit
# 或
yarn test:unit

# E2E测试
npm run test:e2e
# 或
yarn test:e2e
```

## 部署建议

1. 使用Nginx作为静态文件服务器
2. 配置适当的缓存策略
3. 启用GZIP压缩
4. 配置API反向代理

## 生产环境部署

### 前端打包

在开发完成后，需要将前端代码打包为静态文件以便部署到服务器：

```bash
# 进入前端项目目录
cd ledger-frontend

# 安装依赖
npm install

# 构建生产版本
npm run build
```

构建完成后，会在项目根目录下生成 `dist` 目录，包含所有静态资源文件。

### 部署到服务器

#### Docker部署 (推荐)

系统提供了Docker部署脚本，可以快速部署前端服务:

```bash
# 在项目根目录执行
./deploy-frontend-serve-docker.sh
```

该脚本会构建Docker镜像并启动一个容器服务前端应用。

#### 手动部署

##### 1. 上传dist目录

将构建好的 `dist` 目录上传到服务器：

```bash
# 使用scp命令上传(本地执行)
scp -r ./dist user@your-server-ip:/path/to/upload/

# 或使用rsync命令(本地执行)
rsync -avz --delete ./dist/ user@your-server-ip:/path/to/upload/
```

##### 2. 配置Web服务器

**Nginx配置示例:**

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    root /path/to/dist;
    index index.html;
    
    # 启用gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # 缓存静态资源
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, max-age=31536000";
    }
    
    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 前后端分离部署注意事项

1. **API地址配置**：确保前端构建时配置了正确的后端API地址
2. **CORS配置**：确保后端允许前端域名的跨域请求
3. **环境变量**：根据不同环境(开发/测试/生产)设置不同的API地址

### 生产环境优化建议

1. **使用CDN**：将静态资源部署到CDN以提高加载速度
2. **启用HTTPS**：使用SSL证书确保数据传输安全
3. **配置缓存策略**：为静态资源设置适当的缓存头
4. **压缩静态资源**：启用gzip/brotli压缩减小传输体积
5. **监控服务状态**：使用监控工具确保服务持续可用
6. **性能优化**：实施代码分割、懒加载和预加载策略

## 注意事项

- 在生产环境中，请确保禁用所有的模拟数据（mock data）
- 项目已配置ESLint和Prettier，请遵循代码规范
- 提交代码前运行测试确保功能正常
- 移动端导航栏需要特别注意用户体验，确保展开/折叠操作流畅
- 确保所有敏感操作都有适当的确认机制
