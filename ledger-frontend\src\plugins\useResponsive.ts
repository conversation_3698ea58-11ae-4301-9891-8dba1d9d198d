import { ref, onMounted, onBeforeUnmount } from 'vue'

export function useResponsive() {
  const windowWidth = ref(window.innerWidth)
  const windowHeight = ref(window.innerHeight)
  
  const isMobile = ref(false)
  const isTablet = ref(false)
  const isDesktop = ref(false)
  
  const checkDeviceType = () => {
    windowWidth.value = window.innerWidth
    windowHeight.value = window.innerHeight
    
    // 移动设备判断：小于768px
    isMobile.value = windowWidth.value < 768
    // 平板设备判断：768px - 992px
    isTablet.value = windowWidth.value >= 768 && windowWidth.value < 992
    // 桌面设备判断：大于等于992px
    isDesktop.value = windowWidth.value >= 992
  }
  
  // 监听窗口大小变化
  const handleResize = () => {
    checkDeviceType()
  }
  
  // 组件挂载时添加事件监听
  onMounted(() => {
    checkDeviceType()
    window.addEventListener('resize', handleResize)
  })
  
  // 组件卸载前移除事件监听
  onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize)
  })
  
  // 返回响应式设备信息
  return {
    windowWidth,
    windowHeight,
    isMobile,
    isTablet, 
    isDesktop
  }
}

// 输出常用的断点值，可以在组件中直接引用
export const breakpoints = {
  xs: 480,
  sm: 768,
  md: 992,
  lg: 1200
} 