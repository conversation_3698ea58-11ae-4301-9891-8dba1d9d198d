from typing import List
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import JSONResponse # 导入 JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.session import get_db
from app.modules.accounts.schemas import AccountCreate, Account, AccountUpdate
from app.modules.accounts.service import account_service
from app.core.security import get_current_user
from app.modules.users.schemas import User
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("", response_model=Account, status_code=201)
async def create_account(
    *,
    db: AsyncSession = Depends(get_db),
    account_in: AccountCreate,
    current_user: User = Depends(get_current_user)
):
    """
    Create a new account.
    """
    account = await account_service.create(db=db, obj_in=account_in, user_id=current_user.id)
    return account

@router.get("") # 移除 response_model
async def read_accounts(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    skip: int = 0,
    limit: int = 100
):
    """
    Retrieve accounts for current user. Manually serialize to avoid Pydantic issues.
    """
    logger.info("[read_accounts] 开始获取并手动序列化账户...")
    accounts_from_db = await account_service.get_accounts_by_user(db, user_id=current_user.id)
    
    # 手动将 SQLAlchemy 模型转换为字典列表
    accounts_list = []
    for account in accounts_from_db:
        accounts_list.append({
            "id": account.id,
            "user_id": account.user_id,
            "account_name": account.account_name,
            "account_type": str(account.account_type), # 确保类型是字符串
            "institution": account.institution,
            "account_number": account.account_number,
            "currency": account.currency,
            "initial_balance": float(account.initial_balance),
            "current_balance": float(account.current_balance),
            "is_active": account.is_active,
            "created_at": account.created_at.isoformat(),
            "updated_at": account.updated_at.isoformat()
        })
    
    logger.info(f"[read_accounts] 手动序列化完成，返回 {len(accounts_list)} 个账户。")
    return JSONResponse(content=accounts_list)

@router.get("/{account_id}", response_model=Account)
async def read_account(
    *,
    db: AsyncSession = Depends(get_db),
    account_id: int,
    current_user: User = Depends(get_current_user)
):
    """
    Get account by ID, ensuring it belongs to the current user.
    """
    account = await account_service.get_by_id(db, account_id=account_id, user_id=current_user.id)
    if account is None:
        raise HTTPException(status_code=404, detail="Account not found")
    return account

@router.put("/{account_id}", response_model=Account)
async def update_account(
    *,
    db: AsyncSession = Depends(get_db),
    account_id: int,
    account_in: AccountUpdate,
    current_user: User = Depends(get_current_user)
):
    """
    Update an account, ensuring it belongs to the current user.
    """
    account = await account_service.get_by_id(db, account_id=account_id, user_id=current_user.id)
    if account is None:
        raise HTTPException(status_code=404, detail="Account not found")
    
    updated_account = await account_service.update(db, db_obj=account, obj_in=account_in)
    return updated_account

@router.delete("/{account_id}", response_model=Account)
async def delete_account(
    *,
    db: AsyncSession = Depends(get_db),
    account_id: int,
    current_user: User = Depends(get_current_user)
):
    """
    Delete an account, ensuring it belongs to the current user.
    """
    account = await account_service.get_by_id(db, account_id=account_id, user_id=current_user.id)
    if account is None:
        raise HTTPException(status_code=404, detail="Account not found")
    
    deleted_account = await account_service.delete(db, db_obj=account)
    return deleted_account 