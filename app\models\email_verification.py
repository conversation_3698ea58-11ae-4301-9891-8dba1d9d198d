"""
邮箱验证码模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean
from sqlalchemy.sql import func
from app.db.base_class import Base


class EmailVerification(Base):
    """邮箱验证码表"""
    __tablename__ = "email_verifications"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(100), nullable=False, index=True, comment="邮箱地址")
    verification_code = Column(String(6), nullable=False, comment="验证码")
    verification_type = Column(String(20), nullable=False, default="register", comment="验证类型：register, reset_password")
    expires_at = Column(DateTime, nullable=False, comment="过期时间")
    used = Column(Boolean, nullable=False, default=False, comment="是否已使用")
    created_at = Column(DateTime, nullable=False, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now(), comment="更新时间")
