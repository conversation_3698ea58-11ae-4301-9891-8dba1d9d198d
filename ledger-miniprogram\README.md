# 金融账本系统 - 微信小程序端

## 项目概述

基于微信小程序原生开发的个人财务管理应用，与后端API完全对接，提供完整的记账功能。

## 开发进度

### ✅ 第一阶段：基础架构 + 用户认证（已完成）

- [x] 项目基础架构搭建
- [x] API适配器开发
- [x] 用户认证模块（登录/注册）
- [x] 基础页面结构
- [x] 设置模块

### 🚧 后续阶段

- [ ] 第二阶段：仪表盘 + 账户管理
- [ ] 第三阶段：交易记录管理
- [ ] 第四阶段：财务分析 + 预算管理
- [ ] 第五阶段：优化和发布

## 项目结构

```
ledger-miniprogram/
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式
├── pages/                # 页面目录
│   ├── index/           # 首页
│   ├── login/           # 登录页面
│   ├── register/        # 注册页面
│   ├── settings/        # 设置页面
│   ├── accounts/        # 账户管理（待开发）
│   ├── transactions/    # 交易记录（待开发）
│   └── analytics/       # 数据分析（待开发）
├── utils/               # 工具类
│   ├── api.js          # API接口封装
│   └── common.js       # 通用工具函数
└── project.config.json  # 项目配置
```

## 核心功能

### 已实现功能

1. **用户认证**
   - 用户登录/注册
   - 邮箱验证码功能
   - Token管理
   - 自动登录

2. **基础架构**
   - API请求封装
   - 错误处理
   - 数据存储
   - 页面导航

### 待开发功能

1. **账户管理**
   - 账户列表
   - 添加/编辑账户
   - 账户余额显示

2. **交易记录**
   - 收支记录
   - 分类管理
   - 交易搜索

3. **数据分析**
   - 收支统计
   - 图表展示
   - 预算管理

## 开发环境

### 要求

- 微信开发者工具
- Node.js 14+
- 后端API服务

### 配置

1. **微信开发者工具设置**
   - 勾选"不校验合法域名"
   - 启用调试模式

2. **API配置**
   - 默认地址：`http://127.0.0.1:8000/api/v1`
   - 可在 `utils/api.js` 中修改

### 启动步骤

1. 确保后端服务已启动
2. 用微信开发者工具打开项目
3. 编译并预览

## API接口

### 用户认证

- `POST /users/login/access-token` - 用户登录
- `POST /users/register` - 用户注册
- `POST /users/send-register-verification` - 发送注册验证码
- `GET /users/me` - 获取用户信息

### 测试账号

- 用户名：`test`
- 密码：`test123`

## 注意事项

1. **网络配置**
   - 确保微信开发者工具允许HTTP请求
   - 检查防火墙设置

2. **开发规范**
   - 使用ES6+语法
   - 统一错误处理
   - 详细的控制台日志

3. **代码结构**
   - 页面逻辑分离
   - API统一管理
   - 样式模块化

## 下一步开发计划

1. **账户管理模块**
   - 复用后端账户API
   - 实现账户CRUD操作
   - 添加账户余额显示

2. **交易记录模块**
   - 基于现有IOSTransactionModal组件
   - 实现交易记录管理
   - 添加分类功能

3. **数据分析模块**
   - 收支统计图表
   - 预算管理功能
   - 财务报告

## 技术栈

- **前端框架**：微信小程序原生
- **网络请求**：wx.request
- **数据存储**：wx.storage
- **UI组件**：原生组件 + 自定义组件
- **后端API**：FastAPI + MySQL

## 联系方式

如有问题请查看项目文档或联系开发团队。
