import { get, post, put, del } from './config';
import type { BaseResponse } from './types';

// 分类接口定义
export interface Category {
  id: number;
  name: string;
  type: 'income' | 'expense';
  icon: string;
  color: string;
  user_id: number | null;
}

// 获取所有分类
export function getCategories(): Promise<Category[]> {
  return get<Category[]>('/categories/').then(response => response).catch(error => {
    console.error('获取所有分类失败:', error);
    return [];
  });
}

// 获取支出分类
export function getExpenseCategories(): Promise<Category[]> {
  return get<Category[]>('/categories/', { type: 'expense' }).then(response => response).catch(error => {
    console.error('获取支出分类失败:', error);
    return [];
  });
}

// 获取收入分类
export function getIncomeCategories(): Promise<Category[]> {
  return get<Category[]>('/categories/', { type: 'income' }).then(response => response).catch(error => {
    console.error('获取收入分类失败:', error);
    return [];
  });
}

// 获取单个分类
export function getCategoryById(id: number): Promise<Category | null> {
  return get<Category>(`/categories/${id}`).then(response => response).catch(error => {
    console.error(`获取分类${id}失败:`, error);
    return null;
  });
}

// 创建分类
export function createCategory(data: { 
  name: string; 
  type: 'income' | 'expense'; 
  icon: string;
}): Promise<BaseResponse<any>> {
  return post<any>('/categories/', data).then(response => {
    return response || { code: 200, message: '创建分类成功', data: null };
  }).catch(error => {
    console.error('创建分类失败:', error);
    return { 
      code: error.response?.status || 500, 
      message: error.response?.data?.message || '创建分类失败', 
      data: null 
    };
  });
}

// 更新分类
export function updateCategory(data: Category): Promise<BaseResponse<any>> {
  const params = new URLSearchParams();
  if (data.name) params.append('name', data.name);
  if (data.color) params.append('color', data.color);
  if (data.icon) params.append('icon', data.icon);
  
  const url = `/categories/${data.id}?${params.toString()}`;
  return put<any>(url, {}).then(response => {
    // 后端直接返回分类数据，需要包装成 BaseResponse 格式
    if (response && (response.id || response.name)) {
      return { code: 200, message: '更新分类成功', data: response };
    }
    return response || { code: 200, message: '更新分类成功', data: null };
  }).catch(error => {
    console.error(`更新分类${data.id}失败:`, error);
    return { 
      code: error.response?.status || 500, 
      message: error.response?.data?.message || '更新分类失败', 
      data: null 
    };
  });
}

// 删除分类
export function deleteCategory(id: number): Promise<BaseResponse<any>> {
  return del<any>(`/categories/${id}`).then(response => {
    return response || { code: 200, message: '删除分类成功', data: null };
  }).catch(error => {
    console.error(`删除分类${id}失败:`, error);
    return { 
      code: error.response?.status || 500, 
      message: error.response?.data?.message || '删除分类失败', 
      data: null 
    };
  });
} 