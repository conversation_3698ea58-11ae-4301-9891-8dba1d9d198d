<!-- iOS风格表单组件 -->
<view class="ios-form">
  <slot></slot>
</view>

<!-- iOS表单组 -->
<template name="ios-form-group">
  <view class="ios-form-group">
    <slot name="content"></slot>
  </view>
</template>

<!-- iOS表单行 -->
<template name="ios-form-row">
  <view class="ios-form-row" bindtap="{{tapHandler}}">
    <view class="ios-form-label">{{label}}</view>
    <view class="ios-form-value">
      <slot name="value"></slot>
    </view>
  </view>
</template>

<!-- iOS输入框 -->
<template name="ios-input">
  <view class="ios-form-group">
    <view class="ios-form-label">{{label}}</view>
    <view class="ios-input-container">
      <input 
        class="ios-input {{inputClass}}"
        placeholder="{{placeholder}}"
        value="{{value}}"
        type="{{type || 'text'}}"
        maxlength="{{maxlength || -1}}"
        bindinput="{{inputHandler}}"
        bindfocus="{{focusHandler}}"
        bindblur="{{blurHandler}}"
      />
      <text wx:if="{{suffix}}" class="ios-input-suffix">{{suffix}}</text>
    </view>
  </view>
</template>

<!-- iOS选择器行 -->
<template name="ios-picker-row">
  <view class="ios-form-group">
    <view class="ios-form-row" bindtap="{{tapHandler}}">
      <view class="ios-form-label">{{label}}</view>
      <view class="ios-form-value">
        <text class="ios-value-text {{value ? '' : 'placeholder'}}">
          {{displayText || placeholder}}
        </text>
        <text class="ios-chevron">></text>
      </view>
    </view>
  </view>
</template>

<!-- iOS开关行 -->
<template name="ios-switch-row">
  <view class="ios-form-group">
    <view class="ios-form-row">
      <view class="ios-form-label">{{label}}</view>
      <view class="ios-form-value">
        <switch 
          checked="{{checked}}" 
          bindchange="{{changeHandler}}"
          color="#007AFF"
        />
      </view>
    </view>
  </view>
</template>
