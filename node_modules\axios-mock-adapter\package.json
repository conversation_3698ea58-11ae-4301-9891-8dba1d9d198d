{"name": "axios-mock-adapter", "version": "2.1.0", "description": "Axios adapter that allows to easily mock requests", "main": "src/index.js", "scripts": {"clean": "node -e 'require(`fs`).rmSync(`./dist`, {recursive: true, force: true})'", "test": "mocha && npm run clean && npm run build:umd:min && npm run test:types", "test:coverage": "nyc --reporter=html --reporter=text-summary mocha", "test:types": "tsc --project types", "lint": "eslint src test", "build:umd": "webpack --mode development ./src/index.js --output-path ./dist", "build:umd:min": "webpack --mode production ./src/index.js --output-path ./dist", "prepublish": "npm run clean && npm run build:umd && npm run build:umd:min"}, "files": ["src", "dist", "types"], "types": "types", "repository": {"type": "git", "url": "git+https://github.com/ctimmerm/axios-mock-adapter.git"}, "keywords": ["axios", "test", "mock", "request", "stub", "adapter"], "author": "<PERSON> <co<PERSON><PERSON><PERSON>@gmail.com>", "license": "MIT", "bugs": {"url": "https://github.com/ctimmerm/axios-mock-adapter/issues"}, "homepage": "https://github.com/ctimmerm/axios-mock-adapter#readme", "peerDependencies": {"axios": ">= 0.17.0"}, "devDependencies": {"axios": "^1.7.4", "chai": "^4.3.6", "eslint": "^9.8.0", "mocha": "^10.7.0", "nyc": "^17.0.0", "typescript": "^5.5.4", "webpack": "^5.93.0", "webpack-cli": "^5.1.4"}, "dependencies": {"fast-deep-equal": "^3.1.3", "is-buffer": "^2.0.5"}}