// pages/budget/index.js
const api = require('../../utils/api.js')
const common = require('../../utils/common.js')

Page({
  data: {
    selectedMonth: '',
    selectedMonthText: '',
    budgets: [],
    categories: [],
    loading: false,

    // 预算总览数据
    totalBudget: '0.00',
    totalSpent: '0.00',
    remaining: 0,
    usagePercentage: 0,

    // 弹窗相关
    showModal: false,
    editingBudget: null,
    selectedCategory: null,
    selectedCategoryIndex: 0,
    budgetAmount: '',

    // 系统信息
    statusBarHeight: 0,
    navBarHeight: 0
  },

  onLoad() {
    console.log('预算管理页面加载')
    this.getSystemInfo()
    this.initMonth()
    this.loadCategories()
    this.loadBudgets()
  },

  // 获取系统信息
  getSystemInfo() {
    const systemInfo = common.getSystemInfo()
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      navBarHeight: systemInfo.navBarHeight
    })
  },

  onShow() {
    this.loadBudgets()
  },

  onPullDownRefresh() {
    this.loadBudgets()
  },

  // 返回上一页
  goBack() {
    console.log('点击返回按钮')

    // 先显示一个提示，确认点击事件被触发
    wx.showToast({
      title: '点击了返回按钮',
      icon: 'none',
      duration: 1000
    })

    // 延迟执行返回操作
    setTimeout(() => {
      wx.navigateBack({
        delta: 1,
        success: function() {
          console.log('返回成功')
        },
        fail: function(err) {
          console.error('返回失败:', err)
          // 如果返回失败，尝试跳转到首页
          wx.switchTab({
            url: '/pages/index/index'
          })
        }
      })
    }, 1000)
  },

  // 初始化月份
  initMonth() {
    const now = new Date()
    const year = now.getFullYear()
    const month = now.getMonth() + 1
    const monthStr = `${year}-${month.toString().padStart(2, '0')}`

    this.setData({
      selectedMonth: monthStr,
      selectedMonthText: `${year}年${month}月`
    })
  },

  // 月份选择
  onMonthChange(e) {
    const selectedMonth = e.detail.value
    const [year, month] = selectedMonth.split('-')

    this.setData({
      selectedMonth: selectedMonth,
      selectedMonthText: `${year}年${parseInt(month)}月`
    })

    this.loadBudgets()
  },

  // 加载分类数据
  async loadCategories() {
    try {
      const categories = await api.categories.getCategories('expense')
      this.setData({ categories })
    } catch (error) {
      console.error('加载分类失败:', error)
    }
  },

  // 加载预算数据
  async loadBudgets() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const [year, month] = this.data.selectedMonth.split('-')
      const response = await api.budgets.getMonthlyBudgets(parseInt(year), parseInt(month))

      if (response && response.budgets) {
        // 处理预算数据
        const budgets = response.budgets.map(budget => {
          const usagePercentage = budget.amount > 0 ? 
            Math.round((budget.spent / budget.amount) * 100) : 0

          return {
            id: budget.id,
            categoryId: budget.category_id,
            categoryName: budget.category?.name || '未分类',
            icon: this.getCategoryIcon(budget.category?.name || '未分类'),
            amount: budget.amount,
            spent: budget.spent,
            amountText: this.formatAmount(budget.amount),
            spentText: this.formatAmount(budget.spent),
            usagePercentage: usagePercentage
          }
        })

        // 处理总览数据
        const summary = response.summary || {}
        const remaining = (summary.total_budget || 0) - (summary.total_spent || 0)
        const usagePercentage = summary.total_budget > 0 ? 
          Math.round((summary.total_spent / summary.total_budget) * 100) : 0

        this.setData({
          budgets,
          totalBudget: this.formatAmount(summary.total_budget || 0),
          totalSpent: this.formatAmount(summary.total_spent || 0),
          remaining: remaining,
          usagePercentage: usagePercentage
        })
      }

    } catch (error) {
      console.error('加载预算数据失败:', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
      wx.stopPullDownRefresh()
    }
  },

  // 显示添加预算弹窗
  showAddBudget() {
    this.setData({
      showModal: true,
      editingBudget: null,
      selectedCategory: null,
      selectedCategoryIndex: 0,
      budgetAmount: ''
    })
  },

  // 编辑预算
  editBudget(e) {
    const budgetId = e.currentTarget.dataset.id
    const budget = this.data.budgets.find(b => b.id === budgetId)
    
    if (budget) {
      // 找到对应的分类索引
      const categoryIndex = this.data.categories.findIndex(c => c.id === budget.categoryId)
      
      this.setData({
        showModal: true,
        editingBudget: budget,
        selectedCategory: this.data.categories[categoryIndex] || null,
        selectedCategoryIndex: categoryIndex >= 0 ? categoryIndex : 0,
        budgetAmount: budget.amount.toString()
      })
    }
  },

  // 删除预算
  deleteBudget(e) {
    const budgetId = e.currentTarget.dataset.id
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个预算吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            await api.budgets.deleteBudget(budgetId)
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })
            this.loadBudgets()
          } catch (error) {
            console.error('删除预算失败:', error)
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 隐藏弹窗
  hideModal() {
    this.setData({ showModal: false })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  },

  // 分类选择
  onCategoryChange(e) {
    const index = e.detail.value
    const category = this.data.categories[index]
    
    this.setData({
      selectedCategoryIndex: index,
      selectedCategory: category
    })
  },

  // 金额输入
  onAmountInput(e) {
    this.setData({
      budgetAmount: e.detail.value
    })
  },

  // 保存预算
  async saveBudget() {
    const { selectedCategory, budgetAmount, editingBudget } = this.data
    
    if (!selectedCategory) {
      wx.showToast({
        title: '请选择分类',
        icon: 'none'
      })
      return
    }
    
    if (!budgetAmount || parseFloat(budgetAmount) <= 0) {
      wx.showToast({
        title: '请输入有效金额',
        icon: 'none'
      })
      return
    }

    try {
      const [year, month] = this.data.selectedMonth.split('-')
      
      const budgetData = {
        category_id: selectedCategory.id,
        amount: parseFloat(budgetAmount),
        year: parseInt(year),
        month: parseInt(month)
      }

      if (editingBudget) {
        // 编辑预算
        await api.budgets.updateBudget(editingBudget.id, budgetData)
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        })
      } else {
        // 新增预算
        await api.budgets.createBudget(budgetData)
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        })
      }

      this.hideModal()
      this.loadBudgets()

    } catch (error) {
      console.error('保存预算失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  },

  // 格式化金额
  formatAmount(amount) {
    return parseFloat(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  },

  // 获取分类图标
  getCategoryIcon(categoryName) {
    const iconMap = {
      '餐饮美食': '🍽️',
      '交通出行': '🚗',
      '购物消费': '🛍️',
      '生活服务': '🏠',
      '医疗健康': '🏥',
      '教育培训': '📚',
      '娱乐休闲': '🎮'
    }

    return iconMap[categoryName] || '💸'
  }
})
