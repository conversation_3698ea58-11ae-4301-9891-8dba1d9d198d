// components/ios-modal/ios-modal.js
Component({
  properties: {
    // 是否显示模态框
    visible: {
      type: Boolean,
      value: false
    },
    // 标题
    title: {
      type: String,
      value: ''
    },
    // 取消按钮文本
    cancelText: {
      type: String,
      value: '取消'
    },
    // 确认按钮文本
    confirmText: {
      type: String,
      value: '确定'
    },
    // 确认按钮是否禁用
    confirmDisabled: {
      type: Boolean,
      value: false
    },
    // 确认按钮是否显示加载状态
    confirmLoading: {
      type: Boolean,
      value: false
    },
    // 点击遮罩是否关闭
    closeOnOverlay: {
      type: Boolean,
      value: true
    }
  },

  methods: {
    // 处理遮罩点击
    handleOverlayClick() {
      if (this.properties.closeOnOverlay) {
        this.triggerEvent('cancel')
      }
    },

    // 阻止事件冒泡
    stopPropagation() {
      // 阻止点击模态框内容时关闭
    },

    // 处理取消
    handleCancel() {
      this.triggerEvent('cancel')
    },

    // 处理确认
    handleConfirm() {
      if (!this.properties.confirmDisabled) {
        this.triggerEvent('confirm')
      }
    }
  }
})
