<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElDatePicker, ElButton, ElInputNumber } from 'element-plus'
import axios from 'axios'
import { getAccounts } from '@/api/account'
import type { Account } from '@/api/types'

// 组件属性
interface Props {
  accountId?: number
}

const props = defineProps<Props>()

// 事件
const emit = defineEmits(['close', 'success'])

// 响应式状态
const formRef = ref()
const loading = ref(false)
const submitting = ref(false)
const accounts = ref<Account[]>([])

// 表单数据
const form = reactive({
  date: new Date(), // 使用当前日期时间作为默认值
  operation: 'payment',  // payment 或 borrow
  amount: 0,
  category: '还款',
  description: '',
  account_id: props.accountId || undefined as number | undefined,
})

// 表单验证规则
const rules = {
  operation: [{ required: true, message: '请选择操作类型', trigger: 'change' }],
  amount: [
    { required: true, message: '请输入金额', trigger: 'blur' },
    { type: 'number' as const, min: 0.01, message: '金额必须大于0', trigger: 'blur' }
  ],
  date: [{ required: true, message: '请选择日期', trigger: 'change' }],
  account_id: [{ required: true, message: '请选择账户', trigger: 'change' }],
}

// 操作类型选项
const operationTypes = [
  { value: 'payment', label: '还款（减少负债）' },
  { value: 'borrow', label: '借款（增加负债）' }
]

// 根据操作类型获取分类选项
const categoryOptions = computed(() => {
  if (form.operation === 'payment') {
    return ['还款', '利息还款', '手续费', '其他还款']
  } else {
    return ['借款', '信用卡消费', '其他借款']
  }
})

// 获取账户列表
const fetchAccounts = async () => {
  try {
    loading.value = true
    accounts.value = await getAccounts()
    // 只过滤显示负债类型的账户
    accounts.value = accounts.value.filter(acc => acc.account_type === 'debt')
  } catch (error) {
    console.error('获取账户列表失败', error)
    ElMessage.error('获取账户列表失败')
  } finally {
    loading.value = false
  }
}

// 过滤账户
const filterAccounts = () => {
  if (accounts.value && accounts.value.length > 0) {
    // 仅保留债务类账户
    accounts.value = accounts.value.filter(acc => acc.account_type === 'debt')
  }
}

// 直接使用原生axios调用API，绕过可能有问题的封装
const directApiCall = async (url: string, data: any) => {
  try {
    const token = localStorage.getItem('token')
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    }
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }
    
    const response = await axios.post(url, data, { 
      headers,
      baseURL: '/api/v1'
    })
    return response.data
  } catch (error) {
    console.error('API调用失败', error)
    throw error
  }
}

// 格式化日期为YYYY-MM-DD格式，保留时间部分
const formatDate = (date: Date | string): string => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  // 返回完整的日期时间格式
  return dateObj.toISOString().slice(0, 19).replace('T', ' '); // 格式如: 2025-07-22 11:22:33
}

// 提交表单 - 暴露给父组件调用
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid: boolean) => {
    if (!valid) return
    
    try {
      submitting.value = true
      
      // 准备数据
      const transactionType = form.operation === 'payment' ? 'expense' : 'income'
      
      // 确保使用完整的日期时间
      console.log('提交的日期:', form.date);
      
      const data = {
        account_id: form.account_id,
        date: form.date, // 直接使用日期时间字符串
        transaction_type: transactionType,
        type: transactionType,
        amount: Number(form.amount),
        category: form.category || (form.operation === 'payment' ? '还款' : '借款'),
        description: form.description
      }
      
      console.log('提交负债交易数据:', data);
      
      // 调用API
      await directApiCall('/transactions', data)
      
      // 成功处理
      ElMessage.success('负债账户交易记录添加成功')
      emit('success')
      
    } catch (error: any) {
      console.error('提交负债账户交易失败', error)
      
      // 显示友好的错误信息
      if (error.response) {
        ElMessage.error(`提交失败: ${error.response.data?.detail || '未知错误'}`)
      } else {
        ElMessage.error('提交失败，请检查网络连接')
      }
    } finally {
      submitting.value = false
    }
  })
}

// 重置表单
const resetForm = () => {
  form.date = new Date()
  form.operation = 'payment'
  form.amount = 0
  form.category = '还款'
  form.description = ''
}

// 关闭对话框
const handleClose = () => {
  resetForm()
  emit('close')
}

// 组件挂载时获取账户列表
onMounted(async () => {
  await fetchAccounts()
  filterAccounts()
})

// 使用父组件传递的账户ID（如果有）
watch(() => props.accountId, (newVal) => {
  if (newVal) {
    form.account_id = newVal
  }
})

// 暴露方法给父组件使用
defineExpose({
  submitForm
})
</script>

<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-position="top"
    v-loading="loading"
  >
    <el-form-item label="操作类型" prop="operation">
      <el-select v-model="form.operation" placeholder="请选择操作类型" style="width: 100%">
        <el-option label="借款" value="borrow" />
        <el-option label="还款" value="repay" />
      </el-select>
    </el-form-item>
    
    <el-form-item label="金额" prop="amount">
      <el-input-number v-model="form.amount" :min="0.01" :precision="2" style="width: 100%" />
    </el-form-item>
    
    <el-form-item label="交易日期" prop="date">
      <el-date-picker
        v-model="form.date"
        type="datetime"
        placeholder="选择日期和时间"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        style="width: 100%"
      />
    </el-form-item>
    
    <el-form-item label="账户" prop="account_id">
      <el-select v-model="form.account_id" placeholder="选择账户" style="width: 100%" :disabled="props.accountId !== undefined">
        <el-option
          v-for="account in accounts"
          :key="account.id"
          :label="account.account_name"
          :value="account.id"
        >
          <div class="account-option">
            <span>{{ account.account_name }}</span>
            <span class="balance">余额: {{ Number(account.current_balance).toFixed(2) }}</span>
          </div>
        </el-option>
      </el-select>
    </el-form-item>
  </el-form>
</template>

<style lang="scss" scoped>
.form-note {
  background-color: #f0f9eb;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
  color: #67c23a;
  display: flex;
  align-items: center;
  
  i {
    margin-right: 8px;
  }
}

.account-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .balance {
    color: var(--apple-gray);
    font-size: 0.9em;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-md);
  gap: var(--spacing-sm);
}
</style> 