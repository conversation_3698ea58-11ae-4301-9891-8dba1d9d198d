import { get, post, put, del } from './config'
import type { Account, AccountCreateParams, AccountUpdateParams } from './types'

// 获取所有账户列表
export function getAccounts(): Promise<Account[]> {
  return get('/accounts')
}

// 获取账户详情
export function getAccountById(id: number): Promise<Account> {
  return get(`/accounts/${id}`)
}

// 创建新账户
export function createAccount(data: AccountCreateParams): Promise<Account> {
  return post('/accounts', data)
}

// 更新账户
export function updateAccount(data: AccountUpdateParams): Promise<Account> {
  return put(`/accounts/${data.id}`, data)
}

// 删除账户
export function deleteAccount(id: number): Promise<Account> {
  return del(`/accounts/${id}`)
} 