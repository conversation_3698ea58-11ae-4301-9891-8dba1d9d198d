<!-- 添加交易页面 -->
<view class="add-transaction-page">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-left" bindtap="goBack">
      <text class="back-icon">‹</text>
      <text class="back-text">返回</text>
    </view>
    <text class="page-title">添加交易</text>
    <view class="header-right"></view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 表单内容 -->
  <view wx:else class="form-container">
    <!-- 交易类型 -->
    <view class="form-group">
      <view class="form-label">交易类型</view>
      <view class="form-picker" bindtap="showTypePicker">
        <text class="picker-text {{form.transaction_type ? '' : 'placeholder'}}">
          {{typeDisplay || '请选择交易类型'}}
        </text>
        <text class="picker-arrow">></text>
      </view>
    </view>

    <!-- 金额 -->
    <view class="form-group">
      <view class="form-label">金额</view>
      <view class="form-input-container">
        <input 
          class="form-input amount-input" 
          placeholder="0.00" 
          value="{{form.amount}}"
          bindinput="onAmountInput"
          type="digit"
        />
        <text class="currency-symbol">¥</text>
      </view>
    </view>

    <!-- 分类 (非转账时显示) -->
    <view wx:if="{{form.transaction_type !== 'transfer'}}" class="form-group">
      <view class="form-label">分类</view>
      <view class="form-picker" bindtap="showCategoryPicker">
        <text class="picker-text {{form.category_id ? '' : 'placeholder'}}">
          {{categoryDisplay || '请选择分类'}}
        </text>
        <text class="picker-arrow">></text>
      </view>
    </view>

    <!-- 账户 (非转账时显示) -->
    <view wx:if="{{form.transaction_type !== 'transfer'}}" class="form-group">
      <view class="form-label">账户</view>
      <view class="form-picker" bindtap="showAccountPicker">
        <text class="picker-text {{form.account_id ? '' : 'placeholder'}}">
          {{accountDisplay || '请选择账户'}}
        </text>
        <text class="picker-arrow">></text>
      </view>
    </view>

    <!-- 转出账户 (转账时显示) -->
    <view wx:if="{{form.transaction_type === 'transfer'}}" class="form-group">
      <view class="form-label">转出账户</view>
      <view class="form-picker" bindtap="showFromAccountPicker">
        <text class="picker-text {{form.from_account_id ? '' : 'placeholder'}}">
          {{fromAccountDisplay || '请选择转出账户'}}
        </text>
        <text class="picker-arrow">></text>
      </view>
    </view>

    <!-- 转入账户 (转账时显示) -->
    <view wx:if="{{form.transaction_type === 'transfer'}}" class="form-group">
      <view class="form-label">转入账户</view>
      <view class="form-picker" bindtap="showToAccountPicker">
        <text class="picker-text {{form.to_account_id ? '' : 'placeholder'}}">
          {{toAccountDisplay || '请选择转入账户'}}
        </text>
        <text class="picker-arrow">></text>
      </view>
    </view>

    <!-- 日期 -->
    <view class="form-group">
      <view class="form-label">日期</view>
      <picker 
        mode="date" 
        value="{{form.transaction_date}}" 
        bindchange="onDateChange"
      >
        <view class="form-picker">
          <text class="picker-text">{{form.transaction_date}}</text>
          <text class="picker-arrow">></text>
        </view>
      </picker>
    </view>

    <!-- 备注 -->
    <view class="form-group">
      <view class="form-label">备注</view>
      <view class="form-input-container">
        <input 
          class="form-input" 
          placeholder="请输入备注（可选）" 
          value="{{form.description}}"
          bindinput="onDescriptionInput"
          maxlength="100"
        />
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="form-footer">
    <button 
      class="btn-primary" 
      bindtap="saveTransaction"
      disabled="{{submitting || !canSubmit}}"
    >
      {{submitting ? '保存中...' : '保存交易'}}
    </button>
  </view>

  <!-- 交易类型选择器 -->
  <ios-picker
    visible="{{showTypePicker}}"
    title="选择交易类型"
    options="{{transactionTypes}}"
    selected-value="{{form.transaction_type}}"
    value-key="value"
    label-key="label"
    icon-key="icon"
    bind:select="selectType"
    bind:cancel="hideTypePicker"
    bind:confirm="hideTypePicker"
  />

  <!-- 分类选择器 -->
  <ios-picker
    visible="{{showCategoryPicker}}"
    title="选择分类"
    options="{{categoriesWithEmoji}}"
    selected-value="{{form.category_id}}"
    value-key="id"
    label-key="name"
    icon-key="emoji"
    bind:select="selectCategory"
    bind:cancel="hideCategoryPicker"
    bind:confirm="hideCategoryPicker"
  />

  <!-- 账户选择器 -->
  <ios-picker
    visible="{{showAccountPicker}}"
    title="选择账户"
    options="{{accounts}}"
    selected-value="{{form.account_id}}"
    value-key="id"
    label-key="account_name"
    bind:select="selectAccount"
    bind:cancel="hideAccountPicker"
    bind:confirm="hideAccountPicker"
  />

  <!-- 转出账户选择器 -->
  <ios-picker
    visible="{{showFromAccountPicker}}"
    title="选择转出账户"
    options="{{accounts}}"
    selected-value="{{form.from_account_id}}"
    value-key="id"
    label-key="account_name"
    bind:select="selectFromAccount"
    bind:cancel="hideFromAccountPicker"
    bind:confirm="hideFromAccountPicker"
  />

  <!-- 转入账户选择器 -->
  <ios-picker
    visible="{{showToAccountPicker}}"
    title="选择转入账户"
    options="{{accounts}}"
    selected-value="{{form.to_account_id}}"
    value-key="id"
    label-key="account_name"
    bind:select="selectToAccount"
    bind:cancel="hideToAccountPicker"
    bind:confirm="hideToAccountPicker"
  />
</view>
