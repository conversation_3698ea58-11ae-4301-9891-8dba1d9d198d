from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import List, Optional
import logging # 导入 logging 模块

from . import models, schemas

# 设置日志记录器
logger = logging.getLogger(__name__)

class AccountService:
    @staticmethod
    async def get_multi(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[models.Account]:
        result = await db.execute(
            select(models.Account)
            .filter(models.Account.deleted == False)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    @staticmethod
    async def get_accounts_by_user(db: AsyncSession, user_id: int) -> List[models.Account]:
        logger.info(f"正在为用户 {user_id} 查询所有账户...")
        result = await db.execute(
            select(models.Account)
            .filter(models.Account.user_id == user_id, models.Account.deleted == False)
        )
        accounts = result.scalars().all()
        
        logger.info(f"从数据库中为用户 {user_id} 找到了 {len(accounts)} 个账户。")
        for acc in accounts:
            logger.debug(
                f"  -> 账户ID: {acc.id}, 名称: {acc.account_name}, "
                f"类型: {acc.account_type}, 余额: {acc.current_balance}"
            )
            
        return accounts
        
    @staticmethod
    async def get_by_id(db: AsyncSession, account_id: int, user_id: int = None) -> Optional[models.Account]:
        """
        根据ID获取账户，可选验证用户ID
        """
        query = select(models.Account).filter(models.Account.id == account_id, models.Account.deleted == False)
        
        # 如果提供了用户ID，添加用户过滤条件
        if user_id is not None:
            query = query.filter(models.Account.user_id == user_id)
            
        result = await db.execute(query)
        return result.scalars().first()

    @staticmethod
    async def create(db: AsyncSession, obj_in: schemas.AccountCreate, user_id: int = 1) -> models.Account:
        # 注意：在实际应用中，user_id 应该从当前登录用户获取，这里暂时硬编码为1用于测试
        db_account = models.Account(
            **obj_in.dict(),
            user_id=user_id,
            current_balance=obj_in.initial_balance
        )
        db.add(db_account)
        await db.commit()
        await db.refresh(db_account)
        return db_account
        
    @staticmethod
    async def update(
        db: AsyncSession, 
        db_obj: models.Account, 
        obj_in: schemas.AccountUpdate
    ) -> models.Account:
        """
        更新账户信息
        """
        update_data = obj_in.dict(exclude_unset=True)
        
        # 遍历需要更新的字段
        for field, value in update_data.items():
            setattr(db_obj, field, value)
            
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
        
    @staticmethod
    async def delete(db: AsyncSession, db_obj: models.Account) -> models.Account:
        """
        软删除账户
        """
        db_obj.deleted = True
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

# Create a singleton instance of AccountService for direct import
account_service = AccountService() 