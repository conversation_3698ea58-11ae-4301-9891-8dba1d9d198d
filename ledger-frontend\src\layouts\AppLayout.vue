<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 侧边栏折叠状态
const isCollapse = ref(false)
// 移动设备检测
const isMobile = ref(false)

// 检测屏幕尺寸变化
const checkScreenSize = () => {
  isMobile.value = window.innerWidth <= 768
  // 在移动设备上默认折叠侧边栏
  if (isMobile.value) {
    isCollapse.value = true
  }
}

// 监听路由变化，在移动设备上点击菜单项后自动关闭侧边栏
watch(route, () => {
  if (isMobile.value) {
    isCollapse.value = true
  }
})

// 处理刷新账户事件
const handleRefreshAccounts = async () => {
  console.log('AppLayout: 收到刷新账户事件');
  try {
    // 导入账户API方法
    const { getAccounts } = await import('@/api/account');
    // 获取最新账户数据
    await getAccounts();
    console.log('AppLayout: 已获取最新账户数据');
  } catch (err) {
    console.error('获取最新账户数据失败', err);
  }
}

// 组件挂载时添加各种事件监听
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
  
  // 添加刷新账户事件监听
  window.addEventListener('refresh-accounts', handleRefreshAccounts)
})

// 组件卸载前移除所有监听器
onBeforeUnmount(() => {
  window.removeEventListener('resize', checkScreenSize)
  window.removeEventListener('refresh-accounts', handleRefreshAccounts)
})

const handleSelect = (key: string) => {
  router.push({ name: key })
}

// 处理个人中心点击
const handlePersonalCenter = () => {
  router.push({ name: 'settings' })
}

// 处理退出登录
const handleLogout = async () => {
  const success = await userStore.userLogout()
  if (success) {
    // 重定向到登录页
    router.push('/login')
  }
}

// 切换侧边栏
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}
</script>

<template>
  <div class="app-layout" :class="{ 'mobile': isMobile }">
    <!-- 移动端遮罩层，点击后关闭侧边栏 -->
    <div v-if="isMobile && !isCollapse" class="sidebar-overlay" @click="isCollapse = true"></div>
    
    <!-- 侧边栏导航 -->
    <el-menu
      class="sidebar"
      :collapse="isCollapse"
      :default-active="route.name as string"
      @select="handleSelect"
    >
      <div class="logo-container">
        <div class="logo-content" v-if="!isCollapse">
          <span class="logo-symbol">¥</span>
          <span class="logo">金融账本</span>
        </div>
        <span class="logo-icon" v-else>¥</span>
      </div>
      
      <el-menu-item index="home">
        <el-icon><i class="el-icon-menu"></i></el-icon>
        <template #title>仪表盘</template>
      </el-menu-item>
      
      <el-menu-item index="accounts">
        <el-icon><i class="el-icon-s-cooperation"></i></el-icon>
        <template #title>账户管理</template>
      </el-menu-item>
      
      <el-menu-item index="transactions">
        <el-icon><i class="el-icon-s-order"></i></el-icon>
        <template #title>交易记录</template>
      </el-menu-item>
      
      <el-menu-item index="analytics">
        <el-icon><i class="el-icon-s-data"></i></el-icon>
        <template #title>财务分析</template>
      </el-menu-item>
      
      <el-menu-item index="budget">
        <el-icon><i class="el-icon-s-finance"></i></el-icon>
        <template #title>预算管理</template>
      </el-menu-item>
      
      <el-menu-item index="settings">
        <el-icon><i class="el-icon-setting"></i></el-icon>
        <template #title>设置</template>
      </el-menu-item>
    </el-menu>
    
    <!-- 主要内容区域 -->
    <div class="main-container">
      <!-- 顶部导航栏 -->
      <div class="header">
        <!-- 汉堡菜单图标，仅在移动端显示 -->
        <div class="menu-button" v-if="isMobile" @click="toggleSidebar">
          <span></span>
          <span></span>
          <span></span>
        </div>
        
        <!-- 在非移动端显示折叠/展开按钮 -->
        <el-button type="text" @click="toggleSidebar" v-if="!isMobile">
          <el-icon><i class="el-icon-s-fold"></i></el-icon>
        </el-button>
        
        <div class="page-title" v-if="isMobile">
          {{ route.meta.title || '金融账本' }}
        </div>
        
        <div class="header-right">
          <el-dropdown>
            <el-avatar size="small" src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"></el-avatar>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handlePersonalCenter">个人中心</el-dropdown-item>
                <el-dropdown-item @click="handleLogout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <!-- 内容区域 -->
      <div class="content">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.app-layout {
  display: flex;
  height: 100vh;
  position: relative;
}

.sidebar {
  height: 100%;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border-right: none;
  transition: all 0.3s ease;
  z-index: 1000;
  width: 240px !important; /* 增加宽度，默认展开时 */
  background-color: #f8f9fa;
  
  &.el-menu--collapse {
    width: 64px !important; /* 折叠时的宽度 */
  }
  
  .logo-container {
    height: 80px; /* 增加高度 */
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #f0f0f0;
    padding: 0 20px;
  }
  
  .logo-content {
    display: flex;
    align-items: center;
    gap: 8px; /* 图标和文字之间的间距 */
  }

  .logo-symbol {
    font-size: 26px; /* 增大图标 */
    font-weight: bold;
    color: var(--apple-blue);
    width: 42px;
    height: 42px;
    border-radius: 12px;
    background-color: rgba(0, 122, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .logo {
    font-size: 22px; /* 增大字体 */
    font-weight: 600;
    color: var(--apple-blue);
  }
  
  .logo-icon {
    font-size: 26px; /* 增大图标 */
    font-weight: bold;
    color: var(--apple-blue);
    width: 42px;
    height: 42px;
    border-radius: 12px;
    background-color: rgba(0, 122, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  :deep(.el-menu-item) {
    height: 56px; /* 增加菜单项高度 */
    line-height: 56px;
    font-size: 16px; /* 增大字体 */
    padding: 0 20px !important;
    
    &.is-active {
      background-color: rgba(0, 122, 255, 0.1); /* 活跃菜单背景 */
      color: var(--apple-blue);
      font-weight: 600;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background-color: var(--apple-blue);
      }
    }
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.03);
    }
    
    i {
      font-size: 20px; /* 增大图标 */
      margin-right: 10px;
    }
  }
}

// 移动端遮罩层
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  height: 80px; /* 增加高度 */
  padding: 0 30px; /* 增加内边距 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

// 汉堡菜单按钮样式
.menu-button {
  width: 28px; /* 增大尺寸 */
  height: 28px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  cursor: pointer;
  padding: 5px 0;
  
  span {
    display: block;
    width: 100%;
    height: 3px;
    background-color: #333;
    border-radius: 3px;
    transition: all 0.3s ease;
  }
  
  &:hover span {
    background-color: var(--apple-blue);
  }
}

// 深色模式下的汉堡菜单
:global(.dark-mode) .menu-button span {
  background-color: #e2e2e6;
  
  &:hover {
    background-color: #60a5fa;
  }
}

.content {
  flex: 1;
  padding: 30px; /* 增加内边距 */
  overflow-y: auto;
  background-color: #f9f9fb;
}

.header-right {
  display: flex;
  align-items: center;
  
  .el-dropdown {
    margin-left: 20px;
    cursor: pointer;
    
    :deep(.el-avatar) {
      width: 40px; /* 增大头像 */
      height: 40px;
    }
  }
}

.page-title {
  font-weight: 500;
  font-size: 20px; /* 增大字体 */
  text-align: center;
  flex: 1;
}

// 移动端样式调整
.mobile {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 1001;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    
    &:not(.el-menu--collapse) {
      transform: translateX(0);
      width: 260px !important; /* 移动端展开时宽度更大 */
    }
  }
  
  .header {
    padding: 0 20px;
  }
  
  .content {
    padding: 20px;
  }
}

// 响应式媒体查询
@media (max-width: 768px) {
  .app-layout {
    flex-direction: column;
  }
  
  .content {
    padding: 16px;
  }
}

// 小屏设备额外调整
@media (max-width: 480px) {
  .content {
    padding: 12px;
  }
  
  .header {
    height: 60px;
  }
}

/* 深色模式样式覆盖 */
:global(.dark-mode) {
  .app-layout {
    background-color: #1e1e2e !important;
    color: #e2e2e6 !important;
  }
  
  .sidebar {
    background-color: #252636 !important; /* 稍微亮一点的背景 */
    border-color: #363646 !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    
    .logo-container {
      border-color: #363646 !important;
      background-color: #252636 !important;
    }
    
    .logo, .logo-icon {
      color: #60a5fa !important;
    }
    
    :deep(.el-menu-item) {
      &.is-active {
        background-color: rgba(96, 165, 250, 0.15) !important;
        color: #60a5fa !important;
        
        &::before {
          background-color: #60a5fa !important;
        }
      }
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.05) !important;
      }
    }
  }
  
  .main-container {
    background-color: #1e1e2e !important;
    color: #e2e2e6 !important;
  }
  
  .header {
    background-color: #282838 !important;
    border-color: #363646 !important;
    color: #e2e2e6 !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
  }
  
  .content {
    background-color: #1e1e2e !important;
    color: #e2e2e6 !important;
  }
  
  /* 确保白色区域被覆盖 */
  .el-main, .el-container {
    background-color: #1e1e2e !important;
    color: #e2e2e6 !important;
  }
  
  .sidebar-overlay {
    background-color: rgba(0, 0, 0, 0.7);
  }
}
</style> 