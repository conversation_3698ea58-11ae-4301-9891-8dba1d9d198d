/* components/ios-modal/ios-modal.wxss */

/* iOS风格模态框样式 */
.ios-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 40rpx;
  animation: fadeIn 0.3s ease;
}

.ios-modal {
  width: 100%;
  max-width: 750rpx;
  max-height: 85vh;
  background: #F2F2F7;
  border-radius: 40rpx;
  overflow: hidden;
  box-shadow: 0 40rpx 80rpx rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  animation: scaleIn 0.3s ease;
}

/* 模态框头部 */
.ios-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  background: #F8F9FA;
  border-bottom: 1rpx solid #E5E5E7;
}

.ios-header-btn {
  background: none;
  border: none;
  font-size: 32rpx;
  padding: 0;
  min-width: 80rpx;
}

.cancel-btn {
  color: #007AFF;
  text-align: left;
}

.confirm-btn {
  color: #007AFF;
  font-weight: 600;
  text-align: right;
}

.confirm-btn:disabled {
  color: #C7C7CC;
}

.ios-modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
  text-align: center;
  flex: 1;
}

/* 模态框内容 */
.ios-modal-content {
  flex: 1;
  overflow-y: auto;
  background: #F2F2F7;
}

/* 动画 */
@keyframes fadeIn {
  from { 
    opacity: 0; 
  }
  to { 
    opacity: 1; 
  }
}

@keyframes scaleIn {
  from { 
    opacity: 0;
    transform: scale(0.9);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}
