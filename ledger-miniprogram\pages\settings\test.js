// 设置页面功能测试脚本
// 这个文件用于在开发者工具的控制台中测试设置页面的功能

const SettingsTest = {
  // 测试用户信息验证
  testUserInfoValidation() {
    console.log('=== 测试用户信息验证 ===')
    
    const testCases = [
      { username: '', email: '<EMAIL>', expected: false, desc: '空用户名' },
      { username: 'ab', email: '<EMAIL>', expected: false, desc: '用户名太短' },
      { username: 'validuser', email: '', expected: false, desc: '空邮箱' },
      { username: 'validuser', email: 'invalid-email', expected: false, desc: '邮箱格式错误' },
      { username: 'validuser', email: '<EMAIL>', phone: '123', expected: false, desc: '手机号格式错误' },
      { username: 'validuser', email: '<EMAIL>', phone: '13812345678', expected: true, desc: '所有信息正确' },
    ]
    
    testCases.forEach(testCase => {
      // 这里需要在实际页面中调用验证函数
      console.log(`测试: ${testCase.desc} - 期望结果: ${testCase.expected}`)
    })
  },

  // 测试缓存大小计算
  testCacheSize() {
    console.log('=== 测试缓存大小计算 ===')
    
    try {
      const storageInfo = wx.getStorageInfoSync()
      console.log('存储信息:', storageInfo)
      
      const sizeKB = storageInfo.currentSize
      let displaySize
      
      if (sizeKB < 1024) {
        displaySize = `${sizeKB}KB`
      } else {
        displaySize = `${(sizeKB / 1024).toFixed(1)}MB`
      }
      
      console.log('计算的缓存大小:', displaySize)
    } catch (error) {
      console.error('计算缓存大小失败:', error)
    }
  },

  // 测试偏好设置映射
  testPreferencesMapping() {
    console.log('=== 测试偏好设置映射 ===')
    
    const localPreferences = {
      darkMode: true,
      currency: 'USD',
      dailyReminder: false,
      autoBackup: true
    }
    
    const serverSettings = {
      language: 'zh',
      currency: localPreferences.currency,
      dark_mode: localPreferences.darkMode,
      notifications: localPreferences.dailyReminder,
      auto_backup: localPreferences.autoBackup
    }
    
    console.log('本地偏好设置:', localPreferences)
    console.log('映射到服务器设置:', serverSettings)
    
    // 反向映射测试
    const mappedBack = {
      darkMode: serverSettings.dark_mode,
      currency: serverSettings.currency,
      dailyReminder: serverSettings.notifications,
      autoBackup: serverSettings.auto_backup
    }
    
    console.log('从服务器映射回本地:', mappedBack)
  },

  // 测试存储操作
  testStorageOperations() {
    console.log('=== 测试存储操作 ===')
    
    const testData = {
      testKey: 'testValue',
      testObject: { name: 'test', value: 123 }
    }
    
    try {
      // 存储测试
      wx.setStorageSync('test_data', JSON.stringify(testData))
      console.log('存储测试数据成功')
      
      // 读取测试
      const retrieved = wx.getStorageSync('test_data')
      const parsed = JSON.parse(retrieved)
      console.log('读取测试数据:', parsed)
      
      // 清除测试
      wx.removeStorageSync('test_data')
      console.log('清除测试数据成功')
      
    } catch (error) {
      console.error('存储操作测试失败:', error)
    }
  },

  // 运行所有测试
  runAllTests() {
    console.log('开始运行设置页面功能测试...')
    
    this.testUserInfoValidation()
    this.testCacheSize()
    this.testPreferencesMapping()
    this.testStorageOperations()
    
    console.log('所有测试完成！')
  }
}

// 导出测试对象，可在控制台中使用
// 使用方法：在开发者工具控制台中输入 SettingsTest.runAllTests()
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SettingsTest
} else if (typeof window !== 'undefined') {
  window.SettingsTest = SettingsTest
}

console.log('设置页面测试脚本已加载，使用 SettingsTest.runAllTests() 运行所有测试')
