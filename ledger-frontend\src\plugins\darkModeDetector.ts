/**
 * 深色模式检测与应用插件
 * 自动检测深色模式并应用到应用程序的各个部分
 */

import * as echarts from 'echarts'

// 深色模式下的ECharts全局主题
const darkTheme = {
  backgroundColor: '#282838',
  textStyle: {
    color: '#e2e2e6'
  },
  title: {
    textStyle: {
      color: '#e2e2e6'
    },
    subtextStyle: {
      color: '#aaa'
    }
  },
  line: {
    itemStyle: {
      borderWidth: 1
    },
    lineStyle: {
      width: 2
    },
    symbolSize: 4,
    symbol: 'circle',
    smooth: false
  },
  radar: {
    itemStyle: {
      borderWidth: 1
    },
    lineStyle: {
      width: 2
    },
    symbolSize: 4,
    symbol: 'circle',
    smooth: false
  },
  bar: {
    itemStyle: {
      barBorderWidth: 0,
      barBorderColor: '#ccc'
    }
  },
  pie: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    }
  },
  scatter: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    }
  },
  boxplot: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    }
  },
  parallel: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    }
  },
  sankey: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    }
  },
  funnel: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    }
  },
  gauge: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    }
  },
  candlestick: {
    itemStyle: {
      color: '#fd1050',
      color0: '#0cf49b',
      borderColor: '#fd1050',
      borderColor0: '#0cf49b',
      borderWidth: 1
    }
  },
  graph: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    },
    lineStyle: {
      width: 1,
      color: '#aaa'
    },
    symbolSize: 4,
    symbol: 'circle',
    smooth: false,
    color: [
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
      '#ea7ccc'
    ],
    label: {
      color: '#e2e2e6'
    }
  },
  map: {
    itemStyle: {
      areaColor: '#eee',
      borderColor: '#444',
      borderWidth: 0.5
    },
    label: {
      color: '#000'
    },
    emphasis: {
      itemStyle: {
        areaColor: 'rgba(255,215,0,0.8)',
        borderColor: '#444',
        borderWidth: 1
      },
      label: {
        color: '#e2e2e6'
      }
    }
  },
  geo: {
    itemStyle: {
      areaColor: '#eee',
      borderColor: '#444',
      borderWidth: 0.5
    },
    label: {
      color: '#000'
    },
    emphasis: {
      itemStyle: {
        areaColor: 'rgba(255,215,0,0.8)',
        borderColor: '#444',
        borderWidth: 1
      },
      label: {
        color: '#e2e2e6'
      }
    }
  },
  categoryAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: '#363646'
      }
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: '#363646'
      }
    },
    axisLabel: {
      show: true,
      color: '#e2e2e6'
    },
    splitLine: {
      show: false,
      lineStyle: {
        color: [
          '#363646'
        ]
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: [
          'rgba(250,250,250,0.05)',
          'rgba(200,200,200,0.02)'
        ]
      }
    }
  },
  valueAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: '#363646'
      }
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: '#363646'
      }
    },
    axisLabel: {
      show: true,
      color: '#e2e2e6'
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: [
          '#363646'
        ]
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: [
          'rgba(250,250,250,0.05)',
          'rgba(200,200,200,0.02)'
        ]
      }
    }
  },
  logAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: '#363646'
      }
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: '#363646'
      }
    },
    axisLabel: {
      show: true,
      color: '#e2e2e6'
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: [
          '#363646'
        ]
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: [
          'rgba(250,250,250,0.05)',
          'rgba(200,200,200,0.02)'
        ]
      }
    }
  },
  timeAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: '#363646'
      }
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: '#363646'
      }
    },
    axisLabel: {
      show: true,
      color: '#e2e2e6'
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: [
          '#363646'
        ]
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: [
          'rgba(250,250,250,0.05)',
          'rgba(200,200,200,0.02)'
        ]
      }
    }
  },
  toolbox: {
    iconStyle: {
      borderColor: '#999'
    },
    emphasis: {
      iconStyle: {
        borderColor: '#666'
      }
    }
  },
  legend: {
    textStyle: {
      color: '#e2e2e6'
    }
  },
  tooltip: {
    axisPointer: {
      lineStyle: {
        color: '#ccc',
        width: 1
      },
      crossStyle: {
        color: '#ccc',
        width: 1
      }
    }
  },
  timeline: {
    lineStyle: {
      color: '#893448',
      width: 1
    },
    itemStyle: {
      color: '#893448',
      borderWidth: 1
    },
    controlStyle: {
      color: '#893448',
      borderColor: '#893448',
      borderWidth: 0.5
    },
    checkpointStyle: {
      color: '#eb8146',
      borderColor: '#f9b248'
    },
    label: {
      color: '#e2e2e6'
    },
    emphasis: {
      itemStyle: {
        color: '#ffb248'
      },
      controlStyle: {
        color: '#e2e2e6',
        borderColor: '#e2e2e6',
        borderWidth: 0.5
      },
      label: {
        color: '#e2e2e6'
      }
    }
  },
  visualMap: {
    color: [
      '#bf444c',
      '#d88273',
      '#f6efa6'
    ]
  },
  dataZoom: {
    backgroundColor: 'rgba(47,69,84,0)',
    dataBackgroundColor: 'rgba(47,69,84,0.3)',
    fillerColor: 'rgba(167,183,204,0.4)',
    handleColor: '#a7b7cc',
    handleSize: '100%',
    textStyle: {
      color: '#333'
    }
  },
  markPoint: {
    label: {
      color: '#e2e2e6'
    },
    emphasis: {
      label: {
        color: '#e2e2e6'
      }
    }
  }
}

// 注册深色模式主题
echarts.registerTheme('darkMode', darkTheme)

/**
 * 深色模式检测与应用类
 */
class DarkModeDetector {
  private initialized = false

  /**
   * 检查当前是否处于深色模式
   */
  public isDarkMode(): boolean {
    return localStorage.getItem('darkMode') === 'true'
  }

  /**
   * 初始化检测器，注册事件监听
   */
  public init(): void {
    if (this.initialized) return
    
    // 监听深色模式变化
    window.addEventListener('storage', this.handleStorageChange.bind(this))
    
    // 设置初始状态
    this.applyDarkModeToAllCharts()
    
    this.initialized = true
  }

  /**
   * 处理存储变化事件
   */
  private handleStorageChange(e: StorageEvent): void {
    if (e.key === 'darkMode') {
      this.applyDarkModeToAllCharts()
    }
  }

  /**
   * 应用深色模式到所有ECharts实例
   */
  private applyDarkModeToAllCharts(): void {
    const isDark = this.isDarkMode()
    
    // 延迟处理，确保DOM已更新
    setTimeout(() => {
      // 为所有图表容器应用背景色
      const chartContainers = document.querySelectorAll('.chart-container')
      chartContainers.forEach(container => {
        if (container instanceof HTMLElement) {
          container.style.backgroundColor = isDark ? '#282838' : '#fff'
        }
      })
      
      // 查找所有ECharts实例并应用主题
      const echartsInstances = document.querySelectorAll('[_echarts_instance_]')
      echartsInstances.forEach(chartElement => {
        const instance = echarts.getInstanceByDom(chartElement as HTMLElement)
        if (instance) {
          const option = instance.getOption()
          option.backgroundColor = isDark ? '#282838' : '#fff'
          option.textStyle = option.textStyle || {}
          option.textStyle.color = isDark ? '#e2e2e6' : '#333'
          
          // 重新应用选项
          instance.setOption(option, true)
        }
      })
    }, 300)
  }
}

// 创建并导出单例
const darkModeDetector = new DarkModeDetector()

export default darkModeDetector 