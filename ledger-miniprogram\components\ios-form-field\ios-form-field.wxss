/* components/ios-form-field/ios-form-field.wxss */

.ios-form-field {
  
}

.ios-field-group {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
}

.ios-field-label {
  padding: 32rpx 32rpx 16rpx 32rpx;
  font-size: 28rpx;
  color: #8E8E93;
  font-weight: 500;
  display: block;
}

.ios-field-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  min-height: 88rpx;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.ios-field-row:active {
  background-color: #F2F2F7;
}

.ios-field-row .ios-field-label {
  font-size: 34rpx;
  color: #000000;
  font-weight: 500;
  min-width: 160rpx;
  padding: 0;
  display: inline;
}

.ios-field-value {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.ios-value-text {
  font-size: 34rpx;
  color: #000000;
  margin-right: 16rpx;
}

.ios-value-text.placeholder {
  color: #C7C7CC;
}

.ios-chevron {
  font-size: 32rpx;
  color: #C7C7CC;
  font-weight: 500;
}

/* 输入框样式 */
.ios-input-container {
  position: relative;
  padding: 0 32rpx 32rpx 32rpx;
}

.ios-input {
  width: 100%;
  font-size: 34rpx;
  color: #000000;
  background: transparent;
  border: none;
  outline: none;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #E5E5EA;
  transition: border-color 0.3s ease;
}

.ios-input:focus {
  border-bottom-color: #007AFF;
}

.ios-input.amount-input {
  padding-right: 60rpx;
  text-align: right;
}

.ios-input-suffix {
  position: absolute;
  right: 32rpx;
  bottom: 48rpx;
  font-size: 34rpx;
  color: #8E8E93;
}
