<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { showToast } from 'vant'
import { Popup, Button } from 'vant'

// 导入样式
import 'vant/es/toast/style'
import 'vant/es/popup/style'
import 'vant/es/button/style'

const props = defineProps({
  modelValue: {
    type: [Date, String, Number],
    default: null
  },
  placeholder: {
    type: String,
    default: '选择日期和时间'
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 显示状态
const showPicker = ref(false)

// 当前值和临时值
const currentValue = ref(props.modelValue || formatCurrentTime())
const tempDate = ref('')
const tempTime = ref('')

// 初始化临时日期和时间
function initTempValues() {
  const dateTimeStr = typeof currentValue.value === 'string' ? 
    currentValue.value : 
    (currentValue.value instanceof Date ? 
      currentValue.value.toISOString().slice(0, 19).replace('T', ' ') : 
      formatCurrentTime())
  const [datePart, timePart] = dateTimeStr.split(' ')
  tempDate.value = datePart
  tempTime.value = timePart
}

// 格式化当前时间为字符串
function formatCurrentTime() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 显示值
const displayValue = computed(() => {
  if (!currentValue.value) {
    return props.placeholder
  }
  return currentValue.value
})

// 打开选择器
function openPicker() {
  if (props.disabled) return
  initTempValues()
  showPicker.value = true
}

// 确认选择
function confirmPicker() {
  if (tempDate.value && tempTime.value) {
    currentValue.value = `${tempDate.value} ${tempTime.value}`
    emit('update:modelValue', currentValue.value)
    emit('change', currentValue.value)
    showPicker.value = false
    
    showToast({
      message: '时间已选择',
      position: 'bottom',
    })
  } else {
    showToast({
      message: '请选择完整的日期和时间',
      position: 'bottom',
    })
  }
}

// 取消选择
function cancelPicker() {
  showPicker.value = false
}

// 使用当前时间
function useCurrentTime() {
  currentValue.value = formatCurrentTime()
  emit('update:modelValue', currentValue.value)
  emit('change', currentValue.value)
  
  showPicker.value = false
  
  showToast({
    message: '已使用当前时间',
    position: 'bottom',
  })
}

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  if (newVal !== undefined && newVal !== null) {
    currentValue.value = newVal
  }
})
</script>

<template>
  <div class="mobile-datetime-picker">
    <!-- 显示框 -->
    <div class="picker-input" @click="openPicker">
      <div class="picker-value" :class="{ 'placeholder': !currentValue }">
        {{ displayValue }}
      </div>
      <div class="picker-icon">
        <i class="datetime-icon"></i>
      </div>
    </div>
    
    <!-- Vant弹出层 -->
    <Popup
      v-model:show="showPicker"
      position="bottom"
      round
      closeable
      close-icon="close"
      :style="{ height: 'auto' }"
    >
      <div class="picker-header">
        <Button plain size="small" @click="cancelPicker">取消</Button>
        <div class="picker-title">选择日期和时间</div>
        <Button type="primary" size="small" @click="confirmPicker">确认</Button>
      </div>
      
      <div class="picker-content">
        <div class="input-group">
          <label for="date-input">日期：</label>
          <input 
            id="date-input" 
            type="date" 
            v-model="tempDate" 
            class="date-input"
          />
        </div>
        
        <div class="input-group">
          <label for="time-input">时间：</label>
          <input 
            id="time-input" 
            type="time" 
            v-model="tempTime" 
            step="1"
            class="time-input"
          />
        </div>
        
        <div class="current-time">
          <span>当前时间：{{ formatCurrentTime() }}</span>
        </div>
      </div>
    </Popup>
  </div>
</template>

<style lang="scss" scoped>
.mobile-datetime-picker {
  width: 100%;
  
  .picker-input {
    width: 100%;
    height: 40px;
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #dcdfe6;
    display: flex;
    align-items: center;
    padding: 0 12px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:active {
      background-color: #f5f7fa;
    }
    
    .picker-value {
      flex: 1;
      font-size: 14px;
      color: #333;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      
      &.placeholder {
        color: #999;
      }
    }
    
    .picker-icon {
      width: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      
      .datetime-icon {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        border: 1px solid #dcdfe6;
        position: relative;
        
        &::before {
          content: '';
          position: absolute;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background-color: #409eff;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }
  }
  
  .picker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 16px;
    border-bottom: 1px solid #f2f2f2;
    
    .picker-title {
      font-size: 16px;
      font-weight: 500;
    }
  }
  
  .picker-content {
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .input-group {
      width: 100%;
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      
      label {
        width: 60px;
        font-size: 15px;
        color: #333;
      }
      
      .date-input,
      .time-input {
        flex: 1;
        height: 40px;
        border: 1px solid #dcdfe6;
        border-radius: 8px;
        padding: 0 12px;
        font-size: 15px;
        background-color: #fff;
      }
    }
    
    .current-time {
      width: 100%;
      font-size: 14px;
      color: #666;
      margin: 10px 0;
      text-align: center;
    }
  }
}

/* 适配深色模式 */
:global(.dark-mode) {
  .mobile-datetime-picker {
    .picker-input {
      background-color: #282838;
      border-color: #363646;
      
      .picker-value {
        color: #e2e2e6;
        
        &.placeholder {
          color: #909399;
        }
      }
      
      .picker-icon .datetime-icon {
        border-color: #606266;
      }
    }
    
    .picker-content {
      background-color: #282838;
      
      .input-group label {
        color: #e2e2e6;
      }
      
      .date-input,
      .time-input {
        background-color: #363646;
        border-color: #484858;
        color: #e2e2e6;
      }
      
      .current-time {
        color: #a0a0a0;
      }
    }
  }
}
</style>