/* pages/test/api-test.wxss */
.test-page {
  padding: 32rpx;
  background-color: #F2F2F7;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.test-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #000000;
}

.test-section {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 24rpx;
  display: block;
}

.test-btn {
  width: 100%;
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 30rpx;
  margin-bottom: 16rpx;
}

.test-btn:last-child {
  margin-bottom: 0;
}

.test-logs {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  max-height: 600rpx;
  overflow-y: scroll;
}

.logs-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 24rpx;
  display: block;
}

.log-item {
  margin-bottom: 12rpx;
  padding: 16rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
}

.log-text {
  font-size: 24rpx;
  color: #333333;
  word-break: break-all;
}
