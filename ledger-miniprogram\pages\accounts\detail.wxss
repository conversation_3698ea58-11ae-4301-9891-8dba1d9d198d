/* pages/accounts/detail.wxss */
.account-detail-page {
  min-height: 100vh;
  background-color: #F2F2F7;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background-color: #FFFFFF;
  border-bottom: 1rpx solid #E5E5EA;
}

.header-left {
  display: flex;
  align-items: center;
  color: #007AFF;
  font-size: 32rpx;
}

.back-icon {
  font-size: 48rpx;
  margin-right: 8rpx;
  font-weight: 300;
}

.back-text {
  font-size: 32rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

.header-right {
  width: 80rpx;
  text-align: right;
}

.edit-text {
  color: #007AFF;
  font-size: 32rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #E5E5EA;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  color: #8E8E93;
  font-size: 28rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 账户内容 */
.account-content {
  padding: 32rpx;
}

.account-card {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
}

.account-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.account-icon-large {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background-color: #F2F2F7;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
}

.account-icon-large .icon-text {
  font-size: 60rpx;
}

.account-main-info {
  flex: 1;
}

.account-name {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 8rpx;
}

.account-type {
  display: block;
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 4rpx;
}

.account-institution {
  display: block;
  font-size: 26rpx;
  color: #C7C7CC;
}

.account-balance-section {
  text-align: center;
  padding: 32rpx 0;
  border-top: 1rpx solid #F2F2F7;
  border-bottom: 1rpx solid #F2F2F7;
  margin-bottom: 32rpx;
}

.balance-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
}

.balance-label {
  margin-right: 16rpx;
}

.eye-toggle {
  font-size: 32rpx;
  padding: 8rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.1);
  min-width: 48rpx;
  text-align: center;
}

.balance-label {
  display: block;
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 16rpx;
}

.balance-amount {
  font-size: 64rpx;
  font-weight: bold;
  color: #000000;
}

.account-details {
  
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F2F2F7;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 30rpx;
  color: #8E8E93;
}

.detail-value {
  font-size: 30rpx;
  color: #000000;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  border: none;
  font-size: 28rpx;
}

.action-btn.primary {
  background-color: #007AFF;
  color: white;
}

.action-btn.secondary {
  background-color: #FFFFFF;
  color: #007AFF;
  border: 2rpx solid #E5E5EA;
}

.btn-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
}

.btn-text {
  font-size: 28rpx;
}

/* 最近交易 */
.recent-transactions {
  
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

.more-btn {
  color: #007AFF;
  font-size: 28rpx;
}

.transactions-list {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
}

.transaction-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #F2F2F7;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
}

.transaction-icon .icon-text {
  font-size: 40rpx;
}

.transaction-info {
  flex: 1;
}

.transaction-desc {
  display: block;
  font-size: 34rpx;
  color: #000000;
  margin-bottom: 4rpx;
}

.transaction-category {
  font-size: 26rpx;
  color: #8E8E93;
}

.transaction-right {
  text-align: right;
}

.transaction-amount {
  display: block;
  font-size: 34rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.transaction-amount.income {
  color: #34C759;
}

.transaction-amount.expense {
  color: #FF3B30;
}

.transaction-date {
  font-size: 26rpx;
  color: #8E8E93;
}

/* 无交易记录 */
.no-transactions {
  text-align: center;
  padding: 80rpx 40rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
}

.no-transactions-icon {
  display: block;
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.no-transactions-text {
  display: block;
  font-size: 30rpx;
  color: #8E8E93;
  margin-bottom: 12rpx;
}

.no-transactions-hint {
  display: block;
  font-size: 26rpx;
  color: #C7C7CC;
}

/* 危险操作区域 */
.danger-zone {
  margin-top: 80rpx;
  padding-top: 40rpx;
  border-top: 1rpx solid #E5E5EA;
}

.danger-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
  background-color: #FFFFFF;
  border: 2rpx solid #FF3B30;
  border-radius: 16rpx;
  color: #FF3B30;
  font-size: 32rpx;
}

.danger-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.danger-text {
  font-size: 32rpx;
}
