// pages/accounts/edit.js
const api = require('../../utils/api.js')

Page({
  data: {
    accountId: '',
    form: {
      account_name: '',
      account_type: '',
      institution: '',
      account_number: '',
      currency: 'CNY',
      is_active: true
    },
    originalData: {},
    accountTypes: [
      { value: 'bank', label: '银行账户', icon: '🏦' },
      { value: 'fund', label: '基金账户', icon: '📊' },
      { value: 'stock', label: '股票账户', icon: '📈' },
      { value: 'debt', label: '负债账户', icon: '💳' }
    ],
    currencies: ['CNY', 'USD', 'EUR', 'JPY', 'GBP', 'HKD'],
    currencyOptions: [
      { value: 'CNY', label: '人民币 (CNY)' },
      { value: 'USD', label: '美元 (USD)' },
      { value: 'EUR', label: '欧元 (EUR)' },
      { value: 'JPY', label: '日元 (JPY)' },
      { value: 'GBP', label: '英镑 (GBP)' },
      { value: 'HKD', label: '港币 (HKD)' }
    ],
    showAccountTypePicker: false,
    showCurrencyPicker: false,
    loading: false,
    submitting: false
  },

  onLoad(options) {
    console.log('编辑账户页面加载', options)
    if (options.id) {
      this.setData({ accountId: options.id })
      this.loadAccountDetail()
    } else {
      wx.showToast({
        title: '账户ID缺失',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 加载账户详情
  async loadAccountDetail() {
    this.setData({ loading: true })

    try {
      const account = await api.accounts.getDetail(this.data.accountId)
      console.log('加载账户详情:', account)

      const formData = {
        account_name: account.account_name || '',
        account_type: account.account_type || '',
        institution: account.institution || '',
        account_number: account.account_number || '',
        currency: account.currency || 'CNY',
        is_active: account.is_active !== false
      }

      this.setData({
        form: formData,
        originalData: { ...formData }
      })
      
      this.updateComputedData()

    } catch (error) {
      console.error('加载账户详情失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    } finally {
      this.setData({ loading: false })
    }
  },

  // 更新计算属性
  updateComputedData() {
    const type = this.data.accountTypes.find(t => t.value === this.data.form.account_type)
    const accountTypeDisplay = type ? type.label : ''
    
    const { account_name, account_type } = this.data.form
    const canSubmit = account_name.trim() && account_type

    this.setData({
      accountTypeDisplay,
      canSubmit
    })
  },

  // 输入处理
  onAccountNameInput(e) {
    this.setData({
      'form.account_name': e.detail.value
    })
    this.updateComputedData()
  },

  onInstitutionInput(e) {
    this.setData({
      'form.institution': e.detail.value
    })
  },

  onAccountNumberInput(e) {
    this.setData({
      'form.account_number': e.detail.value
    })
  },

  onActiveChange(e) {
    this.setData({
      'form.is_active': e.detail.value
    })
  },

  // 选择器控制
  showAccountTypePicker() {
    this.setData({ showAccountTypePicker: true })
  },

  hideAccountTypePicker() {
    this.setData({ showAccountTypePicker: false })
  },

  showCurrencyPicker() {
    this.setData({ showCurrencyPicker: true })
  },

  hideCurrencyPicker() {
    this.setData({ showCurrencyPicker: false })
  },

  stopPropagation() {
    // 阻止事件冒泡
  },

  // 选择账户类型
  selectAccountType(e) {
    const value = e.detail.value
    this.setData({
      'form.account_type': value,
      showAccountTypePicker: false
    })
    this.updateComputedData()
  },

  // 选择货币类型
  selectCurrency(e) {
    const value = e.detail.value
    this.setData({
      'form.currency': value,
      showCurrencyPicker: false
    })
  },

  // 保存账户
  async saveAccount() {
    if (!this.data.canSubmit) {
      wx.showToast({
        title: '请填写必要信息',
        icon: 'none'
      })
      return
    }

    this.setData({ submitting: true })

    try {
      const updateData = {
        account_name: this.data.form.account_name.trim(),
        account_type: this.data.form.account_type,
        institution: this.data.form.institution.trim() || null,
        account_number: this.data.form.account_number.trim() || null,
        currency: this.data.form.currency,
        is_active: this.data.form.is_active
      }

      console.log('更新账户数据:', updateData)

      await api.accounts.update(this.data.accountId, updateData)

      wx.showToast({
        title: '账户更新成功',
        icon: 'success'
      })

      // 返回账户详情页面
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)

    } catch (error) {
      console.error('更新账户失败:', error)
      wx.showToast({
        title: error.message || '更新失败',
        icon: 'none'
      })
    } finally {
      this.setData({ submitting: false })
    }
  },

  // 返回
  goBack() {
    wx.navigateBack()
  }
})
