// pages/analytics/detail.js
const api = require('../../utils/api.js')

Page({
  data: {
    timeRange: 'month', // month, quarter, year, custom
    startDate: '',
    endDate: '',
    hideAmounts: false,
    
    // 核心指标
    totalIncome: '0.00',
    totalExpense: '0.00',
    netIncome: 0,
    savingsRate: 0,
    incomeChange: 0,
    expenseChange: 0,
    netIncomeChange: 0,
    savingsRateChange: 0,
    
    // 分析数据
    expenseViewType: 'category', // category, trend
    topExpenseCategories: [],
    incomeCategories: [],
    monthlyComparison: [],
    financialAdvice: [],
    
    loading: false
  },

  onLoad(options) {
    console.log('详细分析页面加载')
    this.loadHideAmountsState()
    this.initTimeRange()
    this.loadDetailAnalytics()
  },

  onShow() {
    this.loadDetailAnalytics()
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  },

  // 分享报告
  shareReport() {
    wx.showActionSheet({
      itemList: ['生成图片报告', '导出Excel', '分享给好友'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.generateImageReport()
            break
          case 1:
            this.exportExcel()
            break
          case 2:
            this.shareToFriend()
            break
        }
      }
    })
  },

  // 初始化时间范围
  initTimeRange() {
    const now = new Date()
    const year = now.getFullYear()
    const month = now.getMonth() + 1
    
    this.setData({
      startDate: `${year}-${month.toString().padStart(2, '0')}-01`,
      endDate: `${year}-${month.toString().padStart(2, '0')}-${new Date(year, month, 0).getDate()}`
    })
  },

  // 切换时间范围
  switchTimeRange(e) {
    const range = e.currentTarget.dataset.range
    this.setData({ timeRange: range })
    
    if (range !== 'custom') {
      this.calculateDateRange(range)
    }
    
    this.loadDetailAnalytics()
  },

  // 计算日期范围
  calculateDateRange(range) {
    const now = new Date()
    let startDate, endDate
    
    switch (range) {
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1)
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0)
        break
      case 'quarter':
        const quarter = Math.floor(now.getMonth() / 3)
        startDate = new Date(now.getFullYear(), quarter * 3, 1)
        endDate = new Date(now.getFullYear(), quarter * 3 + 3, 0)
        break
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1)
        endDate = new Date(now.getFullYear(), 11, 31)
        break
    }
    
    this.setData({
      startDate: this.formatDate(startDate),
      endDate: this.formatDate(endDate)
    })
  },

  // 格式化日期
  formatDate(date) {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  // 开始日期选择
  onStartDateChange(e) {
    this.setData({
      startDate: e.detail.value
    })
    this.loadDetailAnalytics()
  },

  // 结束日期选择
  onEndDateChange(e) {
    this.setData({
      endDate: e.detail.value
    })
    this.loadDetailAnalytics()
  },

  // 切换支出视图
  switchExpenseView(e) {
    const type = e.currentTarget.dataset.type
    this.setData({ expenseViewType: type })
  },

  // 加载详细分析数据
  async loadDetailAnalytics() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      // 并行加载各种数据
      await Promise.all([
        this.loadCoreMetrics(),
        this.loadCategoryAnalysis(),
        this.loadMonthlyComparison(),
        this.generateFinancialAdvice()
      ])
    } catch (error) {
      console.error('加载详细分析数据失败:', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载核心指标
  async loadCoreMetrics() {
    // 这里应该调用后端API获取指定时间范围的数据
    // 暂时使用模拟数据
    const mockData = {
      totalIncome: 15000,
      totalExpense: 8500,
      incomeChange: 12.5,
      expenseChange: -5.2,
      netIncomeChange: 25.8,
      savingsRateChange: 8.3
    }

    const netIncome = mockData.totalIncome - mockData.totalExpense
    const savingsRate = mockData.totalIncome > 0 ? 
      ((netIncome / mockData.totalIncome) * 100).toFixed(1) : 0

    this.setData({
      totalIncome: this.formatAmount(mockData.totalIncome),
      totalExpense: this.formatAmount(mockData.totalExpense),
      netIncome: netIncome,
      savingsRate: savingsRate,
      incomeChange: mockData.incomeChange,
      expenseChange: mockData.expenseChange,
      netIncomeChange: mockData.netIncomeChange,
      savingsRateChange: mockData.savingsRateChange
    })
  },

  // 加载分类分析
  async loadCategoryAnalysis() {
    // 模拟数据
    const topExpenseCategories = [
      { id: 1, name: '餐饮美食', icon: '🍽️', amount: 2500, percentage: 29.4 },
      { id: 2, name: '交通出行', icon: '🚗', amount: 1800, percentage: 21.2 },
      { id: 3, name: '购物消费', icon: '🛍️', amount: 1500, percentage: 17.6 },
      { id: 4, name: '生活服务', icon: '🏠', amount: 1200, percentage: 14.1 },
      { id: 5, name: '娱乐休闲', icon: '🎮', amount: 800, percentage: 9.4 }
    ]

    const incomeCategories = [
      { id: 1, name: '工资收入', icon: '💰', amount: 12000, percentage: 80.0 },
      { id: 2, name: '投资收益', icon: '📈', amount: 2000, percentage: 13.3 },
      { id: 3, name: '其他收入', icon: '💵', amount: 1000, percentage: 6.7 }
    ]

    // 格式化金额
    topExpenseCategories.forEach(item => {
      item.amountText = this.formatAmount(item.amount)
    })

    incomeCategories.forEach(item => {
      item.amountText = this.formatAmount(item.amount)
    })

    this.setData({
      topExpenseCategories,
      incomeCategories
    })
  },

  // 加载月度对比
  async loadMonthlyComparison() {
    // 模拟近6个月数据
    const monthlyData = [
      { month: 1, income: 14000, expense: 9000 },
      { month: 2, income: 13500, expense: 8200 },
      { month: 3, income: 15200, expense: 8800 },
      { month: 4, income: 14800, expense: 9200 },
      { month: 5, income: 15500, expense: 8500 },
      { month: 6, income: 15000, expense: 8500 }
    ]

    const maxAmount = Math.max(...monthlyData.map(item => Math.max(item.income, item.expense)))

    const monthlyComparison = monthlyData.map(item => ({
      month: item.month,
      monthText: `${item.month}月`,
      income: item.income,
      expense: item.expense,
      incomeText: this.formatAmount(item.income),
      expenseText: this.formatAmount(item.expense),
      incomeHeight: (item.income / maxAmount * 100),
      expenseHeight: (item.expense / maxAmount * 100)
    }))

    this.setData({ monthlyComparison })
  },

  // 生成财务建议
  async generateFinancialAdvice() {
    const advice = [
      {
        id: 1,
        type: 'warning',
        icon: '⚠️',
        title: '餐饮支出偏高',
        description: '本月餐饮支出占总支出的29.4%，建议控制在20%以内'
      },
      {
        id: 2,
        type: 'success',
        icon: '✅',
        title: '储蓄率良好',
        description: '当前储蓄率43.3%，超过建议的30%，继续保持'
      },
      {
        id: 3,
        type: 'info',
        icon: 'ℹ️',
        title: '投资建议',
        description: '可考虑将部分储蓄用于稳健投资，提高资金利用率'
      }
    ]

    this.setData({ financialAdvice: advice })
  },

  // 格式化金额
  formatAmount(amount) {
    return parseFloat(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  },

  // 加载隐藏金额状态
  loadHideAmountsState() {
    try {
      const hideAmounts = wx.getStorageSync('hideAmounts')
      this.setData({
        hideAmounts: hideAmounts === 'true' || hideAmounts === true
      })
    } catch (error) {
      console.error('加载隐藏金额状态失败:', error)
    }
  },

  // 生成图片报告
  generateImageReport() {
    wx.showLoading({ title: '生成中...' })
    
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '报告已生成',
        icon: 'success'
      })
    }, 2000)
  },

  // 导出Excel
  exportExcel() {
    wx.showLoading({ title: '导出中...' })
    
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '导出成功',
        icon: 'success'
      })
    }, 2000)
  },

  // 分享给好友
  shareToFriend() {
    wx.showShareMenu({
      withShareTicket: true
    })
  }
})
