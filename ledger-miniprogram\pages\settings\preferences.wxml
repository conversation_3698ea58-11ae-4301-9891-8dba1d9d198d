<!-- 偏好设置页面 -->
<view class="preferences-page">
  <!-- 显示设置 -->
  <view class="settings-section">
    <view class="section-header">显示设置</view>
    <view class="section-content">
      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-title">深色模式</text>
          <text class="setting-desc">跟随系统设置或手动切换</text>
        </view>
        <switch 
          checked="{{preferences.darkMode}}" 
          bindchange="onDarkModeChange"
          color="#007AFF"
        />
      </view>

      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-title">默认货币</text>
          <text class="setting-desc">{{preferences.currency || 'CNY'}}</text>
        </view>
        <text class="setting-arrow" bindtap="selectCurrency">></text>
      </view>

      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-title">金额显示精度</text>
          <text class="setting-desc">小数点后{{preferences.decimalPlaces || 2}}位</text>
        </view>
        <text class="setting-arrow" bindtap="selectDecimalPlaces">></text>
      </view>
    </view>
  </view>

  <!-- 交易设置 -->
  <view class="settings-section">
    <view class="section-header">交易设置</view>
    <view class="section-content">
      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-title">快速记账</text>
          <text class="setting-desc">启用快速添加常用交易</text>
        </view>
        <switch 
          checked="{{preferences.quickTransaction}}" 
          bindchange="onQuickTransactionChange"
          color="#007AFF"
        />
      </view>

      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-title">默认交易类型</text>
          <text class="setting-desc">{{preferences.defaultTransactionType === 'expense' ? '支出' : '收入'}}</text>
        </view>
        <text class="setting-arrow" bindtap="selectDefaultTransactionType">></text>
      </view>

      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-title">记账提醒</text>
          <text class="setting-desc">每日定时提醒记账</text>
        </view>
        <switch 
          checked="{{preferences.dailyReminder}}" 
          bindchange="onDailyReminderChange"
          color="#007AFF"
        />
      </view>
    </view>
  </view>

  <!-- 数据设置 -->
  <view class="settings-section">
    <view class="section-header">数据设置</view>
    <view class="section-content">
      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-title">自动备份</text>
          <text class="setting-desc">定期备份数据到云端</text>
        </view>
        <switch
          checked="{{preferences.autoBackup}}"
          bindchange="onAutoBackupChange"
          color="#007AFF"
        />
      </view>

      <view class="setting-item" bindtap="backupData">
        <view class="setting-info">
          <text class="setting-title">立即备份</text>
          <text class="setting-desc">手动创建数据备份</text>
        </view>
        <text class="setting-arrow">></text>
      </view>

      <view class="setting-item" bindtap="exportData">
        <view class="setting-info">
          <text class="setting-title">导出数据</text>
          <text class="setting-desc">导出所有交易记录</text>
        </view>
        <text class="setting-arrow">></text>
      </view>

      <view class="setting-item" bindtap="restoreData">
        <view class="setting-info">
          <text class="setting-title">恢复数据</text>
          <text class="setting-desc">从最新备份恢复数据</text>
        </view>
        <text class="setting-arrow">></text>
      </view>

      <view class="setting-item" bindtap="importData">
        <view class="setting-info">
          <text class="setting-title">导入数据</text>
          <text class="setting-desc">从备份文件恢复数据</text>
        </view>
        <text class="setting-arrow">></text>
      </view>
    </view>
  </view>



  <!-- 保存按钮 -->
  <view class="save-section">
    <button 
      class="btn-primary save-btn" 
      bindtap="savePreferences"
      loading="{{saving}}"
    >
      {{saving ? '保存中...' : '保存设置'}}
    </button>
  </view>
</view>

<!-- 货币选择弹窗 -->
<view class="modal-overlay" wx:if="{{showCurrencyModal}}" bindtap="closeCurrencyModal">
  <view class="modal-content" catchtap="">
    <view class="modal-header">
      <text class="modal-title">选择货币</text>
      <text class="modal-close" bindtap="closeCurrencyModal">×</text>
    </view>
    <view class="modal-body">
      <view 
        class="currency-item {{item.code === preferences.currency ? 'selected' : ''}}"
        wx:for="{{currencyOptions}}"
        wx:key="code"
        bindtap="selectCurrencyOption"
        data-currency="{{item.code}}"
      >
        <text class="currency-code">{{item.code}}</text>
        <text class="currency-name">{{item.name}}</text>
        <text class="currency-check" wx:if="{{item.code === preferences.currency}}">✓</text>
      </view>
    </view>
  </view>
</view>
