<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElSelect, ElOption, ElInput, ElForm, ElFormItem, ElButton } from 'element-plus'
import type { FormItemRule } from 'element-plus'
import { getAccounts } from '@/api/account'
import { createTransaction } from '@/api/transaction'
import type { Account, TransactionCreateParams } from '@/api/types'
import { useResponsive } from '@/plugins/useResponsive'

// 使用响应式工具检测设备类型
const { isMobile } = useResponsive()

// 组件接收的属性
interface Props {
  visible: boolean
  fromAccountId?: number
}

const props = defineProps<Props>()

// 事件
const emit = defineEmits(['close', 'success'])

// 响应式状态
const accounts = ref<Account[]>([])
const loading = ref(false)
const submitting = ref(false)

// 表单数据
const form = reactive({
  from_account_id: props.fromAccountId || undefined as number | undefined,
  to_account_id: undefined as number | undefined,
  amount: 0,
  description: ''
})

// 表单校验规则
const rules = {
  from_account_id: [
    { required: true, message: '请选择转出账户', trigger: 'change' }
  ],
  to_account_id: [
    { required: true, message: '请选择转入账户', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '金额必须大于0', trigger: 'blur' }
  ]
} as Record<string, FormItemRule[]>

// 表单引用
const formRef = ref()

// 可选的转入账户列表(排除当前选择的转出账户)
const filteredToAccounts = computed(() => {
  return accounts.value.filter(account => account.id !== form.from_account_id)
})

// 监听属性变化
watch(() => props.fromAccountId, (newVal) => {
  if (newVal) {
    form.from_account_id = newVal
  }
})

// 获取账户列表
const fetchAccounts = async () => {
  try {
    loading.value = true
    accounts.value = await getAccounts()
  } catch (error) {
    console.error('获取账户列表失败', error)
    ElMessage.error('获取账户列表失败')
  } finally {
    loading.value = false
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        submitting.value = true
        
        const transferData = {
          type: 'transfer',
          amount: form.amount,
          from_account_id: form.from_account_id,
          to_account_id: form.to_account_id,
          account_id: form.from_account_id, // 转账时也需要设置account_id为转出账户ID
          description: form.description || '账户转账'
        }
        
        console.log('提交转账数据:', transferData)
        
        await createTransaction(transferData)
        
        ElMessage.success('转账成功')
        emit('success')
        emit('close')
      } catch (error: any) {
        console.error('转账失败', error)
        ElMessage.error('转账失败: ' + (error.response?.data?.detail || '未知错误'))
      } finally {
        submitting.value = false
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  form.from_account_id = props.fromAccountId || undefined
  form.to_account_id = undefined
  form.amount = 0
  form.description = ''
}

// 关闭对话框
const handleClose = () => {
  resetForm()
  emit('close')
}

// 组件挂载时获取账户列表
fetchAccounts()
</script>

<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    :label-position="isMobile ? 'top' : 'top'"
    v-loading="loading"
    class="transfer-form"
  >
    <el-form-item label="转出账户" prop="from_account_id">
      <el-select
        v-model="form.from_account_id"
        placeholder="请选择转出账户"
        style="width: 100%"
        size="large"
      >
        <el-option
          v-for="account in accounts"
          :key="account.id"
          :label="account.account_name"
          :value="account.id"
        >
          <div class="account-option">
            <span>{{ account.account_name }}</span>
            <span class="balance">余额: {{ Number(account.current_balance).toFixed(2) }}</span>
          </div>
        </el-option>
      </el-select>
    </el-form-item>
    
    <el-form-item label="转入账户" prop="to_account_id">
      <el-select 
        v-model="form.to_account_id"
        placeholder="请选择转入账户"
        style="width: 100%"
        filterable
      >
        <el-option
          v-for="account in filteredToAccounts"
          :key="account.id"
          :label="account.account_name"
          :value="account.id"
        >
          <div class="account-option">
            <span>{{ account.account_name }}</span>
            <span class="balance">余额: {{ Number(account.current_balance).toFixed(2) }}</span>
          </div>
        </el-option>
      </el-select>
    </el-form-item>
    
    <el-form-item label="转账金额" prop="amount">
      <el-input
        v-model.number="form.amount"
        type="number"
        placeholder="请输入转账金额"
        size="large"
      />
    </el-form-item>
    
    <el-form-item label="备注" prop="description">
      <el-input
        v-model="form.description"
        type="textarea"
        :rows="2"
        placeholder="请输入转账备注(选填)"
      />
    </el-form-item>
    
    <div class="form-actions">
      <el-button @click="handleClose" size="large">取消</el-button>
      <el-button
        type="primary"
        @click="submitForm"
        :loading="submitting"
        size="large"
      >
        确认转账
      </el-button>
    </div>
  </el-form>
</template>

<style lang="scss" scoped>
.transfer-form {
  width: 100%;
}

.account-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .balance {
    color: var(--apple-gray);
    font-size: 0.9em;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-md);
  gap: var(--spacing-sm);
  
  @media (max-width: 768px) {
    flex-direction: column;
    
    .el-button {
      width: 100%;
      margin-left: 0 !important;
      margin-bottom: 10px;
      height: 44px;
    }
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
  
  @media (max-width: 768px) {
    padding-bottom: 5px;
  }
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-input__wrapper),
:deep(.el-textarea__inner) {
  @media (max-width: 768px) {
    padding: 8px 12px;
  }
}

:deep(.el-input__inner) {
  @media (max-width: 768px) {
    height: 44px;
    font-size: 16px; /* 防止iOS缩放 */
  }
}

:deep(.el-textarea__inner) {
  @media (max-width: 768px) {
    font-size: 16px; /* 防止iOS缩放 */
  }
}
</style> 