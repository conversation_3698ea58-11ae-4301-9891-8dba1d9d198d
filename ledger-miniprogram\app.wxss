/* app.wxss */
page {
  background-color: #F2F2F7;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 34rpx;
  line-height: 1.4;
  color: #000000;
}

/* 全局容器样式 */
.page-container {
  min-height: 100vh;
  background-color: #F2F2F7;
}

/* 全局按钮样式 */
.btn-primary {
  background-color: #007AFF;
  color: white;
  border-radius: 20rpx;
  padding: 24rpx 32rpx;
  font-size: 34rpx;
  border: none;
}

.btn-primary:active {
  background-color: #0056CC;
}

/* 全局卡片样式 */
.card {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  margin: 32rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 全局列表样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.list-item:last-child {
  border-bottom: none;
}

/* 工具类 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}
