/* 全局样式文件 */

/* 引入Element Plus样式 */
@import 'element-plus/dist/index.css';

/* 基础变量 */
:root {
  /* Apple风格颜色变量 */
  --apple-blue: #007AFF;
  --apple-green: #34C759;
  --apple-red: #FF3B30;
  --apple-yellow: #FFCC00;
  --apple-gray: #8E8E93;
  --apple-light-gray: #F2F2F7;
  --apple-dark-gray: #1C1C1E;
  
  /* 基础尺寸 */
  --border-radius: 10px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
}

/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'San Francisco', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  color: var(--apple-dark-gray);
  background-color: var(--apple-light-gray);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 链接样式 */
a {
  color: var(--apple-blue);
  text-decoration: none;
  transition: all 0.2s ease;
}

  a:hover {
  opacity: 0.8;
}

/* 内容容器 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* 按钮样式增强 */
.el-button--primary {
  background-color: var(--apple-blue);
  border-color: var(--apple-blue);
}

.el-button--success {
  background-color: var(--apple-green);
  border-color: var(--apple-green);
}

.el-button--warning {
  background-color: var(--apple-yellow);
  border-color: var(--apple-yellow);
}

.el-button--danger {
  background-color: var(--apple-red);
  border-color: var(--apple-red);
}

/* 卡片样式增强 */
.el-card {
  border-radius: var(--border-radius);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease, transform 0.3s ease;
  border: none;
}

.el-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 输入框样式增强 */
.el-input__inner {
  border-radius: 8px;
}

/* 日期选择器样式增强 */
.el-date-picker {
  font-family: -apple-system, BlinkMacSystemFont, 'San Francisco', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.el-date-picker .el-picker-panel__content {
  margin: 10px;
}

.el-date-picker .el-date-table th {
  color: var(--apple-dark-gray);
  font-weight: 500;
}

.el-date-picker .el-date-table td.current:not(.disabled) .el-date-table-cell__text {
  background-color: var(--apple-blue);
  color: white;
  font-weight: 500;
}

.el-date-picker .el-date-table td.today .el-date-table-cell__text {
  color: var(--apple-blue);
  font-weight: 600;
}

.el-date-picker .el-date-table td.available:hover {
  color: var(--apple-blue);
}

.el-date-picker .el-date-picker__header-label {
  font-size: 16px;
  font-weight: 500;
  color: var(--apple-dark-gray);
}

.el-date-picker .el-picker-panel__icon-btn {
  color: var(--apple-dark-gray);
}

.el-date-picker .el-picker-panel__icon-btn:hover {
  color: var(--apple-blue);
}

.el-date-picker .el-picker-panel__footer {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 8px;
}

.el-date-picker .el-picker-panel__footer .el-button {
  font-weight: 500;
}

.el-date-picker .el-date-table td.selected .el-date-table-cell__text {
  background-color: var(--apple-blue) !important;
  color: white !important;
  border-radius: 50%;
}

.el-date-picker .el-month-table td .cell,
.el-date-picker .el-year-table td .cell {
  border-radius: 16px;
}

.el-date-picker .el-date-picker__header {
  margin: 8px 8px 0;
}

.el-date-picker .el-time-panel__content {
  padding: 8px;
}

/* 过渡效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  }

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式调整 */
@media (max-width: 768px) {
  :root {
    --spacing-lg: 16px;
  }
  
  .el-card {
    margin-bottom: var(--spacing-md);
  }
}
