import os
from pathlib import Path
from dotenv import load_dotenv
import importlib

# Load .env file
load_dotenv()

# Define the project's base directory, which is the root of the project.
BASE_DIR = Path(__file__).resolve().parent.parent.parent

class Settings:
    # --- Base Config ---
    APP_NAME: str = "金融账本系统"
    APP_VERSION: str = "1.0.0"
    PROJECT_NAME: str = APP_NAME  # 用于 FastAPI 标题
    API_V1_STR: str = "/api/v1"   # API 版本前缀
    DEBUG: bool = False
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key")
    ALLOWED_HOSTS: list[str] = ["*"]

    # --- JWT Config ---
    JWT_SECRET_KEY: str = SECRET_KEY
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 1440  # 24 hours

    # 备份设置
    # The default backup directory is now an absolute path to the 'backup' folder in the project root.
    BACKUP_DIR: str = os.getenv("BACKUP_DIR", str(BASE_DIR / "backup"))

    # --- Security Config ---
    CORS_ORIGINS: list[str] = ["http://localhost:8080", "http://127.0.0.1:8080"]
    RATE_LIMIT_PER_MINUTE: int = 60

    # --- MySQL Config ---
    MYSQL_HOST: str = "localhost"
    MYSQL_PORT: int = 3306
    MYSQL_USER: str = "root"
    MYSQL_PASSWORD: str = ""
    MYSQL_DATABASE: str = "finance_app"
    MYSQL_POOL_SIZE: int = 5
    MYSQL_POOL_RECYCLE: int = 3600

    # --- Redis Config ---
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: str = ""

    # --- Log Config ---
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE: str = ""  # Empty string means stdout

    # --- Email Config ---
    SMTP_HOST: str = "smtp.qq.com"  # QQ邮箱SMTP服务器
    SMTP_PORT: int = 587
    SMTP_USERNAME: str = ""  # 发送邮件的邮箱地址
    SMTP_PASSWORD: str = ""  # 邮箱授权码
    SMTP_USE_TLS: bool = True
    EMAIL_FROM: str = ""  # 发件人邮箱，通常与SMTP_USERNAME相同
    EMAIL_FROM_NAME: str = "金融账本系统"

    def __init__(self):
        self._load_env_config()

    def _load_env_config(self):
        # Environment-specific config loading
        env = os.getenv("APP_ENV", "development")
        try:
            env_module = importlib.import_module(f"app.core.config_{env}")
            for key, value in vars(env_module).items():
                if key.isupper():
                    setattr(self, key, value)
        except ImportError:
            pass

        # Override from environment variables
        for key in self.__class__.__annotations__:
            if key.isupper():
                # Correctly load from environment variable matching the key name
                env_value = os.getenv(key)
                if env_value is not None:
                    # Get the type of the default value
                    default_value = getattr(self, key)
                    original_type = type(default_value)

                    # Handle special types like bool and list
                    if original_type == bool:
                        setattr(self, key, env_value.lower() in ("true", "1", "t"))
                    elif original_type == list:
                        setattr(self, key, env_value.split(","))
                    else:
                        # Convert the string from env to the original type
                        setattr(self, key, original_type(env_value))

settings = Settings() 