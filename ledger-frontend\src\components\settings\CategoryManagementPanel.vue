<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ElButton, ElInput, ElDialog, ElMessage, ElMessageBox } from 'element-plus';
import { getCategories, createCategory, updateCategory, deleteCategory } from '../../api/category';
import { Plus, Edit, Delete, Food, ShoppingCart, VideoPlay, House, Bicycle, Basketball, Suitcase, 
  School, OfficeBuilding, Present, Money, Coin, DataAnalysis, Watch, Reading, Promotion,
  Lightning, Coffee, Van, CreditCard, Cellphone, Bell, Umbrella, Wallet, Headset, Medal, Close, ArrowRight } from '@element-plus/icons-vue';
import type { Category } from '../../api/category';
import { useResponsive } from '../../plugins/useResponsive';

// 获取设备类型
const { isMobile } = useResponsive();

// 分类类型选项
const categoryTypes = [
  { value: 'expense', label: '支出' },
  { value: 'income', label: '收入' },
];

// 图标选项 - 使用Element Plus内置图标
const iconOptions = ref([
  { name: '餐饮', icon: 'Food', component: Food },
  { name: '购物', icon: 'ShoppingCart', component: ShoppingCart },
  { name: '娱乐', icon: 'VideoPlay', component: VideoPlay },
  { name: '住房', icon: 'House', component: House },
  { name: '交通', icon: 'Van', component: Van },
  { name: '运动', icon: 'Basketball', component: Basketball },
  { name: '旅行', icon: 'Suitcase', component: Suitcase },
  { name: '教育', icon: 'School', component: School },
  { name: '工作', icon: 'OfficeBuilding', component: OfficeBuilding },
  { name: '礼物', icon: 'Present', component: Present },
  { name: '工资', icon: 'Money', component: Money },
  { name: '奖金', icon: 'Coin', component: Coin },
  { name: '投资', icon: 'DataAnalysis', component: DataAnalysis },
  { name: '水电煤', icon: 'Lightning', component: Lightning },
  { name: '饮料', icon: 'Coffee', component: Coffee },
  { name: '通信', icon: 'Cellphone', component: Cellphone },
  { name: '信用卡', icon: 'CreditCard', component: CreditCard },
  { name: '兼职', icon: 'Watch', component: Watch },
  { name: '其他收入', icon: 'Promotion', component: Promotion },
  { name: '医疗', icon: 'Bell', component: Bell },
  { name: '保险', icon: 'Umbrella', component: Umbrella },
  { name: '存款', icon: 'Wallet', component: Wallet },
  { name: '数码', icon: 'Headset', component: Headset },
  { name: '奖励', icon: 'Medal', component: Medal },
]);

// 状态
const expenseCategories = ref<Category[]>([]);
const incomeCategories = ref<Category[]>([]);
const loading = ref(false);
const activeTab = ref('expense');

// 移动端折叠展开状态
const isExpanded = ref(false);
const MOBILE_DISPLAY_LIMIT = 6; // 移动端默认显示6个分类（2行）

// 新增/编辑分类相关
const categoryDialogVisible = ref(false);
const iconSelectorVisible = ref(false);
const isEditing = ref(false);
const currentCategory = ref<Category>({
  id: 0,
  name: '',
  type: 'expense',
  icon: 'ShoppingCart',
  user_id: 0
});

// 当前选项卡的分类
const currentCategories = computed(() => {
  return activeTab.value === 'expense' ? expenseCategories.value : incomeCategories.value;
});

// 显示的分类（移动端可能被限制数量）
const displayedCategories = computed(() => {
  const categories = currentCategories.value;
  if (isMobile.value && !isExpanded.value && categories.length > MOBILE_DISPLAY_LIMIT) {
    return categories.slice(0, MOBILE_DISPLAY_LIMIT);
  }
  return categories;
});

// 是否需要显示"查看全部"按钮
const shouldShowExpandButton = computed(() => {
  return isMobile.value && !isExpanded.value && currentCategories.value.length > MOBILE_DISPLAY_LIMIT;
});

// 是否需要显示"收起"按钮
const shouldShowCollapseButton = computed(() => {
  return isMobile.value && isExpanded.value && currentCategories.value.length > MOBILE_DISPLAY_LIMIT;
});

// 加载分类数据
const loadCategories = async () => {
  loading.value = true;
  try {
    const data = await getCategories();
    if (data) {
      expenseCategories.value = data.filter(cat => cat.type === 'expense');
      incomeCategories.value = data.filter(cat => cat.type === 'income');
    }
  } catch (error) {
    console.error('加载分类失败:', error);
    ElMessage.error('加载分类失败');
  } finally {
    loading.value = false;
  }
};

// 打开新增分类对话框
const openAddCategoryDialog = (type: 'income' | 'expense') => {
  isEditing.value = false;
  currentCategory.value = {
    id: 0,
    name: '',
    type: type,
    icon: type === 'income' ? 'Money' : 'ShoppingCart',
    user_id: 0
  };
  categoryDialogVisible.value = true;
};

// 打开编辑分类对话框
const openEditCategoryDialog = (category: Category) => {
  isEditing.value = true;
  currentCategory.value = { ...category };
  categoryDialogVisible.value = true;
};

// 打开图标选择器
const openIconSelector = () => {
  iconSelectorVisible.value = true;
};

// 选择图标
const selectIcon = (iconName: string) => {
  currentCategory.value.icon = iconName;
  iconSelectorVisible.value = false;
};

// 保存分类
const saveCategory = async () => {
  if (!currentCategory.value.name.trim()) {
    ElMessage.warning('请输入分类名称');
    return;
  }

  try {
    if (isEditing.value) {
      const result = await updateCategory(currentCategory.value);
      if (result && result.code === 200) {
        ElMessage.success('更新分类成功');
        categoryDialogVisible.value = false;
        await loadCategories();
      } else {
        console.error('更新失败:', result);
        ElMessage.error(result && result.message ? result.message : '更新分类失败');
      }
    } else {
      const result = await createCategory({
        name: currentCategory.value.name,
        type: currentCategory.value.type,
        icon: currentCategory.value.icon
      });
      if (result && result.code === 200) {
        ElMessage.success('添加分类成功');
        categoryDialogVisible.value = false;
        await loadCategories();
      } else {
        console.error('创建失败:', result);
        ElMessage.error(result && result.message ? result.message : '添加分类失败');
      }
    }
  } catch (error) {
    console.error('保存分类失败:', error);
    ElMessage.error('保存分类失败');
  }
};

// 删除分类
const confirmDeleteCategory = (category: Category) => {
  ElMessageBox.confirm(
    `确定要删除分类 "${category.name}" 吗？此操作将从关联的交易中移除此分类。`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const result = await deleteCategory(category.id);
      if (result && result.code === 200) {
        ElMessage.success('删除分类成功');
        await loadCategories();
      } else {
        ElMessage.error(result && result.message ? result.message : '删除分类失败');
      }
    } catch (error) {
      console.error('删除分类失败:', error);
      ElMessage.error('删除分类失败');
    }
  }).catch(() => {
    // 用户取消操作
  });
};

// 切换分类类型标签
const switchTab = (type: 'income' | 'expense') => {
  activeTab.value = type;
  // 切换标签页时重置展开状态
  isExpanded.value = false;
};

// 展开所有分类
const expandCategories = () => {
  isExpanded.value = true;
};

// 收起分类
const collapseCategories = () => {
  isExpanded.value = false;
};

// 获取图标组件
const getIconComponent = (iconName: string) => {
  const found = iconOptions.value.find(option => option.icon === iconName);
  return found ? found.component : null;
};

onMounted(() => {
  loadCategories();
});
</script>

<template>
  <div class="category-management">
    <!-- 分类类型切换 -->
    <div class="tab-container">
      <div class="tab-wrapper">
        <div 
          class="tab-item" 
          :class="{ active: activeTab === 'expense' }" 
          @click="switchTab('expense')"
        >
          <span class="tab-text">支出分类</span>
        </div>
        <div 
          class="tab-item" 
          :class="{ active: activeTab === 'income' }" 
          @click="switchTab('income')"
        >
          <span class="tab-text">收入分类</span>
        </div>
      </div>
    </div>

    <!-- 分类网格 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>加载中...</p>
    </div>
    
    <div v-else class="categories-section">
      <div class="category-grid">
        <!-- 现有分类 -->
        <div
          v-for="category in displayedCategories"
          :key="category.id"
          class="category-card"
          @click="openEditCategoryDialog(category)"
        >
          <div class="category-icon-wrapper">
            <el-icon v-if="getIconComponent(category.icon)" class="category-icon">
              <component :is="getIconComponent(category.icon)" />
            </el-icon>
            <span v-else class="category-icon-text">{{ category.icon }}</span>
          </div>
          <span class="category-name">{{ category.name }}</span>
        </div>

        <!-- 查看全部按钮 -->
        <div
          v-if="shouldShowExpandButton"
          class="expand-category-card"
          @click="expandCategories"
        >
          <div class="expand-icon-wrapper">
            <el-icon class="expand-icon"><ArrowRight /></el-icon>
          </div>
          <span class="expand-text">查看全部</span>
        </div>

        <!-- 添加按钮 -->
        <div class="add-category-card" @click="openAddCategoryDialog(activeTab)">
          <div class="add-icon-wrapper">
            <el-icon class="add-icon"><Plus /></el-icon>
          </div>
          <span class="add-text">添加分类</span>
        </div>
      </div>

      <!-- 收起按钮 -->
      <div v-if="shouldShowCollapseButton" class="collapse-section">
        <div class="collapse-button" @click="collapseCategories">
          <span>收起</span>
          <el-icon class="collapse-icon"><ArrowRight /></el-icon>
        </div>
      </div>
    </div>

    <!-- 编辑分类弹窗 -->
    <el-dialog
      v-model="categoryDialogVisible"
      :title="isEditing ? '编辑分类' : '添加分类'"
      width="90%"
      :close-on-click-modal="false"
      custom-class="modern-category-dialog"
      destroy-on-close
    >
      <div class="category-form">
        <div class="form-item">
          <label class="form-label">分类名称</label>
          <el-input 
            v-model="currentCategory.name" 
            placeholder="请输入分类名称"
            size="large"
            class="form-input"
          />
        </div>
        
        <div class="form-item">
          <label class="form-label">选择图标</label>
          <div class="icon-selector" @click="openIconSelector">
            <div class="selected-icon">
              <el-icon v-if="getIconComponent(currentCategory.icon)">
                <component :is="getIconComponent(currentCategory.icon)" />
              </el-icon>
              <span v-else>{{ currentCategory.icon }}</span>
            </div>
            <span class="selector-text">点击选择图标</span>
            <el-icon class="arrow-icon"><ArrowRight /></el-icon>
          </div>
        </div>
        
        <!-- 删除按钮（仅编辑时显示） -->
        <div v-if="isEditing" class="danger-section">
          <div class="delete-btn" @click="confirmDeleteCategory(currentCategory)">
            <el-icon><Delete /></el-icon>
            <span>删除分类</span>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="categoryDialogVisible = false" size="large">取消</el-button>
          <el-button type="primary" @click="saveCategory" size="large">
            {{ isEditing ? '更新' : '添加' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 图标选择器弹窗 -->
    <el-dialog
      v-model="iconSelectorVisible"
      title="选择图标"
      width="90%"
      custom-class="icon-selector-dialog"
      :close-on-click-modal="true"
    >
      <div class="icon-selection-grid">
        <div 
          v-for="option in iconOptions" 
          :key="option.icon"
          class="icon-selection-item"
          @click="selectIcon(option.icon)"
        >
          <div class="icon-preview">
            <el-icon><component :is="option.component" /></el-icon>
          </div>
          <span class="icon-label">{{ option.name }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.category-management {
  padding: 0;
  background-color: transparent;
  overflow-x: hidden;

  @media (max-width: 480px) {
    padding: 0;
  }

  @media (max-width: 360px) {
    padding: 0;
  }
}



.tab-container {
  margin-bottom: 24px;
  
  .tab-wrapper {
    display: flex;
    background-color: #e5e7eb;
    border-radius: 12px;
    padding: 4px;
    position: relative;
    
    .tab-item {
      flex: 1;
      padding: 12px 16px;
      text-align: center;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      z-index: 1;
      
      .tab-text {
        font-size: 16px;
        font-weight: 500;
        color: #6b7280;
        transition: color 0.3s ease;
      }
      
      &.active {
        background-color: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        
        .tab-text {
          color: #3b82f6;
        }
      }
      
      @media (max-width: 480px) {
        padding: 14px 12px;
        
        .tab-text {
          font-size: 15px;
        }
      }
    }
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6b7280;
  
  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }
  
  p {
    margin: 0;
    font-size: 16px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.categories-section {
  .category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;

    @media (max-width: 480px) {
      grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
      gap: 8px;
      max-width: 100%;
      overflow: hidden;
    }

    @media (max-width: 360px) {
      grid-template-columns: repeat(3, 1fr);
      gap: 6px;
    }
  }
}

.category-card {
  background: white;
  border-radius: 12px;
  padding: 16px 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #3b82f6;
  }
  
  &:active {
    transform: translateY(0);
  }
  
  .category-icon-wrapper {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;

    .category-icon {
      color: white;
      font-size: 20px;
    }

    .category-icon-text {
      color: white;
      font-size: 12px;
      font-weight: 600;
    }
    
    @media (max-width: 480px) {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      margin-bottom: 6px;

      .category-icon {
        font-size: 16px;
      }

      .category-icon-text {
        font-size: 10px;
      }
    }

    @media (max-width: 360px) {
      width: 28px;
      height: 28px;
      border-radius: 6px;
      margin-bottom: 4px;

      .category-icon {
        font-size: 14px;
      }

      .category-icon-text {
        font-size: 9px;
      }
    }
  }
  
  .category-name {
    font-size: 12px;
    color: #374151;
    text-align: center;
    font-weight: 500;
    line-height: 1.3;

    @media (max-width: 480px) {
      font-size: 11px;
    }

    @media (max-width: 360px) {
      font-size: 10px;
    }
  }
  
  @media (max-width: 480px) {
    padding: 10px 4px;
    border-radius: 10px;
  }

  @media (max-width: 360px) {
    padding: 8px 3px;
    border-radius: 8px;
  }
}

.add-category-card {
  background: white;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 16px 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #3b82f6;
    background-color: #f8fafc;
    
    .add-icon-wrapper {
      background-color: #e0f2fe;
      border-color: #3b82f6;
      
      .add-icon {
        color: #3b82f6;
      }
    }
    
    .add-text {
      color: #3b82f6;
    }
  }
  
  .add-icon-wrapper {
    width: 40px;
    height: 40px;
    background-color: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    transition: all 0.3s ease;

    .add-icon {
      color: #9ca3af;
      font-size: 20px;
      transition: color 0.3s ease;
    }
    
    @media (max-width: 480px) {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      margin-bottom: 6px;

      .add-icon {
        font-size: 16px;
      }
    }

    @media (max-width: 360px) {
      width: 28px;
      height: 28px;
      border-radius: 6px;
      margin-bottom: 4px;

      .add-icon {
        font-size: 14px;
      }
    }
  }
  
  .add-text {
    font-size: 12px;
    color: #6b7280;
    text-align: center;
    font-weight: 500;
    transition: color 0.3s ease;

    @media (max-width: 480px) {
      font-size: 11px;
    }

    @media (max-width: 360px) {
      font-size: 10px;
    }
  }
  
  @media (max-width: 480px) {
    padding: 10px 4px;
    border-radius: 10px;
  }

  @media (max-width: 360px) {
    padding: 8px 3px;
    border-radius: 8px;
  }
}

// 展开按钮样式
.expand-category-card {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  border-radius: 12px;
  padding: 16px 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
  }

  &:active {
    transform: translateY(0);
  }

  .expand-icon-wrapper {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;

    .expand-icon {
      color: white;
      font-size: 20px;
      transform: rotate(90deg);
    }

    @media (max-width: 480px) {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      margin-bottom: 6px;

      .expand-icon {
        font-size: 16px;
      }
    }

    @media (max-width: 360px) {
      width: 28px;
      height: 28px;
      border-radius: 6px;
      margin-bottom: 4px;

      .expand-icon {
        font-size: 14px;
      }
    }
  }

  .expand-text {
    font-size: 12px;
    color: white;
    text-align: center;
    font-weight: 600;

    @media (max-width: 480px) {
      font-size: 11px;
    }

    @media (max-width: 360px) {
      font-size: 10px;
    }
  }

  @media (max-width: 480px) {
    padding: 10px 4px;
    border-radius: 10px;
  }

  @media (max-width: 360px) {
    padding: 8px 3px;
    border-radius: 8px;
  }
}

// 收起按钮区域
.collapse-section {
  margin-top: 16px;
  display: flex;
  justify-content: center;

  .collapse-button {
    background: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: 20px;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: #e5e7eb;
      border-color: #d1d5db;
    }

    span {
      font-size: 14px;
      color: #6b7280;
      font-weight: 500;
    }

    .collapse-icon {
      color: #6b7280;
      font-size: 14px;
      transform: rotate(-90deg);
      transition: transform 0.3s ease;
    }

    @media (max-width: 480px) {
      padding: 6px 12px;

      span {
        font-size: 12px;
      }

      .collapse-icon {
        font-size: 12px;
      }
    }
  }
}

// 编辑分类对话框样式
.modern-category-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    overflow: hidden;
    
    @media (max-width: 480px) {
      margin: 5vh auto !important;
      width: 95% !important;
    }
  }
  
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 24px;
    
    .el-dialog__title {
      font-size: 20px;
      font-weight: 600;
    }
    
    .el-dialog__close {
      color: white;
      font-size: 20px;
    }
  }
  
  :deep(.el-dialog__body) {
    padding: 24px;
  }
  
  :deep(.el-dialog__footer) {
    padding: 0 24px 24px;
    text-align: right;
  }
}

.category-form {
  .form-item {
    margin-bottom: 24px;
    
    .form-label {
      display: block;
      font-size: 16px;
      font-weight: 500;
      color: #374151;
      margin-bottom: 8px;
    }
    
    .form-input {
      :deep(.el-input__wrapper) {
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid #d1d5db;
        
        &.is-focus {
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
      }
    }
  }
  
  .icon-selector {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #f9fafb;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #3b82f6;
      background-color: #f0f9ff;
    }
    
    .selected-icon {
      width: 32px;
      height: 32px;
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      margin-right: 12px;
      font-size: 18px;
    }
    
    .selector-text {
      flex: 1;
      font-size: 16px;
      color: #374151;
    }
    
    .arrow-icon {
      color: #9ca3af;
      font-size: 16px;
    }
  }
  
  .danger-section {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #f3f4f6;
    
    .delete-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 12px 24px;
      background-color: #fef2f2;
      color: #dc2626;
      border: 1px solid #fecaca;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 16px;
      font-weight: 500;
      
      &:hover {
        background-color: #fee2e2;
        border-color: #fca5a5;
      }
      
      &:active {
        transform: scale(0.98);
      }
    }
  }
}

.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  
  :deep(.el-button) {
    padding: 10px 24px;
    border-radius: 8px;
    font-weight: 500;
  }
}

// 图标选择器对话框
.icon-selector-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    
    @media (max-width: 480px) {
      margin: 10vh auto !important;
      width: 95% !important;
    }
  }
  
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 20px 24px;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }
    
    .el-dialog__close {
      color: white;
    }
  }
  
  :deep(.el-dialog__body) {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
  }
}

.icon-selection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 16px;
  
  @media (max-width: 480px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
  }
}

.icon-selection-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  
  &:hover {
    background-color: #f0f9ff;
    border-color: #3b82f6;
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  .icon-preview {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    color: white;
    font-size: 20px;
    
    @media (max-width: 480px) {
      width: 36px;
      height: 36px;
      font-size: 18px;
    }
  }
  
  .icon-label {
    font-size: 12px;
    color: #6b7280;
    text-align: center;
    font-weight: 500;
    
    @media (max-width: 480px) {
      font-size: 11px;
    }
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .category-management {
    background-color: #111827;

    .tab-wrapper {
      background-color: #374151;
      
      .tab-item {
        .tab-text {
          color: #9ca3af;
        }
        
        &.active {
          background-color: #1f2937;
          
          .tab-text {
            color: #60a5fa;
          }
        }
      }
    }
    
    .category-card, .add-category-card {
      background-color: #1f2937;
      border-color: #374151;
      
      .category-name, .add-text {
        color: #f3f4f6;
      }
    }
    
    .add-category-card {
      border-color: #4b5563;
      
      &:hover {
        background-color: #111827;
        border-color: #60a5fa;
      }
    }
  }
}
</style> 