# 金融账本系统 - 微信小程序端开发文档

## 📱 项目概述

基于微信小程序原生开发的个人财务管理应用，与后端FastAPI服务完全对接，提供完整的记账功能。采用模块化架构设计，支持用户认证、账户管理、交易记录、数据分析等核心功能。

## 🚀 开发进度

### ✅ 第一阶段：基础架构 + 用户认证（已完成）

**开发周期**：2024年8月 - 已完成
**开发状态**：✅ 完成并测试通过

#### 核心功能
- [x] **项目基础架构搭建**
  - 微信小程序项目结构设计
  - 页面路由配置和导航系统
  - 全局样式和主题设置
  - 开发环境配置

- [x] **API适配器开发**
  - 统一的网络请求封装（基于wx.request）
  - 完整的错误处理机制
  - JWT Token自动管理
  - 请求/响应拦截器
  - 网络状态监控

- [x] **用户认证模块**
  - 用户登录页面（用户名/密码）
  - 用户注册页面（支持邮箱验证码）
  - 自动登录状态检查
  - 用户信息本地存储
  - 登录状态持久化

- [x] **基础页面框架**
  - 首页布局框架
  - 设置页面完整实现
  - 其他功能页面占位符
  - 统一的页面导航

#### 技术实现
- **开发框架**：微信小程序原生开发
- **网络通信**：wx.request + 自定义API适配器
- **数据存储**：wx.storage（本地存储）
- **状态管理**：页面级状态管理
- **UI设计**：原生组件 + 自定义样式

#### 测试验证
- ✅ 登录功能测试通过（测试账号：test/test123）
- ✅ 注册流程完整（包含邮箱验证码发送）
- ✅ 页面导航和路由正常
- ✅ API通信稳定可靠
- ✅ 错误处理机制有效

### 🚧 后续开发阶段

#### 第二阶段：仪表盘 + 账户管理
**计划开发时间**：待定
**主要功能**：
- [ ] 财务概览仪表盘
- [ ] 账户列表和管理
- [ ] 账户余额显示
- [ ] 账户添加/编辑功能

#### 第三阶段：交易记录管理
**计划开发时间**：待定
**主要功能**：
- [ ] 交易记录列表
- [ ] 添加/编辑交易
- [ ] 交易分类管理
- [ ] 交易搜索和筛选

#### 第四阶段：财务分析 + 预算管理
**计划开发时间**：待定
**主要功能**：
- [ ] 收支统计图表
- [ ] 财务趋势分析
- [ ] 预算设置和监控
- [ ] 财务报告生成

#### 第五阶段：优化和发布
**计划开发时间**：待定
**主要功能**：
- [ ] 性能优化
- [ ] 多平台适配
- [ ] 全面测试
- [ ] 发布准备

## 📁 项目结构

```
ledger-miniprogram/
├── app.js                 # 小程序入口文件
├── app.json              # 小程序全局配置
├── app.wxss              # 全局样式文件
├── pages/                # 页面目录
│   ├── index/           # 首页
│   │   ├── index.js     # 页面逻辑
│   │   ├── index.json   # 页面配置
│   │   ├── index.wxml   # 页面结构
│   │   └── index.wxss   # 页面样式
│   ├── login/           # 登录页面
│   │   ├── login.js     # 登录逻辑
│   │   ├── login.json   # 页面配置
│   │   ├── login.wxml   # 登录界面
│   │   └── login.wxss   # 登录样式
│   ├── register/        # 注册页面
│   │   ├── register.js  # 注册逻辑
│   │   ├── register.json# 页面配置
│   │   ├── register.wxml# 注册界面
│   │   └── register.wxss# 注册样式
│   ├── settings/        # 设置页面
│   │   ├── settings.js  # 设置逻辑
│   │   ├── settings.json# 页面配置
│   │   ├── settings.wxml# 设置界面
│   │   └── settings.wxss# 设置样式
│   ├── accounts/        # 账户管理（待开发）
│   ├── transactions/    # 交易记录（待开发）
│   └── analytics/       # 数据分析（待开发）
├── utils/               # 工具类目录
│   ├── api.js          # API接口封装
│   └── common.js       # 通用工具函数
├── project.config.json  # 项目配置文件
├── project.private.config.json # 私有配置
└── README.md           # 项目说明文档
```

## 🔧 开发环境配置

### 环境要求

- **微信开发者工具** 1.06.0+
- **Node.js** 14.0+
- **后端API服务** 正常运行
- **MySQL数据库** 连接正常

### 开发工具配置

#### 1. 微信开发者工具设置
```
详情 → 本地设置：
✅ 不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书
✅ 启用调试模式
✅ 启用 ES6 转 ES5
✅ 启用增强编译
```

#### 2. API服务配置
```javascript
// utils/api.js
constructor() {
  // 开发环境API地址
  this.baseURL = 'http://127.0.0.1:8000/api/v1'
}
```

#### 3. 后端服务启动
```bash
# 确保后端服务正常运行
cd ledger
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 启动步骤

1. **启动后端服务**
   ```bash
   cd ledger
   python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
   ```

2. **打开微信开发者工具**
   - 选择"小程序"
   - 导入项目：选择 `ledger-miniprogram` 目录
   - AppID：使用测试号或申请的小程序AppID

3. **编译和预览**
   - 点击"编译"按钮
   - 在模拟器中预览效果
   - 使用真机调试测试

## 🌐 API接口文档

### 用户认证接口

#### 1. 用户登录
```
POST /users/login/access-token
Content-Type: application/x-www-form-urlencoded

参数：
- username: 用户名
- password: 密码

响应：
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

#### 2. 用户注册
```
POST /users/register
Content-Type: application/json

参数：
{
  "username": "用户名",
  "email": "邮箱地址",
  "password": "密码",
  "email_verification_code": "邮箱验证码"
}

响应：
{
  "code": 0,
  "message": "注册成功！现在可以登录了",
  "data": {
    "id": 1,
    "username": "test",
    "email": "<EMAIL>",
    "email_verified": true,
    "created_at": "2024-08-11T07:00:00",
    "updated_at": "2024-08-11T07:00:00"
  }
}
```

#### 3. 发送注册验证码
```
POST /users/send-register-verification
Content-Type: application/json

参数：
{
  "email": "邮箱地址"
}

响应：
{
  "code": 0,
  "message": "验证码已发送，请检查您的邮箱",
  "data": null
}
```

#### 4. 获取用户信息
```
GET /users/me
Authorization: Bearer {access_token}

响应：
{
  "id": 1,
  "username": "test",
  "email": "<EMAIL>",
  "email_verified": true,
  "created_at": "2024-08-11T07:00:00",
  "updated_at": "2024-08-11T07:00:00"
}
```

### 测试账号

**已创建的测试账号**：
- 用户名：`test`
- 密码：`test123`
- 邮箱：`<EMAIL>`

## 💻 核心代码实现

### API适配器实现

```javascript
// utils/api.js
class ApiService {
  constructor() {
    this.baseURL = 'http://127.0.0.1:8000/api/v1'
  }

  // 通用请求方法
  request(options) {
    return new Promise((resolve, reject) => {
      const token = wx.getStorageSync('token')
      const fullUrl = this.baseURL + options.url

      console.log('🚀 发起API请求:', {
        url: fullUrl,
        method: options.method || 'GET',
        data: options.data,
        hasToken: !!token
      })

      wx.request({
        url: fullUrl,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          ...options.header
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else if (res.statusCode === 401) {
            // Token过期处理
            wx.removeStorageSync('token')
            wx.removeStorageSync('userInfo')
            wx.showToast({
              title: '登录已过期',
              icon: 'none'
            })
            setTimeout(() => {
              wx.reLaunch({
                url: '/pages/login/login'
              })
            }, 1500)
            reject(new Error('登录已过期'))
          } else {
            const errorMsg = res.data?.message || res.data?.detail || `请求失败 (${res.statusCode})`
            reject(new Error(errorMsg))
          }
        },
        fail: (error) => {
          reject(new Error('网络连接失败，请检查网络设置和后端服务'))
        }
      })
    })
  }

  // 用户认证相关API
  auth = {
    // 登录 - 使用OAuth2PasswordRequestForm格式
    login: (username, password) => {
      return this.request({
        url: '/users/login/access-token',
        method: 'POST',
        data: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
        header: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })
    },

    // 注册
    register: (userData) => {
      return this.post('/users/register', userData)
    },

    // 获取用户信息
    getUserInfo: () => {
      return this.get('/users/me')
    },

    // 发送注册验证码
    sendRegisterCode: (email) => {
      return this.post('/users/send-register-verification', { email })
    }
  }
}

// 创建全局实例
const api = new ApiService()
export default api
```

### 登录页面实现

```javascript
// pages/login/login.js
const api = require('../../utils/api.js').default

Page({
  data: {
    form: {
      username: '',
      password: ''
    },
    showPassword: false,
    loginLoading: false
  },

  async handleLogin() {
    if (!this.validateForm()) {
      return
    }

    this.setData({ loginLoading: true })

    try {
      const { username, password } = this.data.form

      // 调用登录API
      const response = await api.auth.login(username, password)

      // 保存token和用户信息
      wx.setStorageSync('token', response.access_token)

      // 获取用户信息
      const userInfo = await api.auth.getUserInfo()
      wx.setStorageSync('userInfo', userInfo)

      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })

      // 跳转到首页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        })
      }, 1500)

    } catch (error) {
      console.error('登录失败:', error)

      let errorMessage = '登录失败'
      if (error.message.includes('网络')) {
        errorMessage = '网络连接失败，请检查网络设置'
      } else if (error.message.includes('用户名') || error.message.includes('密码')) {
        errorMessage = '用户名或密码错误'
      } else if (error.message) {
        errorMessage = error.message
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      })
    } finally {
      this.setData({ loginLoading: false })
    }
  }
})
```

## 🎨 UI设计规范

### 色彩方案
- **主色调**：#007AFF（iOS蓝）
- **背景色**：#F2F2F7（浅灰）
- **文字色**：#000000（黑色）、#8E8E93（灰色）
- **错误色**：#FF3B30（红色）
- **成功色**：#34C759（绿色）

### 字体规范
- **标题**：56rpx，加粗
- **副标题**：30rpx，常规
- **正文**：34rpx，常规
- **小字**：26rpx，常规

### 间距规范
- **页面边距**：40rpx
- **组件间距**：32rpx
- **内容间距**：16rpx
- **按钮内边距**：24rpx

### 组件规范
- **按钮圆角**：16rpx
- **输入框圆角**：20rpx
- **卡片圆角**：20rpx

## 🧪 测试指南

### 功能测试清单

#### 用户认证测试
- [ ] 登录功能
  - [x] 正确用户名密码登录成功
  - [x] 错误用户名密码登录失败
  - [x] 空用户名密码验证
  - [x] 登录状态持久化
  - [x] Token过期自动跳转

- [ ] 注册功能
  - [x] 完整注册流程
  - [x] 邮箱验证码发送
  - [x] 验证码验证
  - [x] 表单验证
  - [x] 重复用户名检查

#### 页面导航测试
- [x] 页面间跳转正常
- [x] 返回按钮功能
- [x] Tab栏切换
- [x] 登录状态检查

#### API通信测试
- [x] 网络请求正常
- [x] 错误处理机制
- [x] 超时处理
- [x] Token自动携带

### 性能测试

#### 加载性能
- [x] 页面首次加载时间 < 2秒
- [x] 页面切换流畅
- [x] 网络请求响应及时

#### 内存使用
- [x] 无明显内存泄漏
- [x] 页面卸载正常
- [x] 定时器清理

## 🐛 已知问题和解决方案

### 网络连接问题
**问题**：微信开发者工具中无法访问本地API
**解决方案**：
1. 确保勾选"不校验合法域名"
2. 使用127.0.0.1而不是localhost
3. 检查防火墙设置

### Token过期处理
**问题**：Token过期后用户体验不佳
**解决方案**：
1. 自动检测401状态码
2. 清理本地存储
3. 友好提示并跳转登录

### 表单验证
**问题**：用户输入验证不够完善
**解决方案**：
1. 前端实时验证
2. 后端二次验证
3. 详细错误提示

## 📈 性能优化

### 已实现优化
- [x] **请求优化**：统一API管理，减少重复代码
- [x] **错误处理**：完善的错误捕获和用户提示
- [x] **状态管理**：合理的本地存储使用
- [x] **代码结构**：模块化设计，便于维护

### 待优化项目
- [ ] **图片优化**：压缩和懒加载
- [ ] **缓存策略**：API响应缓存
- [ ] **包大小**：代码分包和按需加载
- [ ] **用户体验**：加载状态和骨架屏

## 🔮 下一步开发计划

### 第二阶段：仪表盘 + 账户管理

#### 开发重点
1. **财务概览仪表盘**
   - 总资产显示
   - 收支趋势图表
   - 快速操作入口

2. **账户管理功能**
   - 账户列表展示
   - 添加/编辑账户
   - 账户余额实时更新
   - 账户类型管理

#### 技术准备
- 复用现有账户API接口
- 设计响应式图表组件
- 实现数据实时同步

#### 预期交付
- 完整的账户管理模块
- 基础的数据展示功能
- 良好的用户交互体验

## 📞 技术支持

### 开发团队
- **项目负责人**：[待填写]
- **前端开发**：[待填写]
- **后端开发**：[待填写]
- **UI设计**：[待填写]

### 问题反馈
- **GitHub Issues**：[项目地址]/issues
- **开发文档**：查看项目docs目录
- **API文档**：http://localhost:8000/docs

### 开发资源
- **微信小程序官方文档**：https://developers.weixin.qq.com/miniprogram/dev/
- **FastAPI文档**：https://fastapi.tiangolo.com/
- **项目后端文档**：../backend_md/readme_backend.md

---

**文档版本**：v1.0
**最后更新**：2024年8月11日
**文档状态**：第一阶段开发完成