from sqlalchemy import Column, Integer, String, Enum, ForeignKey
from sqlalchemy.orm import relationship
from app.db.base_class import Base

class Category(Base):
    __tablename__ = "categories"
 
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False)
    type = Column(Enum('income', 'expense', name='category_type'), nullable=False)
    color = Column(String(20), nullable=True, default="#3498db")  # 默认颜色
    icon = Column(String(50), nullable=True, default="ShoppingCart")  # 图标名称
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=True)  # 允许系统默认类别
    
    # 关系
    budgets = relationship("Budget", back_populates="category", cascade="all, delete-orphan")
    user = relationship("User", back_populates="categories") 