<!-- 登录页面 -->
<view class="login-page">
  <!-- 头部Logo区域 -->
  <view class="login-header">
    <view class="logo">
      <text class="logo-text">💰</text>
    </view>
    <view class="app-name">个人记账</view>
    <view class="app-desc">简洁高效的财务管理工具</view>
  </view>
  
  <!-- 登录表单 -->
  <view class="login-form">
    <view class="form-section">
      <!-- 用户名输入 -->
      <view class="form-group">
        <view class="input-row">
          <text class="input-label">用户名 *</text>
          <input
            class="form-input"
            placeholder="请输入用户名"
            value="{{form.username}}"
            bindinput="onUsernameInput"
            maxlength="50"
          />
        </view>
        <view wx:if="{{errors.username}}" class="error-text">{{errors.username}}</view>
      </view>
      
      <!-- 密码输入 -->
      <view class="form-group">
        <view class="input-row">
          <text class="input-label">密码 *</text>
          <input
            class="form-input"
            placeholder="请输入密码"
            value="{{form.password}}"
            password="{{!showPassword}}"
            bindinput="onPasswordInput"
            maxlength="50"
          />
          <text class="password-toggle" bindtap="togglePassword">
            {{showPassword ? '隐藏' : '显示'}}
          </text>
        </view>
        <view wx:if="{{errors.password}}" class="error-text">{{errors.password}}</view>
      </view>
    </view>
    
    <!-- 登录按钮 -->
    <view class="login-actions">
      <button
        class="btn-primary login-btn"
        bindtap="handleLogin"
        disabled="{{loginLoading}}"
      >
        {{loginLoading ? '登录中...' : '登录'}}
      </button>
      
      <!-- 微信登录 -->
      <button
        class="btn-secondary wechat-btn"
        bindtap="handleWechatLogin"
      >
        微信快速登录
      </button>


    </view>
    
    <!-- 底部链接 -->
    <view class="login-footer">
      <text class="link-btn" bindtap="handleForgotPassword">
        忘记密码？
      </text>
      <text class="link-btn" bindtap="goToRegister">
        还没有账号？立即注册
      </text>
    </view>
  </view>
</view>
