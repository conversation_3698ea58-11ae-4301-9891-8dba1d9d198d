from fastapi import APIRouter

from app.api.v1.endpoints.users import router as users_router
from app.api.v1.endpoints.accounts import router as accounts_router
from app.api.v1.endpoints.analytics import router as analytics_router
from app.api.v1.endpoints.transactions import router as transactions_router
from app.api.v1.endpoints.budgets import router as budgets_router
from app.api.v1.endpoints.categories import router as categories_router

api_router = APIRouter()
api_router.include_router(users_router, prefix="/users", tags=["users"])
api_router.include_router(accounts_router, prefix="/accounts", tags=["accounts"])
api_router.include_router(analytics_router, prefix="/analytics", tags=["analytics"])
api_router.include_router(transactions_router, prefix="/transactions", tags=["transactions"])
api_router.include_router(budgets_router, prefix="/budgets", tags=["budgets"])
api_router.include_router(categories_router, prefix="/categories", tags=["categories"]) 