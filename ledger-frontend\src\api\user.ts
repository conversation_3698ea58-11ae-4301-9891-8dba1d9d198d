import axios from './config'
import type { BaseResponse, LoginParams, RegisterParams, UserInfo, UserSettings } from './types'

// 用户登录 - OAuth2格式
export function login(data: FormData) {
  // 将FormData转换为URLSearchParams，适用于OAuth2
  const params = new URLSearchParams();
  params.append('username', data.get('username') as string);
  params.append('password', data.get('password') as string);
  
  // 直接返回axios的promise，拦截器已处理数据提取
  return axios.post('/users/login/access-token', params, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  });
}

// 用户注册
export function register(data: RegisterParams): Promise<any> {
  return axios.post('/users/register', data);
}

// 获取用户信息
export function getUserInfo(): Promise<UserInfo> {
  return axios.get('/users/me');
}

// 更新用户信息
export function updateUserInfo(data: Partial<UserInfo>): Promise<UserInfo> {
  return axios.put('/users/me', data);
}

// 修改密码
export function changePassword(data: { currentPassword: string; newPassword: string }): Promise<BaseResponse<null>> {
  return axios.post('/users/change-password', data);
}

// 退出登录
export function logout(): Promise<BaseResponse<null>> {
  return axios.post('/users/logout');
} 

// 获取用户设置
export function getUserSettings(): Promise<UserSettings> {
  return axios.get('/users/settings');
}

// 更新用户设置
export function updateUserSettings(data: Partial<UserSettings>): Promise<UserSettings> {
  return axios.put('/users/settings', data);
}

// 备份用户数据
export function backupUserData(): Promise<UserSettings> {
  return axios.post('/users/backup');
}

// 获取用户备份列表
export function getUserBackups(): Promise<{
  code: number;
  message: string;
  data: Array<{
    filename: string;
    path: string;
    created_at: string;
    size: number;
    size_display: string;
    timestamp: number;
  }>
}> {
  return axios.get('/users/backups');
}

// 恢复最新备份
export function restoreLatestBackup(): Promise<BaseResponse<{
  backup_info: {
    filename: string;
    path: string;
    created_at: string;
    size_display: string;
  }
}>> {
  return axios.post('/users/restore-latest');
}

// 导出用户数据
export function exportUserData(): Promise<any> {
  return axios.get('/users/export', { responseType: 'blob' });
}

// 恢复用户数据
export function restoreUserData(file: File): Promise<BaseResponse<null>> {
  const formData = new FormData();
  formData.append('file', file);
  return axios.post('/users/restore', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
} 

// 清空用户所有数据
export function clearAllUserData(): Promise<BaseResponse<null>> {
  return axios.delete('/users/clear-data');
}

// 删除用户账号
export function deleteUserAccount(): Promise<BaseResponse<null>> {
  return axios.delete('/users/account');
}

// 邮箱验证相关接口
export function sendRegisterVerificationCode(data: { email: string }): Promise<BaseResponse<null>> {
  return axios.post('/users/send-register-verification', data);
}

export function verifyEmail(data: { email: string; verification_code: string }): Promise<BaseResponse<null>> {
  return axios.post('/users/verify-email', data);
}

export function resendVerificationEmail(data: { email: string }): Promise<BaseResponse<null>> {
  return axios.post('/users/resend-verification', data);
}

export function forgotPassword(data: { email: string }): Promise<BaseResponse<null>> {
  return axios.post('/users/forgot-password', data);
}

export function resetPassword(data: { email: string; reset_code: string; new_password: string }): Promise<BaseResponse<null>> {
  return axios.post('/users/reset-password', data);
}