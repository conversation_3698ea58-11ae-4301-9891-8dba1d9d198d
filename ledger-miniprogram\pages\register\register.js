// pages/register/register.js
const api = require('../../utils/api.js')

Page({
  data: {
    form: {
      username: '',
      email: '',
      password: '',
      verificationCode: ''
    },
    errors: {},
    showPassword: false,
    registerLoading: false,
    codeSending: false,
    codeCountdown: 0,
    codeTimer: null
  },

  onLoad() {
    console.log('注册页面加载')
  },

  // 输入事件处理
  onUsernameInput(e) {
    this.setData({
      'form.username': e.detail.value
    })
  },

  onEmailInput(e) {
    this.setData({
      'form.email': e.detail.value
    })
  },

  onPasswordInput(e) {
    this.setData({
      'form.password': e.detail.value
    })
  },

  onVerificationCodeInput(e) {
    this.setData({
      'form.verificationCode': e.detail.value
    })
  },

  // 切换密码显示
  togglePassword() {
    this.setData({
      showPassword: !this.data.showPassword
    })
  },

  // 表单验证
  validateForm() {
    const { username, email, password, verificationCode } = this.data.form
    const errors = {}

    if (!username.trim()) {
      errors.username = '请输入用户名'
    } else if (username.length < 3) {
      errors.username = '用户名长度不能少于3位'
    }

    if (!email.trim()) {
      errors.email = '请输入邮箱'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      errors.email = '请输入有效的邮箱地址'
    }

    if (!verificationCode.trim()) {
      errors.verificationCode = '请输入邮箱验证码'
    } else if (verificationCode.length !== 6) {
      errors.verificationCode = '验证码必须是6位数字'
    }

    if (!password.trim()) {
      errors.password = '请输入密码'
    } else if (password.length < 6) {
      errors.password = '密码长度不能少于6位'
    }

    this.setData({ errors })
    return Object.keys(errors).length === 0
  },

  // 处理注册
  async handleRegister() {
    if (!this.validateForm()) {
      return
    }

    this.setData({ registerLoading: true })

    try {
      const { username, email, password, verificationCode } = this.data.form

      // 调用真实的注册API
      const response = await api.auth.register({
        username,
        email,
        password,
        email_verification_code: verificationCode
      })

      wx.showToast({
        title: '注册成功',
        icon: 'success'
      })

      // 跳转到登录页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)

    } catch (error) {
      console.error('注册失败:', error)

      // 显示具体错误信息
      let errorMessage = '注册失败'
      if (error.message.includes('网络')) {
        errorMessage = '网络连接失败，请检查网络设置'
      } else if (error.message.includes('用户名')) {
        errorMessage = '用户名已存在'
      } else if (error.message.includes('邮箱')) {
        errorMessage = '邮箱已被注册'
      } else if (error.message) {
        errorMessage = error.message
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      })
    } finally {
      this.setData({ registerLoading: false })
    }
  },

  // 发送验证码
  async sendVerificationCode() {
    const { email } = this.data.form

    if (!email.trim()) {
      wx.showToast({
        title: '请先输入邮箱',
        icon: 'none'
      })
      return
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      wx.showToast({
        title: '请输入有效的邮箱地址',
        icon: 'none'
      })
      return
    }

    this.setData({ codeSending: true })

    try {
      await api.auth.sendRegisterCode(email)

      wx.showToast({
        title: '验证码已发送',
        icon: 'success'
      })

      // 开始倒计时
      this.startCountdown()

    } catch (error) {
      console.error('发送验证码失败:', error)
      wx.showToast({
        title: error.message || '发送失败',
        icon: 'none'
      })
    } finally {
      this.setData({ codeSending: false })
    }
  },

  // 开始倒计时
  startCountdown() {
    this.setData({ codeCountdown: 60 })

    const timer = setInterval(() => {
      const countdown = this.data.codeCountdown - 1
      this.setData({ codeCountdown: countdown })

      if (countdown <= 0) {
        clearInterval(timer)
        this.setData({ codeTimer: null })
      }
    }, 1000)

    this.setData({ codeTimer: timer })
  },

  // 页面卸载时清理定时器
  onUnload() {
    if (this.data.codeTimer) {
      clearInterval(this.data.codeTimer)
    }
  },

  // 返回登录页
  goToLogin() {
    wx.navigateBack()
  }
})
