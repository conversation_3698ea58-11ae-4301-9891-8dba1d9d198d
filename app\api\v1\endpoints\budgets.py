from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError
from typing import List, Optional
from datetime import datetime

from app.db.session import get_db
from app.core.security import get_current_user
from app.modules.users.schemas import User
from app.modules.budgets.schemas import (
    Budget, BudgetCreate, BudgetUpdate, BudgetSummary, 
    MonthlyBudgetResponse, BudgetWithCategory
)
from app.modules.budgets import services as budget_service
from app.modules.categories import services as category_service
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/", response_model=MonthlyBudgetResponse)
async def get_monthly_budgets(
    year: Optional[int] = Query(None, description="预算年份，默认为当前年份"),
    month: Optional[int] = Query(None, description="预算月份，默认为当前月份"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户某月的所有预算及汇总数据
    """
    try:
        # 如果没有指定年份或月份，使用当前日期
        now = datetime.now()
        year = year or now.year
        month = month or now.month
        
        # 更新预算的已用金额（根据当月交易）
        await budget_service.update_budget_spent_amounts(db, current_user.id, year, month)
        
        # 获取预算汇总信息
        summary = await budget_service.get_budget_summary(db, current_user.id, year, month)
        
        # 获取详细预算列表
        budgets_data = await budget_service.get_monthly_budgets(db, current_user.id, year, month)
        
        # 转换为BudgetWithCategory对象列表
        budgets = []
        for budget_data in budgets_data:
            budget = BudgetWithCategory(
                id=budget_data["id"],
                user_id=budget_data["user_id"],
                category_id=budget_data["category_id"],
                category_name=budget_data["category_name"],
                category_color=budget_data["category_color"],
                amount=budget_data["amount"],
                spent=budget_data["spent"],
                year=budget_data["year"],
                month=budget_data["month"]
            )
            budgets.append(budget)
            
        return MonthlyBudgetResponse(summary=summary, budgets=budgets)
    
    except Exception as e:
        logger.error(f"获取预算列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取预算列表失败: {str(e)}")

@router.post("/", response_model=Budget, status_code=status.HTTP_201_CREATED)
async def create_budget(
    budget_in: BudgetCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    创建新预算
    """
    try:
        # 检查类别是否存在
        category = await category_service.get_category(db, budget_in.category_id, current_user.id)
        if not category:
            raise HTTPException(status_code=404, detail="类别不存在")
            
        # 创建预算
        budget = await budget_service.create_budget(db, budget_in, current_user.id)
        return budget
    
    except IntegrityError:
        # 唯一约束冲突，可能该月该类别已经有预算
        raise HTTPException(
            status_code=400,
            detail="该月此类别已存在预算，请更新现有预算"
        )
    except Exception as e:
        logger.error(f"创建预算失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建预算失败: {str(e)}")

@router.put("/{budget_id}", response_model=Budget)
async def update_budget(
    budget_id: int = Path(..., description="预算ID"),
    budget_in: BudgetUpdate = Body(..., description="要更新的预算信息"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    更新预算
    """
    try:
        # 检查预算是否存在且属于当前用户
        budget = await budget_service.get_budget(db, budget_id, current_user.id)
        if not budget:
            raise HTTPException(status_code=404, detail="预算不存在")
            
        # 更新预算
        updated_budget = await budget_service.update_budget(db, budget, budget_in)
        return updated_budget
    
    except Exception as e:
        logger.error(f"更新预算失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新预算失败: {str(e)}")

@router.delete("/{budget_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_budget(
    budget_id: int = Path(..., description="预算ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    删除预算
    """
    try:
        # 检查预算是否存在且属于当前用户
        budget = await budget_service.get_budget(db, budget_id, current_user.id)
        if not budget:
            raise HTTPException(status_code=404, detail="预算不存在")
            
        # 删除预算
        await budget_service.delete_budget(db, budget)
    
    except Exception as e:
        logger.error(f"删除预算失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除预算失败: {str(e)}") 