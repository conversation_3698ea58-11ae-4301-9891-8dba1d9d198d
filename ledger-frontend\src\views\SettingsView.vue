<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import AppLayout from '../layouts/AppLayout.vue'
import { ElCard, ElDivider, ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { getUserInfo, getUserSettings, clearAllUserData, deleteUserAccount } from '../api/user'
import type { UserInfo, UserSettings } from '../api/types'
import { useRouter } from 'vue-router'

// 导入组件
import UserProfilePanel from '../components/settings/UserProfilePanel.vue'
import PasswordChangePanel from '../components/settings/PasswordChangePanel.vue'
import SystemSettingsPanel from '../components/settings/SystemSettingsPanel.vue'
import DataBackupPanel from '../components/settings/DataBackupPanel.vue'
import CategoryManagementPanel from '../components/settings/CategoryManagementPanel.vue'

// 组件状态
const isLoading = ref(false)
const userInfo = ref<UserInfo | null>(null)
const userSettings = ref<Partial<UserSettings>>({
  language: 'zh',
  currency: 'CNY',
  dark_mode: false,
  notifications: true,
  auto_backup: false
})
const lastBackup = ref<string | null>(null)

// 加载用户信息和设置
onMounted(async () => {
  try {
    isLoading.value = true
    
    // 获取用户信息
    userInfo.value = await getUserInfo()
    
    // 获取用户设置
    const settings = await getUserSettings()
    userSettings.value = settings
    
    if (settings.last_backup) {
      lastBackup.value = new Date(settings.last_backup).toLocaleString()
    }
  } catch (error) {
    console.error('加载用户数据失败', error)
    ElMessage({
      type: 'error',
      message: '加载用户数据失败'
    })
  } finally {
    isLoading.value = false
  }
})

// 处理用户信息更新
const handleProfileUpdated = (updatedInfo: UserInfo) => {
  userInfo.value = updatedInfo
}

// 处理设置更新
const handleSettingsUpdated = (updatedSettings: UserSettings) => {
  userSettings.value = updatedSettings
}

// 处理备份完成
const handleBackupCompleted = (backupTime: string) => {
  if (backupTime) {
    lastBackup.value = new Date(backupTime).toLocaleString()
  }
}

// 清空所有数据
const clearAllData = () => {
  ElMessageBox.confirm(
    '此操作将清空您的所有数据，包括账户、交易、预算等，此操作不可恢复！是否继续？',
    '危险操作',
    {
      confirmButtonText: '确认清空',
      cancelButtonText: '取消',
      type: 'error',
      distinguishCancelAndClose: true,
    }
  ).then(() => {
    // 创建全屏加载
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在清空数据...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 调用API清空数据
    clearAllUserData()
      .then(response => {
        loadingInstance.close();
        ElMessage({
          type: 'success',
          message: '数据已成功清空'
        });
        
        // 短暂延迟后刷新页面
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      })
      .catch(error => {
        loadingInstance.close();
        console.error('清空数据失败:', error);
        ElMessage({
          type: 'error',
          message: '清空数据失败: ' + (error.response?.data?.detail || '未知错误')
        });
      });
  }).catch(() => {
    // 用户取消操作
  });
}

// 导入路由
const router = useRouter();

// 删除账户
const deleteAccount = () => {
  // 第一次确认
  ElMessageBox.confirm(
    '此操作将删除您的账户及所有相关数据，此操作不可恢复！是否继续？',
    '危险操作',
    {
      confirmButtonText: '确认删除',
      cancelButtonText: '取消',
      type: 'error',
      distinguishCancelAndClose: true,
    }
  ).then(() => {
    // 第二次确认，要求用户输入确认短语
    ElMessageBox.prompt(
      '请输入"DELETE"以确认删除账户',
      '二次确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        inputPattern: /^DELETE$/,
        inputErrorMessage: '请输入正确的确认短语: DELETE',
        type: 'error'
      }
    ).then(({ value }) => {
      // 创建全屏加载
      const loadingInstance = ElLoading.service({
        lock: true,
        text: '正在删除账户...',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      // 调用API删除账户
      deleteUserAccount()
        .then(response => {
          loadingInstance.close();
          ElMessage({
            type: 'success',
            message: '账户已成功删除'
          });
          
          // 清除本地认证信息
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          
          // 跳转到登录页
          setTimeout(() => {
            router.push('/login');
          }, 1500);
        })
        .catch(error => {
          loadingInstance.close();
          console.error('删除账户失败:', error);
          ElMessage({
            type: 'error',
            message: '删除账户失败: ' + (error.response?.data?.detail || '未知错误')
          });
        });
    }).catch(() => {
      // 用户取消二次确认
    });
  }).catch(() => {
    // 用户取消首次确认
  });
}
</script>

<template>
  <AppLayout>
    <h1 class="page-title">设置</h1>
    
    <div v-if="isLoading" class="loading-placeholder">
      <p>正在加载设置...</p>
    </div>
    
    <div v-else class="settings-container">
      <el-card class="settings-section" v-if="userInfo">
        <template #header>
          <div class="section-header">
            <h2>个人信息</h2>
            <span class="section-desc">更新您的个人资料信息</span>
          </div>
        </template>
        
        <UserProfilePanel 
          :user-info="userInfo" 
          @profile-updated="handleProfileUpdated" 
        />
      </el-card>
      
      <el-card class="settings-section">
        <template #header>
          <div class="section-header">
            <h2>修改密码</h2>
            <span class="section-desc">定期修改密码可以提高账户安全性</span>
          </div>
        </template>
        
        <PasswordChangePanel />
      </el-card>
      
      <el-card class="settings-section">
        <template #header>
          <div class="section-header">
            <h2>系统设置</h2>
            <span class="section-desc">自定义应用的显示和行为</span>
          </div>
        </template>
        
        <SystemSettingsPanel 
          :settings="userSettings" 
          @settings-updated="handleSettingsUpdated" 
        />
      </el-card>
      
      <el-card class="settings-section">
        <template #header>
          <div class="section-header">
            <h2>分类管理</h2>
            <span class="section-desc">管理交易分类和图标</span>
          </div>
        </template>
        
        <CategoryManagementPanel />
      </el-card>
      
      <el-card class="settings-section">
        <template #header>
          <div class="section-header">
            <h2>数据管理</h2>
            <span class="section-desc">备份、恢复和导出您的数据</span>
          </div>
        </template>
        
        <DataBackupPanel 
          :last-backup="lastBackup" 
          @backup-completed="handleBackupCompleted" 
        />
        
        <el-divider class="data-divider" />
        
        <div class="danger-zone">
          <h3>危险操作</h3>
          <p>以下操作将永久删除数据，请谨慎操作。</p>
          <div class="danger-actions">
            <el-button type="danger" @click="clearAllData">清空所有数据</el-button>
            <el-button type="danger" @click="deleteAccount">删除账户</el-button>
          </div>
        </div>
      </el-card>
    </div>
  </AppLayout>
</template>

<style lang="scss" scoped>
.page-title {
  font-weight: 600;
  color: var(--apple-dark-gray);
  font-size: 28px;
  margin-bottom: var(--spacing-lg);
  
  @media (max-width: 480px) {
    font-size: 24px;
    margin-bottom: var(--spacing-md);
  }
}

.loading-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--apple-gray);
  font-size: 16px;
}

.settings-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.settings-section {
  margin-bottom: var(--spacing-md);
  
  @media (max-width: 480px) {
    margin-bottom: var(--spacing-lg);
    
    .data-divider {
      margin: 24px 0;
    }
  }
}

.section-header {
  display: flex;
  flex-direction: column;
  
  h2 {
    margin: 0;
    font-size: 18px;
    color: var(--apple-dark-gray);
  }
  
  .section-desc {
    font-size: 14px;
    color: var(--apple-gray);
    margin-top: 4px;
  }
  
  @media (max-width: 480px) {
    h2 {
      font-size: 20px;
    }
    
    .section-desc {
      font-size: 14px;
      margin-top: 6px;
    }
  }
}

.danger-zone {
  width: 100%;
  
  h3 {
    color: var(--apple-red);
    font-size: 16px;
    margin-bottom: 8px;
  }
  
  p {
    color: var(--apple-gray);
    font-size: 14px;
    margin-bottom: var(--spacing-md);
  }
  
  .danger-actions {
    display: flex;
    gap: var(--spacing-md);
    
         @media (max-width: 480px) {
       flex-direction: column;
       gap: 12px;
       width: 100%; /* 确保容器本身是全宽 */
       
       .el-button {
         height: 44px;
         font-size: 15px;
         width: 100%;
         display: flex;
         align-items: center;
         justify-content: center;
         margin-left: 0;
         margin-right: 0;
         box-sizing: border-box;
         padding-left: 0;
         padding-right: 0;
       }
     }
  }
  
    @media (max-width: 480px) {
     margin-top: 20px;
     margin-left: 0;
     margin-right: 0;
     padding-top: 20px;
     padding-left: 0;
     padding-right: 0;
     width: 100%;
     border-top: 1px solid rgba(0, 0, 0, 0.1);
    
    h3 {
      font-size: 18px;
      margin-bottom: 12px;
    }
    
    p {
      margin-bottom: 16px;
    }
  }
}

/* 深色模式特定样式 */
:global(.dark-mode) {
  .page-title {
    color: #c9d1d9;
  }
  
  .section-header {
    h2 {
      color: #c9d1d9;
    }
    
    .section-desc {
      color: #a0a8b3;
    }
  }
  
  .danger-zone {
    p {
      color: #a0a8b3;
    }
  }
  
  .settings-section, 
  .el-card, 
  .el-card__body,
  .el-form,
  .el-form-item,
  .el-form-item__content {
    background-color: #2d2d3a !important;
    color: #c9d1d9 !important;
  }
}
</style> 