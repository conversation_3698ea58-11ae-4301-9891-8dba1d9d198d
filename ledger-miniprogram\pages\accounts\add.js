// pages/accounts/add.js
const api = require('../../utils/api.js')

Page({
  data: {
    form: {
      account_name: '',
      account_type: '',
      institution: '',
      account_number: '',
      initial_balance: '',
      currency: 'CNY'
    },
    accountTypes: [
      { value: 'bank', label: '银行账户', icon: '🏦' },
      { value: 'fund', label: '基金账户', icon: '📊' },
      { value: 'stock', label: '股票账户', icon: '📈' },
      { value: 'debt', label: '负债账户', icon: '💳' }
    ],
    currencies: ['CNY', 'USD', 'EUR', 'JPY', 'GBP', 'HKD'],
    currencyOptions: [
      { value: 'CNY', label: '人民币 (CNY)' },
      { value: 'USD', label: '美元 (USD)' },
      { value: 'EUR', label: '欧元 (EUR)' },
      { value: 'JPY', label: '日元 (JPY)' },
      { value: 'GBP', label: '英镑 (GBP)' },
      { value: 'HKD', label: '港币 (HKD)' }
    ],
    showAccountTypePicker: false,
    showCurrencyPicker: false,
    submitting: false
  },

  onLoad() {
    console.log('添加账户页面加载')
    this.updateComputedData()
  },

  // 更新计算属性
  updateComputedData() {
    const type = this.data.accountTypes.find(t => t.value === this.data.form.account_type)
    const accountTypeDisplay = type ? type.label : ''

    const { account_name, account_type } = this.data.form
    const canSubmit = account_name.trim() && account_type

    this.setData({
      accountTypeDisplay,
      canSubmit
    })
  },

  // 输入处理
  onAccountNameInput(e) {
    this.setData({
      'form.account_name': e.detail.value
    })
    this.updateComputedData()
  },

  onInstitutionInput(e) {
    this.setData({
      'form.institution': e.detail.value
    })
  },

  onAccountNumberInput(e) {
    this.setData({
      'form.account_number': e.detail.value
    })
  },

  onInitialBalanceInput(e) {
    let value = e.detail.value
    // 验证数字格式
    if (value && !/^\d*\.?\d{0,2}$/.test(value)) {
      return
    }
    this.setData({
      'form.initial_balance': value
    })
  },

  // 选择器控制
  showAccountTypePicker() {
    this.setData({ showAccountTypePicker: true })
  },

  hideAccountTypePicker() {
    this.setData({ showAccountTypePicker: false })
  },

  showCurrencyPicker() {
    this.setData({ showCurrencyPicker: true })
  },

  hideCurrencyPicker() {
    this.setData({ showCurrencyPicker: false })
  },

  stopPropagation() {
    // 阻止事件冒泡
  },

  // 选择账户类型
  selectAccountType(e) {
    const value = e.detail.value
    this.setData({
      'form.account_type': value,
      showAccountTypePicker: false
    })
    this.updateComputedData()
  },

  // 选择货币类型
  selectCurrency(e) {
    const value = e.detail.value
    this.setData({
      'form.currency': value,
      showCurrencyPicker: false
    })
  },

  // 保存账户
  async saveAccount() {
    console.log('开始保存账户，当前表单数据:', this.data.form)
    console.log('canSubmit状态:', this.data.canSubmit)

    if (!this.data.canSubmit) {
      wx.showToast({
        title: '请填写必要信息',
        icon: 'none'
      })
      return
    }

    this.setData({ submitting: true })

    try {
      const accountData = {
        account_name: this.data.form.account_name.trim(),
        account_type: this.data.form.account_type,
        institution: this.data.form.institution.trim() || null,
        account_number: this.data.form.account_number.trim() || null,
        initial_balance: parseFloat(this.data.form.initial_balance) || 0,
        currency: this.data.form.currency
      }

      console.log('准备发送的账户数据:', accountData)
      console.log('数据类型检查:', {
        account_name: typeof accountData.account_name,
        account_type: typeof accountData.account_type,
        initial_balance: typeof accountData.initial_balance,
        currency: typeof accountData.currency
      })

      const result = await api.accounts.create(accountData)
      console.log('账户创建成功，返回结果:', result)

      wx.showToast({
        title: '账户创建成功',
        icon: 'success'
      })

      // 返回账户列表页面
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)

    } catch (error) {
      console.error('创建账户失败，详细错误信息:', error)
      console.error('错误类型:', typeof error)
      console.error('错误消息:', error.message)
      console.error('错误堆栈:', error.stack)

      wx.showToast({
        title: error.message || '创建失败',
        icon: 'none',
        duration: 3000
      })
    } finally {
      this.setData({ submitting: false })
    }
  },

  // 返回
  goBack() {
    wx.navigateBack()
  }
})
