<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import AppLayout from '../layouts/AppLayout.vue'
import { ElCard, ElRow, ElCol, ElStatistic, ElSkeleton, ElSkeletonItem } from 'element-plus'
import { getDashboardData } from '@/api/analytics'
import { getTransactions } from '@/api/transaction'
import type { Account, Transaction } from '@/api/types'
import { getAccounts } from '@/api/account'
import * as echarts from 'echarts'

// 图表实例
const trendChartRef = ref<HTMLElement | null>(null)
const pieChartRef = ref<HTMLElement | null>(null)
let trendChart: echarts.ECharts | null = null
let pieChart: echarts.ECharts | null = null

// 加载状态
const loading = ref(true)

// 仪表盘数据
const dashboardData = ref({
  totalAssets: 0,
  totalLiabilities: 0,
  netWorth: 0,
  assetGrowth: 0
})

// 账户列表
const accounts = ref<Account[]>([])

// 近期交易
const recentTransactions = ref<Transaction[]>([])

// 获取仪表盘数据
const fetchDashboardData = async () => {
  try {
    loading.value = true
    // 后端直接返回数据对象，不需要检查res.code
    const data = await getDashboardData();
    if (data) {
      dashboardData.value = data;
    }
  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取账户列表
const fetchAccounts = async () => {
  try {
    // 后端直接返回数组
    const data = await getAccounts() as unknown as Account[];
    if (data) {
      accounts.value = data;
    }
  } catch (error) {
    console.error('获取账户列表失败:', error)
  }
}

// 获取近期交易
const fetchRecentTransactions = async () => {
  try {
    // 后端 /transactions/recent 端点会处理好"近期"的逻辑
    const data = await getTransactions();
    if (data) {
      recentTransactions.value = data;
    }
  } catch (error) {
    console.error('获取近期交易失败:', error);
  }
}

// 检查当前主题模式
const isDarkMode = computed(() => {
  return localStorage.getItem('darkMode') === 'true'
})

// 初始化资产变化趋势图
const initTrendChart = () => {
  if (!trendChartRef.value) {
    console.error('趋势图容器不存在');
    return;
  }
  
  console.log('正在初始化趋势图...');
  
  // 如果图表已存在，先销毁
  if (trendChart) {
    trendChart.dispose();
  }
  
  try {
    // 设置容器尺寸
    const container = trendChartRef.value;
    container.style.width = '100%';
    container.style.height = '280px';
    
    // 获取容器的尺寸
    const width = container.clientWidth;
    const height = container.clientHeight;
    console.log(`趋势图容器尺寸: ${width}x${height}`);
    
    // 创建图表实例
    trendChart = echarts.init(container);
    
    // 模拟数据 - 在实际应用中应该从后端获取
    const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月'];
    const assetData = [3000, 3200, 3400, 3300, 3500, 3800, 3910];
    
    // 深色模式颜色
    const darkMode = isDarkMode.value;
    const textColor = darkMode ? '#e2e2e6' : '#333';
    const axisLineColor = darkMode ? '#363646' : '#E4E7ED';
    const tooltipBackgroundColor = darkMode ? '#282838' : '#fff';
    const tooltipBorderColor = darkMode ? '#363646' : '#ddd';
    
    // 设置图表选项
    const option = {
      backgroundColor: darkMode ? '#282838' : '#fff',
      tooltip: {
        trigger: 'axis',
        backgroundColor: tooltipBackgroundColor,
        borderColor: tooltipBorderColor,
        textStyle: {
          color: textColor
        }
      },
      xAxis: {
        type: 'category',
        data: months,
        axisLine: {
          lineStyle: {
            color: axisLineColor
          }
        },
        axisLabel: {
          color: textColor
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value} 元',
          color: textColor
        },
        axisLine: {
          lineStyle: {
            color: axisLineColor
          }
        },
        splitLine: {
          lineStyle: {
            color: darkMode ? '#363646' : '#EBEEF5'
          }
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '3%',
        containLabel: true
      },
      series: [
        {
          name: '资产总额',
          type: 'line',
          data: assetData,
          markPoint: {
            data: [
              { type: 'max', name: '最大值' },
              { type: 'min', name: '最小值' }
            ]
          },
          lineStyle: {
            color: '#409EFF'
          },
          itemStyle: {
            color: '#409EFF'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(64, 158, 255, 0.5)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ])
          }
        }
      ]
    };
    
    // 应用选项
    trendChart.setOption(option);
    console.log('趋势图初始化成功');
  } catch (error) {
    console.error('趋势图初始化失败:', error);
  }
}

// 初始化资产分布饼图
const initPieChart = () => {
  if (!pieChartRef.value) {
    console.error('饼图容器不存在');
    return;
  }
  
  console.log('正在初始化饼图...');
  
  // 如果图表已存在，先销毁
  if (pieChart) {
    pieChart.dispose();
  }
  
  try {
    // 设置容器尺寸
    const container = pieChartRef.value;
    container.style.width = '100%';
    container.style.height = '280px';
    
    // 获取容器的尺寸
    const width = container.clientWidth;
    const height = container.clientHeight;
    console.log(`饼图容器尺寸: ${width}x${height}`);
    
    // 创建图表实例
    pieChart = echarts.init(container);
    
    // 显示所有账户，不再过滤
    const validAccounts = accounts.value;
    
    console.log('有效账户:', validAccounts);
    
    const pieData = validAccounts.map(account => ({
      name: account.account_name,
      value: Math.abs(Number(account.current_balance)),
      itemStyle: {
        // 负债账户使用不同的颜色
        color: Number(account.current_balance) < 0 ? '#F56C6C' : undefined
      }
    }));
    
    // 如果没有有效数据，显示一个空状态
    if (pieData.length === 0) {
      pieData.push({
        name: '暂无数据',
        value: 100,
        itemStyle: {
          color: isDarkMode.value ? '#444' : '#ccc'
        }
      });
    }
    
    // 深色模式颜色
    const darkMode = isDarkMode.value;
    const textColor = darkMode ? '#e2e2e6' : '#333';
    const tooltipBackgroundColor = darkMode ? '#282838' : '#fff';
    const tooltipBorderColor = darkMode ? '#363646' : '#ddd';
    
    // 设置图表选项
    const option = {
      backgroundColor: darkMode ? '#282838' : '#fff',
      tooltip: {
        trigger: 'item',
        formatter: function(params: any) {
          const value = Number(params.value);
          return `${params.name}: ${value} 元 (${params.percent}%)`;
        },
        backgroundColor: tooltipBackgroundColor,
        borderColor: tooltipBorderColor,
        textStyle: {
          color: textColor
        }
      },
      legend: {
        orient: 'vertical',
        left: '5%',
        top: 'center',
        itemWidth: 10,
        itemHeight: 10,
        textStyle: {
          fontSize: 12,
          color: textColor
        }
      },
      series: [
        {
          name: '账户余额',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['65%', '50%'],
          avoidLabelOverlap: true,
          itemStyle: {
            borderRadius: 4,
            borderColor: darkMode ? '#1e1e2e' : '#fff',
            borderWidth: 1
          },
          label: {
            show: false
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 14,
              fontWeight: 'bold',
              formatter: '{b}: {d}%',
              color: textColor
            }
          },
          labelLine: {
            show: false
          },
          data: pieData
        }
      ]
    };
    
    // 应用选项
    pieChart.setOption(option);
    console.log('饼图初始化成功');
  } catch (error) {
    console.error('饼图初始化失败:', error);
  }
}

// 重新绘制图表
const redrawCharts = () => {
  initTrendChart();
  initPieChart();
};

// 监听主题变化
const handleThemeChange = (e: any) => {
  if (e.key === 'darkMode') {
    setTimeout(redrawCharts, 200);
  }
};

// 窗口大小变化时调整图表大小
const handleResize = () => {
  trendChart?.resize();
  pieChart?.resize();
};

// 格式化金额（带有千位分隔符）
const formatAmount = (amount: number): number => {
  return amount
}

// 格式化百分比
const formatPercentage = (percentage: number) => {
  return (percentage * 100).toFixed(1) + '%'
}

// 获取账户类型名称
const getAccountTypeName = (type: string) => {
  const types: Record<string, string> = {
    'bank': '银行账户',
    'digital': '数字钱包',
    'credit': '信用卡',
    'cash': '现金',
    'investment': '投资账户'
  }
  return types[type] || '其他'
}

// 获取交易类型名称
const getTransactionTypeName = (type: string) => {
  const types: Record<string, string> = {
    'income': '收入',
    'expense': '支出',
    'transfer': '转账'
  }
  return types[type] || '未知'
}

// 获取账户名称
const getAccountName = (accountId: number) => {
  const account = accounts.value.find(a => a.id === accountId)
  return account ? account.account_name : '未知账户'
}

// 页面加载时获取数据
onMounted(async () => {
  // 获取数据
  await Promise.all([
    fetchDashboardData(),
    fetchAccounts(),
    fetchRecentTransactions()
  ]);
  
  // 标记加载完成
  loading.value = false;
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
  
  // 监听主题变化
  window.addEventListener('storage', handleThemeChange);
  
  // 确保DOM更新后再初始化图表
  await nextTick();
  
  // 延迟初始化图表，确保DOM已经渲染完成
  setTimeout(() => {
    console.log('开始初始化图表...');
    initTrendChart();
    initPieChart();
  }, 500);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('storage', handleThemeChange);
  trendChart?.dispose();
  pieChart?.dispose();
});
</script>

<template>
  <AppLayout>
    <h1 class="page-title">仪表盘</h1>
    
    <!-- 资产概览卡片 -->
    <div class="overview-cards">
      <div class="overview-card total-assets">
        <div class="card-label">总资产</div>
        <div v-if="loading" class="skeleton-value"></div>
        <div v-else class="card-value">
          <span class="currency">¥</span>{{ formatAmount(dashboardData.totalAssets) }}
        </div>
      </div>
      
      <div class="overview-card total-liabilities">
        <div class="card-label">总负债</div>
        <div v-if="loading" class="skeleton-value"></div>
        <div v-else class="card-value">
          <span class="currency">¥</span>{{ formatAmount(dashboardData.totalLiabilities) }}
        </div>
      </div>
      
      <div class="overview-card net-worth">
        <div class="card-label">净资产</div>
        <div v-if="loading" class="skeleton-value"></div>
        <div v-else class="card-value">
          <span class="currency">¥</span>{{ formatAmount(dashboardData.netWorth) }}
        </div>
        <div v-if="!loading && dashboardData.assetGrowth > 0" class="growth-indicator">
          <i class="el-icon-top"></i>
          {{ formatPercentage(dashboardData.assetGrowth) }}
        </div>
      </div>
    </div>
    
    <!-- 图表区域 -->
    <div class="dashboard-charts">
      <div class="chart-card trend-chart">
        <div class="card-header">
          <h3>资产变化趋势</h3>
        </div>
        <div v-if="loading" class="skeleton-chart"></div>
        <div v-else ref="trendChartRef" class="chart-container"></div>
      </div>
      
      <div class="chart-card pie-chart">
        <div class="card-header">
          <h3>资产分布</h3>
        </div>
        <div v-if="loading" class="skeleton-chart"></div>
        <div v-else ref="pieChartRef" class="chart-container"></div>
      </div>
    </div>
    
    <!-- 近期交易 -->
    <div class="recent-transactions-card">
      <div class="card-header">
        <h3>近期交易</h3>
        <el-button type="primary" plain size="small" @click="$router.push('/transactions')">查看全部</el-button>
      </div>
      
      <div v-if="loading" class="skeleton-list">
        <div class="skeleton-item" v-for="i in 3" :key="i"></div>
      </div>
      
      <div v-else class="transactions-list">
        <div class="transaction-item" v-for="transaction in recentTransactions" :key="transaction.id">
          <div class="transaction-badge" :class="transaction.type"></div>
          <div class="transaction-content">
            <div class="transaction-main">
              <div class="transaction-info">
                <div class="transaction-name">
                  {{ transaction.description || getTransactionTypeName(transaction.type || '') }}
                </div>
                <div class="transaction-details">
                  <span class="transaction-date">{{ transaction.date }}</span>
                  <template v-if="transaction.type === 'transfer'">
                    <span class="transaction-accounts">
                      {{ getAccountName(transaction.from_account_id || 0) }} → {{ getAccountName(transaction.to_account_id || 0) }}
                    </span>
                  </template>
                  <template v-else>
                    <span class="transaction-account">{{ getAccountName(transaction.account_id || 0) }}</span>
                  </template>
                </div>
              </div>
              <div 
                class="transaction-amount" 
                :class="transaction.type"
              >
                {{ transaction.type === 'income' ? '+' : transaction.type === 'expense' ? '-' : '' }}
                {{ formatAmount(transaction.amount) }}
              </div>
            </div>
          </div>
        </div>
        
        <div class="empty-transactions" v-if="recentTransactions.length === 0">
          <i class="el-icon-document"></i>
          <span>暂无交易记录</span>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<style lang="scss" scoped>
.page-title {
  margin-bottom: 20px;
  font-weight: 600;
  color: var(--apple-dark-gray);
  font-size: 28px;
}

/* 概览卡片 */
.overview-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
  }
}

.overview-card {
  flex: 1;
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  position: relative;
  transition: transform 0.2s, box-shadow 0.2s;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  }
  
  .card-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 12px;
  }
  
  .card-value {
    font-size: 30px;
    font-weight: 700;
    line-height: 1.2;
    color: #333;
    
    .currency {
      font-size: 20px;
      font-weight: 500;
      margin-right: 4px;
      opacity: 0.7;
    }
  }
  
  .growth-indicator {
    position: absolute;
    top: 24px;
    right: 24px;
    background: rgba(52, 199, 89, 0.1);
    color: #34C759;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 4px;
      font-size: 12px;
    }
  }
  
  .skeleton-value {
    height: 36px;
    background: #f0f0f0;
    border-radius: 6px;
    animation: pulse 1.5s ease-in-out infinite;
  }
}

/* 图表区域 */
.dashboard-charts {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.chart-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }
  
  .chart-container {
    height: 280px;
    width: 100%;
  }
  
  .skeleton-chart {
    height: 280px;
    background: linear-gradient(90deg, #f0f0f0 25%, #f8f8f8 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 8px;
  }
}

/* 近期交易卡片 */
.recent-transactions-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }
}

.skeleton-list {
  .skeleton-item {
    height: 70px;
    background: #f0f0f0;
    margin-bottom: 12px;
    border-radius: 8px;
    animation: pulse 1.5s ease-in-out infinite;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.transactions-list {
  .transaction-item {
    display: flex;
    margin-bottom: 12px;
    border-radius: 12px;
    background: #f9f9f9;
    transition: transform 0.15s;
    
    &:hover {
      transform: translateX(2px);
    }
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .transaction-badge {
      width: 6px;
      border-radius: 12px 0 0 12px;
      
      &.income {
        background-color: #34C759;
      }
      
      &.expense {
        background-color: #FF3B30;
      }
      
      &.transfer {
        background-color: #007AFF;
      }
    }
    
    .transaction-content {
      flex: 1;
      padding: 16px;
    }
    
    .transaction-main {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .transaction-info {
      flex: 1;
      
      .transaction-name {
        font-weight: 600;
        font-size: 16px;
        margin-bottom: 4px;
      }
      
      .transaction-details {
        display: flex;
        gap: 12px;
        font-size: 13px;
        color: #666;
        
        .transaction-date {
          color: #888;
        }
        
        .transaction-account,
        .transaction-accounts {
          font-weight: 500;
        }
      }
    }
    
    .transaction-amount {
      font-weight: 700;
      font-size: 18px;
      
      &.income {
        color: #34C759;
      }
      
      &.expense {
        color: #FF3B30;
      }
      
      &.transfer {
        color: #007AFF;
      }
    }
  }
}

.empty-transactions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #999;
  
  i {
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.6;
  }
  
  span {
    font-size: 15px;
  }
}

/* 动画效果 */
@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.6;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 深色模式适配 */
:global(.dark-mode) {
  .overview-card {
    background: #282838;
    
    .card-label {
      color: #aaa;
    }
    
    .card-value {
      color: #e2e2e6;
    }
    
    .skeleton-value {
      background: #363646;
    }
  }
  
  .chart-card,
  .recent-transactions-card {
    background: #282838;
    
    .card-header h3 {
      color: #e2e2e6;
    }
  }
  
  .skeleton-chart {
    background: linear-gradient(90deg, #363646 25%, #3a3a4a 50%, #363646 75%);
  }
  
  .skeleton-list .skeleton-item {
    background: #363646;
  }
  
  .transactions-list {
    .transaction-item {
      background: #323242;
      
      .transaction-info .transaction-details {
        color: #aaa;
        
        .transaction-date {
          color: #888;
        }
      }
      
      .transaction-name {
        color: #e2e2e6;
      }
    }
  }
}
</style> 