import smtplib
import random
import string
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.utils import formataddr, make_msgid, formatdate
from datetime import datetime, timedelta
from typing import Optional
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

class EmailService:
    """邮件服务类"""
    
    def __init__(self):
        self.smtp_host = settings.SMTP_HOST
        self.smtp_port = settings.SMTP_PORT
        self.smtp_username = settings.SMTP_USERNAME
        self.smtp_password = settings.SMTP_PASSWORD
        self.smtp_use_tls = settings.SMTP_USE_TLS
        self.email_from = settings.EMAIL_FROM or settings.SMTP_USERNAME
        self.email_from_name = settings.EMAIL_FROM_NAME
    
    def generate_verification_code(self) -> str:
        """生成6位数字验证码"""
        return ''.join(random.choices(string.digits, k=6))
    
    def get_verification_expiry(self) -> datetime:
        """获取验证码过期时间（15分钟后）"""
        return datetime.utcnow() + timedelta(minutes=15)
    
    async def send_email(self, to_email: str, subject: str, html_content: str) -> bool:
        """发送邮件"""
        try:
            # 检查邮件配置
            if not self.smtp_username or not self.smtp_password:
                logger.warning("邮件配置不完整，跳过发送邮件")
                return True  # 开发环境下返回True，避免阻塞流程

            logger.info(f"准备发送邮件到: {to_email}")
            logger.info(f"SMTP配置: {self.smtp_host}:{self.smtp_port}, TLS: {self.smtp_use_tls}")

            # 创建邮件
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject

            # 修复From头部格式，确保Gmail兼容性
            if self.email_from_name:
                # 使用RFC 2822标准格式
                from email.utils import formataddr
                msg['From'] = formataddr((self.email_from_name, self.email_from))
            else:
                msg['From'] = self.email_from

            msg['To'] = to_email

            # 添加Message-ID和Date头部，提高邮件兼容性
            msg['Message-ID'] = make_msgid()
            msg['Date'] = formatdate(localtime=True)

            # 添加HTML内容
            html_part = MIMEText(html_content, 'html', 'utf-8')
            msg.attach(html_part)

            # 发送邮件
            logger.info(f"正在连接SMTP服务器...")
            with smtplib.SMTP(self.smtp_host, self.smtp_port, timeout=30) as server:
                server.set_debuglevel(1)  # 启用调试模式
                logger.info(f"连接成功，启动TLS...")
                if self.smtp_use_tls:
                    server.starttls()
                logger.info(f"正在登录...")
                server.login(self.smtp_username, self.smtp_password)
                logger.info(f"登录成功，发送邮件...")
                server.send_message(msg)

            logger.info(f"邮件发送成功: {to_email}")
            return True

        except smtplib.SMTPAuthenticationError as e:
            logger.error(f"SMTP认证失败: {to_email}, 错误: {str(e)}")
            logger.error("请检查邮箱用户名和授权码是否正确")
            return False
        except smtplib.SMTPConnectError as e:
            logger.error(f"SMTP连接失败: {to_email}, 错误: {str(e)}")
            logger.error("请检查SMTP服务器地址和端口是否正确")
            return False
        except Exception as e:
            logger.error(f"邮件发送失败: {to_email}, 错误: {str(e)}")
            logger.error(f"错误类型: {type(e).__name__}")
            return False
    
    async def send_verification_email(self, to_email: str, username: str, verification_code: str) -> bool:
        """发送邮箱验证邮件"""
        subject = f"【{self.email_from_name}】邮箱验证码"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>邮箱验证</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                    <h1 style="margin: 0; font-size: 28px;">邮箱验证</h1>
                </div>
                
                <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
                    <p style="font-size: 16px; margin-bottom: 20px;">亲爱的 <strong>{username}</strong>，</p>
                    
                    <p style="font-size: 16px; margin-bottom: 20px;">
                        感谢您注册{self.email_from_name}！为了确保您的账户安全，请使用以下验证码完成邮箱验证：
                    </p>
                    
                    <div style="background: white; border: 2px dashed #667eea; padding: 20px; text-align: center; margin: 30px 0; border-radius: 8px;">
                        <span style="font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 8px;">{verification_code}</span>
                    </div>
                    
                    <p style="font-size: 14px; color: #666; margin-bottom: 20px;">
                        <strong>注意事项：</strong><br>
                        • 验证码有效期为15分钟<br>
                        • 请勿将验证码告诉他人<br>
                        • 如果您没有注册账户，请忽略此邮件
                    </p>
                    
                    <div style="border-top: 1px solid #ddd; padding-top: 20px; margin-top: 30px; text-align: center;">
                        <p style="font-size: 12px; color: #999; margin: 0;">
                            此邮件由系统自动发送，请勿回复<br>
                            © 2024 {self.email_from_name}
                        </p>
                    </div>
                </div>
            </div>
        </body>
        </html>
        """
        
        return await self.send_email(to_email, subject, html_content)
    
    async def send_password_reset_email(self, to_email: str, username: str, reset_code: str) -> bool:
        """发送密码重置邮件"""
        subject = f"【{self.email_from_name}】密码重置验证码"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>密码重置</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                    <h1 style="margin: 0; font-size: 28px;">密码重置</h1>
                </div>
                
                <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
                    <p style="font-size: 16px; margin-bottom: 20px;">亲爱的 <strong>{username}</strong>，</p>
                    
                    <p style="font-size: 16px; margin-bottom: 20px;">
                        我们收到了您的密码重置请求。请使用以下验证码重置您的密码：
                    </p>
                    
                    <div style="background: white; border: 2px dashed #f5576c; padding: 20px; text-align: center; margin: 30px 0; border-radius: 8px;">
                        <span style="font-size: 32px; font-weight: bold; color: #f5576c; letter-spacing: 8px;">{reset_code}</span>
                    </div>
                    
                    <p style="font-size: 14px; color: #666; margin-bottom: 20px;">
                        <strong>注意事项：</strong><br>
                        • 验证码有效期为15分钟<br>
                        • 请勿将验证码告诉他人<br>
                        • 如果您没有申请密码重置，请忽略此邮件
                    </p>
                    
                    <div style="border-top: 1px solid #ddd; padding-top: 20px; margin-top: 30px; text-align: center;">
                        <p style="font-size: 12px; color: #999; margin: 0;">
                            此邮件由系统自动发送，请勿回复<br>
                            © 2024 {self.email_from_name}
                        </p>
                    </div>
                </div>
            </div>
        </body>
        </html>
        """
        
        return await self.send_email(to_email, subject, html_content)

    async def send_register_verification_email(self, to_email: str, verification_code: str) -> bool:
        """发送注册验证邮件"""
        subject = f"【{self.email_from_name}】注册验证码"

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>注册验证</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                    <h1 style="margin: 0; font-size: 28px;">注册验证</h1>
                </div>

                <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
                    <p style="font-size: 16px; margin-bottom: 20px;">您好！</p>

                    <p style="font-size: 16px; margin-bottom: 20px;">
                        感谢您注册{self.email_from_name}！请使用以下验证码完成邮箱验证：
                    </p>

                    <div style="background: white; border: 2px dashed #667eea; padding: 20px; text-align: center; margin: 30px 0; border-radius: 8px;">
                        <span style="font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 8px;">{verification_code}</span>
                    </div>

                    <p style="font-size: 14px; color: #666; margin-bottom: 20px;">
                        <strong>注意事项：</strong><br>
                        • 验证码有效期为15分钟<br>
                        • 请勿将验证码告诉他人<br>
                        • 如果您没有注册账户，请忽略此邮件
                    </p>

                    <div style="border-top: 1px solid #ddd; padding-top: 20px; margin-top: 30px; text-align: center;">
                        <p style="font-size: 12px; color: #999; margin: 0;">
                            此邮件由系统自动发送，请勿回复<br>
                            © 2024 {self.email_from_name}
                        </p>
                    </div>
                </div>
            </div>
        </body>
        </html>
        """

        return await self.send_email(to_email, subject, html_content)

# 创建全局邮件服务实例
email_service = EmailService()
