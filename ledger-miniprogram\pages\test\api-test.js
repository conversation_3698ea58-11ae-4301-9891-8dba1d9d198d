// pages/test/api-test.js
const api = require('../../utils/api.js')

Page({
  data: {
    logs: []
  },

  onLoad() {
    this.addLog('API测试页面加载完成')
  },

  addLog(message) {
    const timestamp = new Date().toLocaleTimeString()
    const logMessage = `[${timestamp}] ${message}`
    
    this.setData({
      logs: [logMessage, ...this.data.logs].slice(0, 20) // 只保留最近20条日志
    })
    
    console.log(logMessage)
  },

  // 测试创建账户
  async testCreateAccount() {
    this.addLog('开始测试创建账户...')

    // 首先检查登录状态
    const token = wx.getStorageSync('token')
    this.addLog(`当前Token: ${token ? '已设置' : '未设置'}`)

    if (!token) {
      this.addLog('错误：用户未登录')
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    try {
      const testAccountData = {
        account_name: '测试银行账户',
        account_type: 'bank',
        institution: '测试银行',
        account_number: '**********',
        initial_balance: 1000.00,
        currency: 'CNY'
      }

      this.addLog(`发送数据: ${JSON.stringify(testAccountData)}`)
      this.addLog(`数据类型检查: initial_balance=${typeof testAccountData.initial_balance}`)

      const result = await api.accounts.create(testAccountData)

      this.addLog(`创建成功: ${JSON.stringify(result)}`)

      wx.showToast({
        title: '测试账户创建成功',
        icon: 'success'
      })

    } catch (error) {
      this.addLog(`创建失败: ${error.message}`)
      this.addLog(`错误详情: ${JSON.stringify(error)}`)
      console.error('测试创建账户失败:', error)

      wx.showToast({
        title: '创建失败',
        icon: 'none'
      })
    }
  },

  // 测试获取账户列表
  async testGetAccounts() {
    this.addLog('开始测试获取账户列表...')
    
    try {
      const accounts = await api.accounts.getList()
      this.addLog(`获取成功，账户数量: ${accounts.length}`)
      this.addLog(`账户数据: ${JSON.stringify(accounts)}`)
      
      wx.showToast({
        title: '获取账户列表成功',
        icon: 'success'
      })
      
    } catch (error) {
      this.addLog(`获取失败: ${error.message}`)
      console.error('测试获取账户列表失败:', error)
      
      wx.showToast({
        title: '获取失败',
        icon: 'none'
      })
    }
  },

  // 测试获取仪表盘
  async testGetDashboard() {
    this.addLog('开始测试获取仪表盘...')
    
    try {
      const dashboard = await api.analytics.getDashboard()
      this.addLog(`仪表盘数据: ${JSON.stringify(dashboard)}`)
      
      wx.showToast({
        title: '获取仪表盘成功',
        icon: 'success'
      })
      
    } catch (error) {
      this.addLog(`获取失败: ${error.message}`)
      console.error('测试获取仪表盘失败:', error)
      
      wx.showToast({
        title: '获取失败',
        icon: 'none'
      })
    }
  }
})
