/* 
 * 移动端适配样式
 * 专门处理在小屏幕设备上 Element Plus 组件的显示
 */

/* 基础响应式工具类 */
.hidden-xs {
  @media (max-width: 768px) {
    display: none !important;
  }
}

.visible-xs {
  display: none !important;
  @media (max-width: 768px) {
    display: block !important;
  }
}

.visible-xs-inline {
  display: none !important;
  @media (max-width: 768px) {
    display: inline !important;
  }
}

.visible-xs-flex {
  display: none !important;
  @media (max-width: 768px) {
    display: flex !important;
  }
}

/* 表格组件移动端适配 */
@media (max-width: 768px) {
  .el-table {
    width: 100%;
    overflow-x: auto;
    
    /* 确保表格可以横向滚动 */
    .el-table__body, .el-table__header {
      min-width: 100%;
    }
    
    /* 紧凑显示 */
    .el-table__cell {
      padding: 8px !important;
    }
    
    .cell {
      padding-left: 8px !important;
      padding-right: 8px !important;
      line-height: 1.3;
    }
  }
  
  /* 响应式隐藏非关键列 */
  .el-table__column-mobile-hidden {
    display: none !important;
  }
}

/* 表单组件移动端适配 */
@media (max-width: 768px) {
  .el-form--inline .el-form-item {
    margin-right: 0;
    width: 100%;
    display: block;
  }

  .el-form-item {
    margin-bottom: 15px;
  }



  .el-form-item__label {
    float: none;
    display: block;
    text-align: left;
    padding: 0 0 8px;
  }

  .el-form-item__content {
    margin-left: 0 !important;
  }

  /* 日期选择器全宽 */
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 100%;
  }

  /* 选择器全宽 */
  .el-select {
    width: 100%;
  }

  /* 表单操作按钮统一样式 */
  .form-actions {
    gap: 12px !important;

    .el-button {
      flex: 1 !important;
      height: 44px !important;
      font-size: 16px !important;
      border-radius: 8px !important;
      margin: 0 !important;
      touch-action: manipulation !important;
    }
  }
}

/* 弹窗组件移动端适配 */
@media (max-width: 768px) {
  .el-dialog {
    width: 90% !important;
    max-width: none !important;
    margin-top: 5vh !important;
    max-height: 90vh !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
  }
  
  .el-dialog .el-dialog__wrapper {
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
  }
  
  .el-dialog__header {
    padding: 15px 15px 10px;
    flex-shrink: 0 !important;
  }
  
  .el-dialog__body {
    padding: 15px !important;
    flex: 1 !important;
    overflow-y: auto !important;
    min-height: 0 !important;
  }
  
  .el-dialog__footer {
    padding: 15px 15px 20px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    flex-wrap: wrap !important;
    gap: 12px !important;
    border-top: 1px solid #f0f0f0 !important;
    background-color: #fafafa !important;
    margin: 0 !important;
    border-radius: 0 0 8px 8px !important;
    flex-shrink: 0 !important;
  }
  
  .el-dialog__footer .el-button {
    flex: 1 !important;
    min-width: 0 !important;
    height: 48px !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    border-radius: 8px !important;
    touch-action: manipulation !important;
    border-width: 1.5px !important;
  }
  
  .el-dialog__footer .dialog-footer {
    width: 100% !important;
    display: flex !important;
    gap: 12px !important;
  }
  
  /* 确保对话框中的span.dialog-footer也正确显示 */
  .el-dialog__footer span.dialog-footer {
    width: 100% !important;
    display: flex !important;
    gap: 12px !important;
    justify-content: space-between !important;
  }
  
  /* 确保所有对话框按钮都有正确的样式 */
  .el-dialog__footer .el-button[type="button"] {
    flex: 1 !important;
    height: 48px !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    border-radius: 8px !important;
    touch-action: manipulation !important;
    min-width: 100px !important;
  }
  
  /* 强制显示所有对话框footer */
  .responsive-dialog .el-dialog__footer {
    position: relative !important;
    bottom: 0 !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 9999 !important;
  }
  
     /* 最强制的footer显示 - 针对添加账户对话框 */
   .el-dialog[aria-label*="添加"] .el-dialog__footer,
   .el-dialog .el-dialog__footer,
   .add-account-dialog .el-dialog__footer {
     display: flex !important;
     visibility: visible !important;
     opacity: 1 !important;
     height: auto !important;
     min-height: 60px !important;
     position: relative !important;
     bottom: auto !important;
     left: auto !important;
     right: auto !important;
     transform: none !important;
   }
   
   /* 特别针对添加账户对话框 */
   .add-account-dialog {
     max-height: 90vh !important;
   }
   
   .add-account-dialog .el-dialog__body {
     max-height: calc(90vh - 120px) !important;
     overflow-y: auto !important;
   }
   
   .add-account-dialog .el-dialog__footer {
     position: sticky !important;
     bottom: 0 !important;
     background: white !important;
     border-top: 1px solid #eee !important;
     z-index: 1000 !important;
   }
}

/* 下拉菜单移动端适配 */
@media (max-width: 768px) {
  .el-dropdown-menu {
    min-width: 120px !important;
  }
}

/* 菜单组件移动端适配 */
@media (max-width: 768px) {
  .el-menu--popup {
    min-width: 180px;
  }
  
  .el-menu-item, .el-submenu__title {
    height: 50px;
    line-height: 50px;
  }
}

/* 卡片组件移动端适配 */
@media (max-width: 768px) {
  .el-card {
    margin-bottom: 15px;
  }
  
  .el-card__header {
    padding: 15px;
  }
  
  .el-card__body {
    padding: 15px;
  }
}

/* 分页组件移动端适配 */
@media (max-width: 768px) {
  .el-pagination {
    white-space: normal !important;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    
    .btn-prev, 
    .btn-next, 
    .el-pager li {
      margin: 5px 3px;
    }
    
    .el-pagination__sizes {
      display: none !important;
    }
  }
}

/* 标签页组件移动端适配 */
@media (max-width: 768px) {
  .el-tabs__header {
    margin-bottom: 15px;
  }
  
  .el-tabs__item {
    padding: 0 10px !important;
  }
}

/* 步骤条移动端适配 */
@media (max-width: 768px) {
  .el-steps {
    overflow-x: auto;
  }
  
  .el-step__title {
    font-size: 12px !important;
  }
  
  .el-step__description {
    font-size: 10px !important;
  }
}

/* 触摸优化 */
@media (hover: none) and (pointer: coarse) {
  /* 按钮更大的触摸区域 */
  .el-button {
    min-height: 40px;
  }
  
  /* 表单元素更易点击 */
  .el-radio__inner, 
  .el-checkbox__inner {
    transform: scale(1.2);
  }
} 
    
    /* 美化对话框footer按钮样式 - 确保覆盖所有可能的footer选择器 */
    .el-dialog__footer,
    [class*="dialog"] .el-dialog__footer,
    .el-dialog .el-dialog__footer,
    .el-dialog__wrapper .el-dialog__footer {
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
      height: auto !important;
      position: relative !important;
      background-color: #fff !important;
      border-top: 1px solid #f0f0f0 !important;
      padding: 12px 20px !important;
      margin: 5px 0 0 0 !important;
      z-index: 1001 !important;
    }
    
    /* dialog-footer容器样式 */
    .dialog-footer,
    .el-dialog__footer .dialog-footer,
    span.dialog-footer {
      display: flex !important;
      flex-direction: row !important;
      gap: 12px !important;
      width: 100% !important;
      align-items: center !important;
      justify-content: flex-end !important;
      visibility: visible !important;
      opacity: 1 !important;
      margin-top: 0 !important;
    }
    
    /* 移动端按钮样式优化 */
    .dialog-footer .el-button,
    .el-dialog__footer .el-button,
    span.dialog-footer .el-button {
      flex: 1 !important;
      height: 48px !important;
      font-size: 16px !important;
      font-weight: 500 !important;
      border-radius: 8px !important;
      border: none !important;
      transition: all 0.2s ease !important;
      touch-action: manipulation !important;
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
      visibility: visible !important;
      opacity: 1 !important;
      position: relative !important;
      margin: 0 !important;
      white-space: nowrap !important;
    }
    
    /* 取消按钮样式 */
    .dialog-footer .el-button:not(.el-button--primary),
    .el-dialog__footer .el-button:not(.el-button--primary),
    span.dialog-footer .el-button:not(.el-button--primary) {
      background-color: #f5f5f5 !important;
      color: #333 !important;
    }
    
    .dialog-footer .el-button:not(.el-button--primary):active,
    .el-dialog__footer .el-button:not(.el-button--primary):active,
    span.dialog-footer .el-button:not(.el-button--primary):active {
      background-color: #e8e8e8 !important;
    }
    
    /* 主要按钮（添加/确定）样式 */
    .dialog-footer .el-button--primary,
    .el-dialog__footer .el-button--primary,
    span.dialog-footer .el-button--primary {
      background-color: #007AFF !important;
      color: white !important;
    }
    
    .dialog-footer .el-button--primary:active,
    .el-dialog__footer .el-button--primary:active,
    span.dialog-footer .el-button--primary:active {
      background-color: #0062CC !important;
    }
    
    /* 深色模式适配 */
    .dark-mode .el-dialog__footer {
      background-color: #1c1c1e !important;
      border-top-color: #363646 !important;
    }
    
    .dark-mode .dialog-footer .el-button:not(.el-button--primary) {
      background-color: #3a3a3c !important;
      color: #e2e2e6 !important;
    }
    
    .dark-mode .dialog-footer .el-button:not(.el-button--primary):active {
      background-color: #48484a !important;
    }
    
    /* 最强制的显示规则 - 确保对话框底部按钮永远显示 */
    @media (max-width: 768px) {
      .el-dialog .el-dialog__footer,
      .el-dialog [class*="footer"],
      .el-dialog__footer,
      .responsive-dialog .el-dialog__footer {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        bottom: auto !important;
        left: auto !important;
        right: auto !important;
        transform: none !important;
        height: auto !important;
        min-height: 60px !important;
        padding: 12px 20px !important;
        margin: 5px 0 0 0 !important;
        background: white !important;
        border-top: 1px solid #f0f0f0 !important;
        z-index: 1001 !important;
      }
      
      .el-dialog .el-dialog__footer *,
      .el-dialog__footer *,
      .responsive-dialog .el-dialog__footer * {
        visibility: visible !important;
        opacity: 1 !important;
        display: initial !important;
      }
      
      .el-dialog .el-dialog__footer .el-button,
      .el-dialog__footer .el-button,
      .responsive-dialog .el-dialog__footer .el-button {
        display: inline-flex !important;
        visibility: visible !important;
        opacity: 1 !important;
      }
    } 

/* 移动端交易对话框专用样式 */
@media (max-width: 768px) {
  /* 对话框整体样式 */
  .mobile-transaction-dialog .el-dialog {
    border-radius: 16px !important;
    margin: 0 !important;
    max-height: 90vh !important;
    overflow: hidden !important;
  }
  
  .mobile-transaction-dialog .el-dialog__header {
    padding: 16px 20px 12px !important;
    border-bottom: 1px solid #f0f0f0 !important;
    background: #fff !important;
  }
  
  .mobile-transaction-dialog .el-dialog__title {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #333 !important;
  }
  
  .mobile-transaction-dialog .el-dialog__headerbtn {
    top: 16px !important;
    right: 16px !important;
    width: 32px !important;
    height: 32px !important;
  }
  
  .mobile-transaction-dialog .el-dialog__body {
    padding: 16px 20px 0 !important;
    max-height: calc(90vh - 140px) !important;
    overflow-y: auto !important;
  }
  
  /* 表单项紧凑布局 */
  .mobile-transaction-dialog .el-form-item {
    margin-bottom: 12px !important;
  }
  
  .mobile-transaction-dialog .el-form-item:last-of-type {
    margin-bottom: 6px !important;
  }
  
  .mobile-transaction-dialog .el-form-item__label {
    padding-bottom: 6px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    color: #333 !important;
    line-height: 1.2 !important;
  }
  
  .mobile-transaction-dialog .el-form-item__content {
    margin-left: 0 !important;
  }
  
  /* 输入框样式优化 */
  .mobile-transaction-dialog .el-input__wrapper,
  .mobile-transaction-dialog .el-select .el-input__wrapper,
  .mobile-transaction-dialog .el-textarea__inner {
    border-radius: 8px !important;
    border: 1px solid #e0e0e0 !important;
    box-shadow: none !important;
    transition: border-color 0.2s !important;
  }
  
  .mobile-transaction-dialog .el-input__wrapper:hover,
  .mobile-transaction-dialog .el-select .el-input__wrapper:hover {
    border-color: #007AFF !important;
  }
  
  .mobile-transaction-dialog .el-input__wrapper.is-focus,
  .mobile-transaction-dialog .el-select .el-input__wrapper.is-focus {
    border-color: #007AFF !important;
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1) !important;
  }
  
  .mobile-transaction-dialog .el-input__inner,
  .mobile-transaction-dialog .el-textarea__inner {
    height: 42px !important;
    font-size: 15px !important;
    padding: 0 12px !important;
  }
  
  .mobile-transaction-dialog .el-textarea__inner {
    height: auto !important;
    min-height: 42px !important;
    padding: 10px 12px !important;
    resize: none !important;
  }
  
  /* 选择器样式 */
  .mobile-transaction-dialog .el-select {
    width: 100% !important;
  }
  
  .mobile-transaction-dialog .el-select .el-input__inner {
    height: 42px !important;
    line-height: 42px !important;
  }
  
  /* 日期选择器样式 */
  .mobile-transaction-dialog .el-date-editor {
    width: 100% !important;
  }
  
  .mobile-transaction-dialog .el-date-editor .el-input__inner {
    height: 42px !important;
    padding: 0 12px !important;
  }
  
  /* 账户选择项样式 */
  .mobile-transaction-dialog .account-option {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100% !important;
  }
  
  .mobile-transaction-dialog .account-option .account-type {
    font-size: 12px !important;
    color: #999 !important;
    background: #f5f5f5 !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
  }
  
  /* 分类选择项样式 */
  .mobile-transaction-dialog .category-option {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100% !important;
  }
  
  .mobile-transaction-dialog .category-icon {
    color: #666 !important;
  }
  
  /* 底部操作按钮区域 */
  .mobile-transaction-dialog .dialog-bottom-actions {
    position: sticky !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background: #fff !important;
    border-top: 1px solid #f0f0f0 !important;
    padding: 12px 20px !important;
    margin: 16px -20px 0 !important;
    display: flex !important;
    gap: 12px !important;
    z-index: 100 !important;
  }
  
  .mobile-transaction-dialog .action-button {
    flex: 1 !important;
    height: 44px !important;
    border-radius: 8px !important;
    border: none !important;
    font-size: 15px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.2s !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
  
  .mobile-transaction-dialog .cancel-btn {
    background: #f5f5f5 !important;
    color: #666 !important;
  }
  
  .mobile-transaction-dialog .cancel-btn:active {
    background: #e8e8e8 !important;
  }
  
  .mobile-transaction-dialog .confirm-btn {
    background: #007AFF !important;
    color: white !important;
  }
  
  .mobile-transaction-dialog .confirm-btn:active {
    background: #0056CC !important;
  }
  
  .mobile-transaction-dialog .confirm-btn.disabled {
    background: #ccc !important;
    color: #999 !important;
    cursor: not-allowed !important;
  }
  
  /* 特殊字段样式 */
  .mobile-transaction-dialog .highlight-form-item .el-form-item__label {
    color: #007AFF !important;
    font-weight: 600 !important;
  }
  
  /* 滚动条样式 */
  .mobile-transaction-dialog .el-dialog__body::-webkit-scrollbar {
    width: 4px !important;
  }
  
  .mobile-transaction-dialog .el-dialog__body::-webkit-scrollbar-track {
    background: transparent !important;
  }
  
  .mobile-transaction-dialog .el-dialog__body::-webkit-scrollbar-thumb {
    background: #ddd !important;
    border-radius: 2px !important;
  }
  
  .mobile-transaction-dialog .el-dialog__body::-webkit-scrollbar-thumb:hover {
    background: #bbb !important;
  }
} 

/* 移动端添加账户对话框专用样式 */
@media (max-width: 768px) {
  /* 账户对话框整体样式 */
  .mobile-account-dialog .el-dialog {
    border-radius: 16px !important;
    margin: 0 !important;
    max-height: 90vh !important;
    overflow: hidden !important;
  }
  
  .mobile-account-dialog .el-dialog__header {
    padding: 16px 20px 12px !important;
    border-bottom: 1px solid #f0f0f0 !important;
    background: #fff !important;
  }
  
  .mobile-account-dialog .el-dialog__title {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #333 !important;
  }
  
  .mobile-account-dialog .el-dialog__headerbtn {
    top: 16px !important;
    right: 16px !important;
    width: 32px !important;
    height: 32px !important;
  }
  
  .mobile-account-dialog .el-dialog__body {
    padding: 16px 20px 0 !important;
    max-height: calc(90vh - 140px) !important;
    overflow-y: auto !important;
  }
  
  /* 表单项紧凑布局 */
  .mobile-account-dialog .el-form-item {
    margin-bottom: 12px !important;
  }
  
  .mobile-account-dialog .el-form-item:last-of-type {
    margin-bottom: 6px !important;
  }
  
  .mobile-account-dialog .el-form-item__label {
    padding-bottom: 6px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    color: #333 !important;
    line-height: 1.2 !important;
  }
  
  .mobile-account-dialog .el-form-item__content {
    margin-left: 0 !important;
  }
  
  /* 输入框样式优化 */
  .mobile-account-dialog .el-input__wrapper,
  .mobile-account-dialog .el-select .el-input__wrapper {
    border-radius: 8px !important;
    border: 1px solid #e0e0e0 !important;
    box-shadow: none !important;
    transition: border-color 0.2s !important;
  }
  
  .mobile-account-dialog .el-input__wrapper:hover,
  .mobile-account-dialog .el-select .el-input__wrapper:hover {
    border-color: #007AFF !important;
  }
  
  .mobile-account-dialog .el-input__wrapper.is-focus,
  .mobile-account-dialog .el-select .el-input__wrapper.is-focus {
    border-color: #007AFF !important;
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1) !important;
  }
  
  .mobile-account-dialog .el-input__inner {
    height: 42px !important;
    font-size: 15px !important;
    padding: 0 12px !important;
  }
  
  /* 选择器样式 */
  .mobile-account-dialog .el-select {
    width: 100% !important;
  }
  
  .mobile-account-dialog .el-select .el-input__inner {
    height: 42px !important;
    line-height: 42px !important;
  }
  
  /* 账户类型选择项样式 */
  .mobile-account-dialog .account-type-option {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    width: 100% !important;
  }
  
  .mobile-account-dialog .type-icon {
    font-size: 16px !important;
  }
  
  .mobile-account-dialog .type-name {
    font-size: 14px !important;
  }
  
  /* 底部操作按钮区域 */
  .mobile-account-dialog .dialog-bottom-actions {
    position: sticky !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background: #fff !important;
    border-top: 1px solid #f0f0f0 !important;
    padding: 12px 20px !important;
    margin: 16px -20px 0 !important;
    display: flex !important;
    gap: 12px !important;
    z-index: 100 !important;
  }
  
  .mobile-account-dialog .action-button {
    flex: 1 !important;
    height: 44px !important;
    border-radius: 8px !important;
    border: none !important;
    font-size: 15px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.2s !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
  
  .mobile-account-dialog .cancel-btn {
    background: #f5f5f5 !important;
    color: #666 !important;
  }
  
  .mobile-account-dialog .cancel-btn:active {
    background: #e8e8e8 !important;
  }
  
  .mobile-account-dialog .confirm-btn {
    background: #007AFF !important;
    color: white !important;
  }
  
  .mobile-account-dialog .confirm-btn:active {
    background: #0056CC !important;
  }
  
  .mobile-account-dialog .confirm-btn.disabled {
    background: #ccc !important;
    color: #999 !important;
    cursor: not-allowed !important;
  }
  
  /* 滚动条样式 */
  .mobile-account-dialog .el-dialog__body::-webkit-scrollbar {
    width: 4px !important;
  }
  
  .mobile-account-dialog .el-dialog__body::-webkit-scrollbar-track {
    background: transparent !important;
  }
  
  .mobile-account-dialog .el-dialog__body::-webkit-scrollbar-thumb {
    background: #ddd !important;
    border-radius: 2px !important;
  }
  
  .mobile-account-dialog .el-dialog__body::-webkit-scrollbar-thumb:hover {
    background: #bbb !important;
  }
} 