from pydantic import BaseModel, Field, validator
from typing import Optional, List
from decimal import Decimal
from datetime import date

# 基础预算模型
class BudgetBase(BaseModel):
    amount: Decimal = Field(..., description="预算金额")
    year: int = Field(..., description="预算年份")
    month: int = Field(..., description="预算月份")
    
    @validator('month')
    def month_must_be_valid(cls, v):
        if v < 1 or v > 12:
            raise ValueError('月份必须在1到12之间')
        return v
    
    @validator('year')
    def year_must_be_valid(cls, v):
        if v < 2000 or v > 2100:
            raise ValueError('年份必须在2000到2100之间')
        return v

# 创建预算请求
class BudgetCreate(BudgetBase):
    category_id: int = Field(..., description="预算类别ID")

# 更新预算请求
class BudgetUpdate(BaseModel):
    amount: Optional[Decimal] = Field(None, description="预算金额")

# 预算返回模型
class Budget(BudgetBase):
    id: int
    category_id: int
    user_id: int
    spent: Decimal = Field(default=0, description="已使用金额")
    
    class Config:
        from_attributes = True
        
    def remaining(self) -> Decimal:
        """计算剩余预算"""
        return self.amount - self.spent
    
    def percentage_used(self) -> float:
        """计算预算使用百分比"""
        if self.amount > 0:
            return float((self.spent / self.amount) * 100)
        return 0.0

# 预算汇总信息
class BudgetSummary(BaseModel):
    total_budget: Decimal = Field(..., description="总预算")
    total_spent: Decimal = Field(..., description="总支出")
    remaining: Decimal = Field(..., description="剩余预算")
    usage_percentage: float = Field(..., description="预算使用百分比")

# 带类别名称的详细预算信息
class BudgetWithCategory(Budget):
    category_name: str = Field(..., description="类别名称")
    category_color: str = Field(..., description="类别颜色")

# 预算月度汇总响应
class MonthlyBudgetResponse(BaseModel):
    summary: BudgetSummary
    budgets: List[BudgetWithCategory] 