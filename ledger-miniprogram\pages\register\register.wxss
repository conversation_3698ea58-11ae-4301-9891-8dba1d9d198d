/* pages/register/register.wxss */
.register-page {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding: 40rpx;
}

.register-header {
  text-align: center;
  padding: 80rpx 0 60rpx;
}

.title {
  font-size: 56rpx;
  font-weight: bold;
  color: #000000;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 30rpx;
  color: #8E8E93;
}

.form-section {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 40rpx;
}

.input-row {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.input-row:last-child {
  border-bottom: none;
}

.input-label {
  font-size: 34rpx;
  color: #000000;
  margin-right: 32rpx;
  min-width: 120rpx;
}

.form-input {
  flex: 1;
  font-size: 34rpx;
  color: #000000;
}

.password-toggle {
  color: #007AFF;
  font-size: 28rpx;
  padding: 0;
  background: none;
  border: none;
}

.register-actions {
  margin-bottom: 40rpx;
}

.register-footer {
  text-align: center;
}

.link-btn {
  background: none;
  border: none;
  color: #007AFF;
  font-size: 26rpx;
  margin: 16rpx 0;
}

.verification-input {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.verification-code {
  flex: 1;
}

.send-code-btn {
  padding: 16rpx 24rpx;
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 26rpx;
  white-space: nowrap;
  min-width: 160rpx;
}

.send-code-btn.disabled {
  background-color: #C7C7CC;
  color: #8E8E93;
}
