from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from passlib.context import CryptContext
from jose import JWTError, jwt
from datetime import datetime, timedelta
from typing import Optional
import logging
from sqlalchemy import and_ # Added for the new_code

from . import models, schemas
from app.core.config import settings
from app.services.email_service import email_service
from app.services.verification_service import VerificationService

# 设置日志
logger = logging.getLogger(__name__)

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class UserService:
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        return pwd_context.verify(plain_password, hashed_password)

    @staticmethod
    def get_password_hash(password: str) -> str:
        return pwd_context.hash(password)

    @staticmethod
    async def get_user_by_username(db: AsyncSession, username: str) -> Optional[models.User]:
        try:
            logger.info(f"查询用户名: {username}")
            # 修改查询，添加deleted=False条件，只返回未删除的用户
            result = await db.execute(
                select(models.User).filter(
                    and_(
                        models.User.username == username,
                        models.User.deleted == False  # 确保只返回未被删除的用户
                    )
                )
            )
            user = result.scalars().first()
            if user:
                logger.info(f"找到用户: ID={user.id}, Username={user.username}")
            else:
                logger.info(f"未找到用户名为 {username} 的用户，或用户已被删除")
            return user
        except Exception as e:
            logger.error(f"查询用户名时出错: {str(e)}")
            raise

    @staticmethod
    async def get_user_by_email(db: AsyncSession, email: str) -> Optional[models.User]:
        try:
            logger.info(f"查询邮箱: {email}")
            result = await db.execute(select(models.User).filter(models.User.email == email))
            user = result.scalars().first()
            if user:
                logger.info(f"找到用户: ID={user.id}, Email={user.email}")
            else:
                logger.info(f"未找到邮箱为 {email} 的用户")
            return user
        except Exception as e:
            logger.error(f"查询邮箱时出错: {str(e)}")
            raise

    @staticmethod
    async def create_user(db: AsyncSession, user: schemas.UserCreate) -> models.User:
        try:
            logger.info(f"创建用户: Username={user.username}, Email={user.email}")

            # 验证邮箱验证码
            is_code_valid = await UserService.verify_register_email_code(
                db=db,
                email=user.email,
                verification_code=user.email_verification_code
            )

            if not is_code_valid:
                logger.warning(f"邮箱验证码无效: {user.email}")
                raise ValueError("邮箱验证码无效或已过期")

            # 创建新用户（验证码已通过独立表验证）
            hashed_password = UserService.get_password_hash(user.password)

            db_user = models.User(
                username=user.username,
                email=user.email,
                password_hash=hashed_password,
                phone=user.phone,
                email_verified=True,  # 注册时已验证邮箱
                email_verification_code=None,
                email_verification_expires=None
            )
            db.add(db_user)

            await db.commit()
            await db.refresh(db_user)

            logger.info(f"用户创建成功: ID={db_user.id}")
            return db_user
        except Exception as e:
            await db.rollback()
            logger.error(f"创建用户时出错: {str(e)}")
            raise

    @staticmethod
    def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
        return encoded_jwt 

    @staticmethod
    async def update_user_password(db: AsyncSession, user_id: int, new_password: str) -> bool:
        try:
            logger.info(f"更新用户密码: User ID={user_id}")
            result = await db.execute(select(models.User).filter(models.User.id == user_id))
            user = result.scalars().first()
            
            if not user:
                logger.warning(f"未找到用户: ID={user_id}")
                return False
                
            # 生成新密码的哈希值
            hashed_password = UserService.get_password_hash(new_password)
            user.password_hash = hashed_password
            
            await db.commit()
            logger.info(f"密码更新成功: User ID={user_id}")
            return True
        except Exception as e:
            await db.rollback()
            logger.error(f"更新密码时出错: {str(e)}")
            raise 

    @staticmethod
    async def update_user_info(db: AsyncSession, user_id: int, user_data: dict) -> Optional[models.User]:
        try:
            logger.info(f"更新用户信息: User ID={user_id}")
            result = await db.execute(select(models.User).filter(models.User.id == user_id))
            user = result.scalars().first()
            
            if not user:
                logger.warning(f"未找到用户: ID={user_id}")
                return None
                
            # 更新用户信息
            # 只更新允许的字段
            allowed_fields = ["username", "email", "phone"]
            for field in allowed_fields:
                if field in user_data:
                    setattr(user, field, user_data[field])
            
            await db.commit()
            await db.refresh(user)
            logger.info(f"用户信息更新成功: User ID={user_id}")
            return user
        except Exception as e:
            await db.rollback()
            logger.error(f"更新用户信息时出错: {str(e)}")
            raise

    @staticmethod
    async def verify_email(db: AsyncSession, email: str, verification_code: str) -> bool:
        """验证邮箱"""
        try:
            logger.info(f"验证邮箱: {email}")
            result = await db.execute(select(models.User).filter(models.User.email == email))
            user = result.scalars().first()

            if not user:
                logger.warning(f"用户不存在: {email}")
                return False

            if user.email_verified:
                logger.info(f"邮箱已验证: {email}")
                return True

            # 检查验证码是否正确且未过期
            if (user.email_verification_code == verification_code and
                user.email_verification_expires and
                user.email_verification_expires > datetime.utcnow()):

                # 更新验证状态
                user.email_verified = True
                user.email_verification_code = None
                user.email_verification_expires = None

                await db.commit()
                logger.info(f"邮箱验证成功: {email}")
                return True
            else:
                logger.warning(f"验证码无效或已过期: {email}")
                return False

        except Exception as e:
            await db.rollback()
            logger.error(f"验证邮箱时出错: {str(e)}")
            raise

    @staticmethod
    async def resend_verification_email(db: AsyncSession, email: str) -> bool:
        """重新发送验证邮件"""
        try:
            logger.info(f"重新发送验证邮件: {email}")
            result = await db.execute(select(models.User).filter(models.User.email == email))
            user = result.scalars().first()

            if not user:
                logger.warning(f"用户不存在: {email}")
                return False

            if user.email_verified:
                logger.info(f"邮箱已验证，无需重发: {email}")
                return True

            # 生成新的验证码
            verification_code = email_service.generate_verification_code()
            verification_expires = email_service.get_verification_expiry()

            user.email_verification_code = verification_code
            user.email_verification_expires = verification_expires

            await db.commit()

            # 发送验证邮件
            success = await email_service.send_verification_email(
                to_email=email,
                username=user.username,
                verification_code=verification_code
            )

            if success:
                logger.info(f"验证邮件重发成功: {email}")
            else:
                logger.error(f"验证邮件重发失败: {email}")

            return success

        except Exception as e:
            await db.rollback()
            logger.error(f"重发验证邮件时出错: {str(e)}")
            raise

    @staticmethod
    async def request_password_reset(db: AsyncSession, email: str) -> bool:
        """请求密码重置"""
        try:
            logger.info(f"请求密码重置: {email}")
            result = await db.execute(select(models.User).filter(models.User.email == email))
            user = result.scalars().first()

            if not user:
                logger.warning(f"用户不存在: {email}")
                # 为了安全，即使用户不存在也返回True，不暴露用户是否存在
                return True

            # 生成密码重置码
            reset_code = email_service.generate_verification_code()
            reset_expires = email_service.get_verification_expiry()

            user.password_reset_code = reset_code
            user.password_reset_expires = reset_expires

            await db.commit()

            # 发送密码重置邮件
            success = await email_service.send_password_reset_email(
                to_email=email,
                username=user.username,
                reset_code=reset_code
            )

            if success:
                logger.info(f"密码重置邮件发送成功: {email}")
            else:
                logger.error(f"密码重置邮件发送失败: {email}")

            return success

        except Exception as e:
            await db.rollback()
            logger.error(f"请求密码重置时出错: {str(e)}")
            raise

    @staticmethod
    async def reset_password_with_code(db: AsyncSession, email: str, reset_code: str, new_password: str) -> bool:
        """使用重置码重置密码"""
        try:
            logger.info(f"重置密码: {email}")
            result = await db.execute(select(models.User).filter(models.User.email == email))
            user = result.scalars().first()

            if not user:
                logger.warning(f"用户不存在: {email}")
                return False

            # 检查重置码是否正确且未过期
            if (user.password_reset_code == reset_code and
                user.password_reset_expires and
                user.password_reset_expires > datetime.utcnow()):

                # 更新密码
                user.password_hash = UserService.get_password_hash(new_password)
                user.password_reset_code = None
                user.password_reset_expires = None

                await db.commit()
                logger.info(f"密码重置成功: {email}")
                return True
            else:
                logger.warning(f"重置码无效或已过期: {email}")
                return False

        except Exception as e:
            await db.rollback()
            logger.error(f"重置密码时出错: {str(e)}")
            raise

    @staticmethod
    async def send_register_verification_code(db: AsyncSession, email: str) -> bool:
        """发送注册验证码"""
        try:
            logger.info(f"发送注册验证码: {email}")

            # 检查邮箱是否已被注册（只检查正式用户，不包括临时记录）
            existing_user = await UserService.get_user_by_email(db, email)
            if existing_user and existing_user.username != "":  # 只有正式用户才算已注册
                logger.warning(f"邮箱已被注册: {email}")
                return False

            # 使用新的验证码服务
            verification_service = VerificationService()
            success = await verification_service.send_verification_code(
                db=db,
                email=email,
                verification_type="register"
            )

            return success

        except Exception as e:
            logger.error(f"发送注册验证码时出错: {str(e)}")
            await db.rollback()
            raise

    @staticmethod
    async def verify_register_email_code(db: AsyncSession, email: str, verification_code: str) -> bool:
        """验证注册邮箱验证码"""
        try:
            logger.info(f"验证注册邮箱验证码: {email}")

            # 使用新的验证码服务
            verification_service = VerificationService()
            is_valid = await verification_service.verify_code(
                db=db,
                email=email,
                code=verification_code,
                verification_type="register"
            )

            if is_valid:
                logger.info(f"注册邮箱验证码验证成功: {email}")
            else:
                logger.warning(f"邮箱验证码无效: {email}")

            return is_valid

        except Exception as e:
            logger.error(f"验证注册邮箱验证码时出错: {str(e)}")
            raise


    @staticmethod
    async def get_user_settings(db: AsyncSession, user_id: int) -> Optional[models.UserSettings]:
        try:
            logger.info(f"获取用户设置: User ID={user_id}")
            result = await db.execute(select(models.UserSettings).filter(models.UserSettings.user_id == user_id))
            settings = result.scalars().first()
            
            if not settings:
                logger.info(f"未找到用户设置，创建默认设置: User ID={user_id}")
                # 如果没有找到设置，创建默认设置
                settings = models.UserSettings(user_id=user_id)
                db.add(settings)
                await db.commit()
                await db.refresh(settings)
                
            return settings
        except Exception as e:
            logger.error(f"获取用户设置时出错: {str(e)}")
            raise
            
    @staticmethod
    async def update_user_settings(db: AsyncSession, user_id: int, settings_data: dict) -> Optional[models.UserSettings]:
        try:
            logger.info(f"更新用户设置: User ID={user_id}")
            
            # 先获取用户设置，如果不存在则创建
            settings = await UserService.get_user_settings(db, user_id)
            if not settings:
                logger.warning(f"未找到用户设置: User ID={user_id}")
                return None
            
            # 更新设置
            allowed_fields = ["language", "currency", "dark_mode", "notifications", "auto_backup"]
            for field in allowed_fields:
                if field in settings_data:
                    setattr(settings, field, settings_data[field])
            
            await db.commit()
            await db.refresh(settings)
            logger.info(f"用户设置更新成功: User ID={user_id}")
            return settings
        except Exception as e:
            await db.rollback()
            logger.error(f"更新用户设置时出错: {str(e)}")
            raise
            
    @staticmethod
    async def update_backup_time(db: AsyncSession, user_id: int) -> Optional[models.UserSettings]:
        try:
            logger.info(f"更新备份时间: User ID={user_id}")
            
            # 先获取用户设置，如果不存在则创建
            settings = await UserService.get_user_settings(db, user_id)
            if not settings:
                logger.warning(f"未找到用户设置: User ID={user_id}")
                return None
            
            # 更新备份时间
            settings.last_backup = datetime.utcnow()
            
            await db.commit()
            await db.refresh(settings)
            logger.info(f"备份时间更新成功: User ID={user_id}")
            return settings
        except Exception as e:
            await db.rollback()
            logger.error(f"更新备份时间时出错: {str(e)}")
            raise 