<!-- 注册页面 -->
<view class="register-page">
  <view class="register-header">
    <view class="title">创建账号</view>
    <view class="subtitle">加入个人记账，开始管理您的财务</view>
  </view>
  
  <view class="form-section">
    <view class="input-row">
      <text class="input-label">用户名 *</text>
      <input
        class="form-input"
        placeholder="请输入用户名"
        value="{{form.username}}"
        bindinput="onUsernameInput"
      />
    </view>
    
    <view class="input-row">
      <text class="input-label">邮箱 *</text>
      <input
        class="form-input"
        placeholder="请输入邮箱"
        value="{{form.email}}"
        bindinput="onEmailInput"
      />
    </view>

    <view class="input-row">
      <text class="input-label">邮箱验证码 *</text>
      <view class="verification-input">
        <input
          class="form-input verification-code"
          placeholder="请输入验证码"
          value="{{form.verificationCode}}"
          bindinput="onVerificationCodeInput"
          maxlength="6"
        />
        <button
          class="send-code-btn {{codeCountdown > 0 ? 'disabled' : ''}}"
          bindtap="sendVerificationCode"
          disabled="{{codeCountdown > 0 || codeSending}}"
        >
          {{codeCountdown > 0 ? codeCountdown + 's' : (codeSending ? '发送中...' : '发送验证码')}}
        </button>
      </view>
    </view>

    <view class="input-row">
      <text class="input-label">密码 *</text>
      <input
        class="form-input"
        placeholder="请输入密码"
        value="{{form.password}}"
        password="{{!showPassword}}"
        bindinput="onPasswordInput"
      />
      <text class="password-toggle" bindtap="togglePassword">
        {{showPassword ? '隐藏' : '显示'}}
      </text>
    </view>
  </view>
  
  <view class="register-actions">
    <button class="btn-primary" bindtap="handleRegister" disabled="{{registerLoading}}">
      {{registerLoading ? '注册中...' : '注册'}}
    </button>
  </view>
  
  <view class="register-footer">
    <text class="link-btn" bindtap="goToLogin">
      已有账号？立即登录
    </text>
  </view>
</view>
