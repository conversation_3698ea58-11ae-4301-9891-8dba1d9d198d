// pages/transactions/list.js
const api = require('../../utils/api.js')

Page({
  data: {
    transactions: [],
    groupedTransactions: [],
    startDate: '',
    endDate: '',
    totalIncome: '0.00',
    totalExpense: '0.00',
    netAmount: 0,
    netAmountText: '¥0.00',
    loading: false,
    page: 1,
    hasMore: true,
    hideAmounts: false
  },

  onLoad() {
    console.log('交易记录页面加载')
    this.loadHideAmountsState()
    this.initDateRange()
    this.loadTransactions()
  },

  onShow() {
    // 从其他页面返回时刷新数据
    this.loadTransactions(true) // 传入true进行完整刷新，避免重复数据
  },

  onPullDownRefresh() {
    this.loadTransactions(true)
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreTransactions()
    }
  },

  // 初始化日期范围（默认当月）
  initDateRange() {
    const now = new Date()
    const year = now.getFullYear()
    const month = now.getMonth()

    const startDate = new Date(year, month, 1)
    const endDate = new Date(year, month + 1, 0)

    this.setData({
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    })
  },

  // 加载交易记录
  async loadTransactions(refresh = false) {
    if (this.data.loading) return

    this.setData({ loading: true })

    if (refresh) {
      this.setData({
        page: 1,
        transactions: [],
        hasMore: true
      })
    }

    try {
      const params = {
        page: refresh ? 1 : this.data.page,
        limit: 20,
        start_date: this.data.startDate,
        end_date: this.data.endDate
      }

      const response = await api.transactions.getList(params)

      // 后端直接返回交易数组，不是包装对象
      const transactionList = Array.isArray(response) ? response : []

      const newTransactions = refresh ?
        transactionList :
        [...this.data.transactions, ...transactionList]

      // 计算收入和支出总额
      let totalIncome = 0
      let totalExpense = 0

      newTransactions.forEach(transaction => {
        const amount = parseFloat(transaction.amount) || 0
        if (transaction.type === 'income') {
          totalIncome += amount
        } else if (transaction.type === 'expense') {
          totalExpense += amount
        }
      })

      this.setData({
        transactions: newTransactions,
        totalIncome: this.formatAmount(totalIncome),
        totalExpense: this.formatAmount(totalExpense),
        netAmount: totalIncome - totalExpense,
        hasMore: transactionList.length >= 20, // 如果返回的数据等于limit，可能还有更多
        page: refresh ? 2 : this.data.page + 1
      })

      // 计算净额文本
      const netAmount = this.data.netAmount
      this.setData({
        netAmountText: netAmount >= 0 ?
          `+¥${this.formatAmount(netAmount)}` :
          `-¥${this.formatAmount(Math.abs(netAmount))}`
      })

      // 按日期分组
      this.groupTransactionsByDate(newTransactions)

    } catch (error) {
      console.error('加载交易记录失败:', error)

      if (!error.message.includes('登录已过期')) {
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    } finally {
      this.setData({ loading: false })
      wx.stopPullDownRefresh()
    }
  },

  // 加载更多交易
  async loadMoreTransactions() {
    await this.loadTransactions(false)
  },

  // 按日期分组交易
  groupTransactionsByDate(transactions) {
    const groups = {}

    // 确保transactions是数组
    if (!Array.isArray(transactions)) {
      console.warn('transactions不是数组:', transactions)
      this.setData({ groupedTransactions: [] })
      return
    }

    transactions.forEach(transaction => {
      // 使用transaction.date或created_at字段
      const dateStr = transaction.date || transaction.created_at
      const date = new Date(dateStr)
      const dateKey = date.toLocaleDateString('zh-CN')

      if (!groups[dateKey]) {
        groups[dateKey] = {
          date: dateKey,
          transactions: [],
          totalIncome: 0,
          totalExpense: 0,
          netAmount: 0
        }
      }

      // 处理分类显示
      let categoryDisplay = transaction.category_name || transaction.category
      if (!categoryDisplay || categoryDisplay === 'null') {
        // 如果没有分类，根据交易类型设置默认分类
        if (transaction.type === 'transfer') {
          categoryDisplay = '转账'
        } else if (transaction.type === 'income') {
          categoryDisplay = '收入'
        } else if (transaction.type === 'expense') {
          categoryDisplay = '支出'
        } else {
          categoryDisplay = '未分类'
        }
      }

      const formattedTransaction = {
        id: transaction.id,
        type: transaction.type, // 后端返回的字段名是type
        icon: this.getTransactionIcon(categoryDisplay, transaction.type),
        description: transaction.description,
        category: categoryDisplay,
        account: transaction.account_name,
        amountText: this.formatTransactionAmount(transaction.amount, transaction.type),
        time: date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      }

      groups[dateKey].transactions.push(formattedTransaction)

      const amount = parseFloat(transaction.amount) || 0
      if (transaction.type === 'income') {
        groups[dateKey].totalIncome += amount
      } else if (transaction.type === 'expense') {
        groups[dateKey].totalExpense += amount
      }
    })

    // 计算每组的净额并格式化
    const groupedTransactions = Object.values(groups).map(group => {
      group.netAmount = group.totalIncome - group.totalExpense
      group.netAmountText = group.netAmount >= 0 ?
        `+¥${this.formatAmount(group.netAmount)}` :
        `-¥${this.formatAmount(Math.abs(group.netAmount))}`
      return group
    })

    // 按日期倒序排列
    groupedTransactions.sort((a, b) => new Date(b.date) - new Date(a.date))

    this.setData({ groupedTransactions })
  },

  // 格式化金额
  formatAmount(amount) {
    return parseFloat(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  },

  // 格式化交易金额
  formatTransactionAmount(amount, type) {
    const formattedAmount = this.formatAmount(Math.abs(amount))
    return type === 'income' ? `+¥${formattedAmount}` : `-¥${formattedAmount}`
  },

  // 获取交易图标
  getTransactionIcon(categoryName, type) {
    const iconMap = {
      '餐饮美食': '🍽️',
      '交通出行': '🚗',
      '购物消费': '🛍️',
      '生活服务': '🏠',
      '医疗健康': '🏥',
      '教育培训': '📚',
      '娱乐休闲': '🎮',
      '工资收入': '💰',
      '投资收益': '📈',
      '其他收入': '💵',
      '转账': '🔄'
    }

    return iconMap[categoryName] || (type === 'income' ? '💰' : '💸')
  },

  // 日期选择事件
  onStartDateChange(e) {
    this.setData({
      startDate: e.detail.value
    })
    this.loadTransactions(true)
  },

  onEndDateChange(e) {
    this.setData({
      endDate: e.detail.value
    })
    this.loadTransactions(true)
  },

  // 显示筛选弹窗
  showFilterModal() {
    wx.showToast({
      title: '筛选功能开发中',
      icon: 'none'
    })
  },

  // 添加交易
  addTransaction() {
    wx.navigateTo({
      url: '/pages/transactions/add'
    })
  },

  // 加载隐藏金额状态
  loadHideAmountsState() {
    try {
      const hideAmounts = wx.getStorageSync('hideAmounts')
      this.setData({
        hideAmounts: hideAmounts === 'true' || hideAmounts === true
      })
    } catch (error) {
      console.error('加载隐藏金额状态失败:', error)
    }
  },

  // 切换金额显示/隐藏
  toggleAmountVisibility() {
    const newHideState = !this.data.hideAmounts
    this.setData({
      hideAmounts: newHideState
    })

    // 保存状态到本地存储
    try {
      wx.setStorageSync('hideAmounts', newHideState)
    } catch (error) {
      console.error('保存隐藏金额状态失败:', error)
    }
  }
})
