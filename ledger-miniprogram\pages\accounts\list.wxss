/* pages/accounts/list.wxss */
.accounts-page {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding-bottom: 120rpx;
}

.total-assets-card {
  background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
  margin: 32rpx;
  border-radius: 20rpx;
  padding: 40rpx;
  color: white;
}

.assets-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.assets-title-row {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
}

.assets-title {
  font-size: 30rpx;
  opacity: 0.9;
  margin-right: 16rpx;
}

.eye-toggle {
  font-size: 32rpx;
  padding: 8rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  min-width: 48rpx;
  text-align: center;
}

.assets-amount {
  font-size: 72rpx;
  font-weight: bold;
}

.assets-breakdown {
  display: flex;
  justify-content: space-between;
}

.breakdown-item {
  text-align: center;
}

.breakdown-label {
  display: block;
  font-size: 26rpx;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.breakdown-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
}

.breakdown-value.debt {
  color: #FF3B30;
}

.accounts-list {
  padding: 0 32rpx;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.list-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

.account-count {
  font-size: 26rpx;
  color: #8E8E93;
}

/* 加载状态样式 */
.loading-container {

}

.loading-accounts {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
}

.loading-account-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.loading-account-item:last-child {
  border-bottom: none;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #F2F2F7;
  margin-right: 32rpx;
  animation: pulse 1.5s ease-in-out infinite;
}

.loading-content {
  flex: 1;
}

.loading-line {
  height: 24rpx;
  background-color: #F2F2F7;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  animation: pulse 1.5s ease-in-out infinite;
}

.loading-line.short {
  width: 60%;
}

.loading-line.long {
  width: 40%;
}

.loading-balance {
  width: 120rpx;
  height: 32rpx;
  background-color: #F2F2F7;
  border-radius: 16rpx;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
}

.empty-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #8E8E93;
  margin-bottom: 16rpx;
}

.empty-hint {
  display: block;
  font-size: 26rpx;
  color: #C7C7CC;
  margin-bottom: 40rpx;
}

.btn-primary {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.accounts-container {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
}

.account-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.account-item:last-child {
  border-bottom: none;
}

.account-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #F2F2F7;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
}

.icon-text {
  font-size: 40rpx;
}

.account-info {
  flex: 1;
}

.account-name {
  display: block;
  font-size: 34rpx;
  color: #000000;
  margin-bottom: 4rpx;
}

.account-type {
  display: block;
  font-size: 26rpx;
  color: #8E8E93;
  margin-bottom: 4rpx;
}

.account-institution {
  display: block;
  font-size: 24rpx;
  color: #C7C7CC;
}

.account-balance {
  display: flex;
  align-items: center;
}

.balance-amount {
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
  margin-right: 16rpx;
}

.balance-arrow {
  font-size: 32rpx;
  color: #C7C7CC;
  font-weight: 500;
}

.fab {
  position: fixed;
  bottom: 120rpx;
  right: 32rpx;
  width: 112rpx;
  height: 112rpx;
  background-color: #007AFF;
  border-radius: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);
  z-index: 1000;
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: 300;
}
