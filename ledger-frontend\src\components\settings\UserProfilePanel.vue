<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElForm, ElFormItem, ElInput, ElButton, ElMessage, ElAlert } from 'element-plus'
import { updateUserInfo as apiUpdateUserInfo } from '../../api/user'
import type { UserInfo } from '../../api/types'

const props = defineProps<{
  userInfo: UserInfo
}>()

const emit = defineEmits(['profile-updated'])

// 表单数据
const userForm = reactive({
  username: props.userInfo.username || '',
  email: props.userInfo.email || '',
  phone: props.userInfo.phone || ''
})

// 检查是否有短用户名
const hasShortUsername = computed(() => {
  return props.userInfo.username.length < 3
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 组件状态
const isLoading = ref(false)
const formRef = ref()

// 更新用户信息
const updateUserInfo = async () => {
  if (!formRef.value) return
  
  try {
    // 表单验证
    await formRef.value.validate()
    
    isLoading.value = true
    
    // 调用API更新用户信息
    const updatedInfo = await apiUpdateUserInfo({
      username: userForm.username,
      email: userForm.email,
      phone: userForm.phone
    })
    
    ElMessage({
      type: 'success',
      message: '个人信息已更新'
    })
    
    // 通知父组件信息已更新
    emit('profile-updated', updatedInfo)
  } catch (error) {
    console.error('更新用户信息失败', error)
    ElMessage({
      type: 'error',
      message: '更新用户信息失败，请检查表单填写是否正确'
    })
  } finally {
    isLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    userForm.username = props.userInfo.username || ''
    userForm.email = props.userInfo.email || ''
    userForm.phone = props.userInfo.phone || ''
    formRef.value.resetFields()
  }
}
</script>

<template>
  <div class="user-profile-panel">
    <!-- 短用户名提示 -->
    <el-alert
      v-if="hasShortUsername"
      title="用户名需要更新"
      type="warning"
      :closable="false"
      show-icon
    >
      <p>您当前的用户名长度不符合最新规则要求（3-20个字符）。</p>
      <p>请更新您的用户名以继续使用所有功能。</p>
    </el-alert>
    
    <el-form 
      ref="formRef"
      :model="userForm" 
      :rules="rules"
      label-position="top"
    >
      <el-form-item label="用户名" prop="username">
        <el-input v-model="userForm.username" placeholder="请输入用户名(3-20个字符)" />
      </el-form-item>
      
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="userForm.email" type="email" />
      </el-form-item>
      
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="userForm.phone" />
      </el-form-item>
      
      <el-form-item>
        <div class="form-actions">
          <el-button type="primary" @click="updateUserInfo" :loading="isLoading">
            保存
          </el-button>
          <el-button @click="resetForm">
            重置
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.user-profile-panel {
  width: 100%;
}

.form-actions {
  display: flex;
  gap: 10px;

  @media (max-width: 768px) {
    gap: 12px;

    .el-button {
      flex: 1;
      height: 44px;
      font-size: 16px;
      border-radius: 8px;
      margin: 0;
    }
  }
}

.el-alert {
  margin-bottom: 20px;
}
</style> 