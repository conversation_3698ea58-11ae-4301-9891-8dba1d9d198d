<!-- 账户详情页面 -->
<view class="account-detail-page">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-left" bindtap="goBack">
      <text class="back-icon">‹</text>
      <text class="back-text">返回</text>
    </view>
    <text class="page-title">账户详情</text>
    <view class="header-right" bindtap="editAccount">
      <text class="edit-text">编辑</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 账户信息 -->
  <view wx:else class="account-content">
    <!-- 账户卡片 -->
    <view class="account-card">
      <view class="account-header">
        <view class="account-icon-large">
          <text class="icon-text">{{account.icon}}</text>
        </view>
        <view class="account-main-info">
          <text class="account-name">{{account.account_name}}</text>
          <text class="account-type">{{account.type_display}}</text>
          <text wx:if="{{account.institution}}" class="account-institution">{{account.institution}}</text>
        </view>
      </view>
      
      <view class="account-balance-section">
        <view class="balance-header">
          <text class="balance-label">当前余额</text>
          <text class="eye-toggle" bindtap="toggleAmountVisibility">{{hideAmounts ? '👁️' : '🙈'}}</text>
        </view>
        <text class="balance-amount">{{hideAmounts ? '****' : '¥' + account.balanceText}}</text>
      </view>

      <!-- 账户详细信息 -->
      <view class="account-details">
        <view class="detail-item" wx:if="{{account.account_number}}">
          <text class="detail-label">账户号码</text>
          <text class="detail-value">{{account.account_number}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">货币类型</text>
          <text class="detail-value">{{account.currency}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">创建时间</text>
          <text class="detail-value">{{account.created_at_text}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">最后更新</text>
          <text class="detail-value">{{account.updated_at_text}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn primary" bindtap="addTransaction">
        <text class="btn-icon">💰</text>
        <text class="btn-text">记一笔</text>
      </button>
      <button class="action-btn secondary" bindtap="viewTransactions">
        <text class="btn-icon">📋</text>
        <text class="btn-text">查看交易</text>
      </button>
    </view>

    <!-- 最近交易 -->
    <view class="recent-transactions">
      <view class="section-header">
        <text class="section-title">最近交易</text>
        <text class="more-btn" bindtap="viewAllTransactions">查看全部</text>
      </view>

      <!-- 交易列表 -->
      <view wx:if="{{recentTransactions.length > 0}}" class="transactions-list">
        <view class="transaction-item" wx:for="{{recentTransactions}}" wx:key="id">
          <view class="transaction-icon">
            <text class="icon-text">{{item.icon}}</text>
          </view>
          <view class="transaction-info">
            <text class="transaction-desc">{{item.description}}</text>
            <text class="transaction-category">{{item.category}}</text>
          </view>
          <view class="transaction-right">
            <text class="transaction-amount {{item.type}}">{{item.amountText}}</text>
            <text class="transaction-date">{{item.date}}</text>
          </view>
        </view>
      </view>

      <!-- 无交易记录 -->
      <view wx:else class="no-transactions">
        <text class="no-transactions-icon">📝</text>
        <text class="no-transactions-text">暂无交易记录</text>
        <text class="no-transactions-hint">点击"记一笔"开始使用此账户</text>
      </view>
    </view>

    <!-- 危险操作区域 -->
    <view class="danger-zone">
      <button class="danger-btn" bindtap="deleteAccount">
        <text class="danger-icon">🗑️</text>
        <text class="danger-text">删除账户</text>
      </button>
    </view>
  </view>
</view>
