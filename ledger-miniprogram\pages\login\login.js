// pages/login/login.js
const api = require('../../utils/api.js')

Page({
  data: {
    form: {
      username: '',
      password: ''
    },
    errors: {
      username: '',
      password: ''
    },
    showPassword: false,
    loginLoading: false
  },

  async onLoad() {
    console.log('登录页面加载')

    // 检查是否已登录
    const token = wx.getStorageSync('token')
    if (token) {
      wx.switchTab({
        url: '/pages/index/index'
      })
      return
    }


  },

  // 用户名输入
  onUsernameInput(e) {
    this.setData({
      'form.username': e.detail.value,
      'errors.username': ''
    })
  },

  // 密码输入
  onPasswordInput(e) {
    this.setData({
      'form.password': e.detail.value,
      'errors.password': ''
    })
  },

  // 切换密码显示
  togglePassword() {
    this.setData({
      showPassword: !this.data.showPassword
    })
  },

  // 表单验证
  validateForm() {
    const { username, password } = this.data.form
    const errors = {}
    
    if (!username.trim()) {
      errors.username = '请输入用户名'
    }
    
    if (!password.trim()) {
      errors.password = '请输入密码'
    } else if (password.length < 6) {
      errors.password = '密码长度不能少于6位'
    }
    
    this.setData({ errors })
    return Object.keys(errors).length === 0
  },

  // 处理登录
  async handleLogin() {
    if (!this.validateForm()) {
      return
    }

    this.setData({ loginLoading: true })

    try {
      const { username, password } = this.data.form

      // 调用真实的登录API
      const response = await api.auth.login(username, password)

      // 保存token和用户信息
      wx.setStorageSync('token', response.access_token)
      wx.setStorageSync('userInfo', JSON.stringify(response.user))

      // 更新全局用户信息
      const app = getApp()
      app.updateGlobalUserInfo(response.user)

      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })

      // 跳转到首页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        })
      }, 1500)

    } catch (error) {
      console.error('登录失败:', error)

      // 显示具体错误信息
      let errorMessage = '登录失败'
      if (error.message.includes('网络')) {
        errorMessage = '网络连接失败，请检查网络设置'
      } else if (error.message.includes('用户名') || error.message.includes('密码')) {
        errorMessage = '用户名或密码错误'
      } else if (error.message) {
        errorMessage = error.message
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      })
    } finally {
      this.setData({ loginLoading: false })
    }
  },

  // 微信登录
  handleWechatLogin() {
    wx.login({
      success: (res) => {
        console.log('微信登录code:', res.code)
        // 这里应该将code发送到后端进行验证
        wx.showToast({
          title: '微信登录功能开发中',
          icon: 'none'
        })
      },
      fail: (error) => {
        console.error('微信登录失败:', error)
        wx.showToast({
          title: '微信登录失败',
          icon: 'none'
        })
      }
    })
  },

  // 忘记密码
  handleForgotPassword() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 跳转到注册页
  goToRegister() {
    wx.navigateTo({
      url: '/pages/register/register'
    })
  }
})
